#!/usr/bin/env python3
"""
Test script for the PDF extraction API endpoint.
"""

import requests
import sys

def test_extract_endpoint():
    """Test the /extract endpoint with error handling for missing files."""
    base_url = "http://localhost:8080"
    
    # Test root endpoint first
    try:
        response = requests.get(f"{base_url}/")
        print(f"Root endpoint status: {response.status_code}")
        print(f"Root response: {response.json()}")
    except Exception as e:
        print(f"Error testing root endpoint: {e}")
        return False
    
    # Test extract endpoint with missing file (should return 400)
    try:
        # Test with no file
        response = requests.post(f"{base_url}/extract")
        print(f"Extract endpoint (no file) status: {response.status_code}")
        if response.status_code == 422:  # FastAPI validation error
            print("✅ Correctly rejected request with no file")
        else:
            print(f"Extract response: {response.text}")
    except Exception as e:
        print(f"Error testing extract endpoint: {e}")
    
    # Test extract endpoint with non-PDF file
    try:
        # Create a dummy text file to test validation
        with open("test.txt", "w") as f:
            f.write("This is not a PDF file")
        
        with open("test.txt", "rb") as f:
            files = {"file": ("test.txt", f, "text/plain")}
            response = requests.post(f"{base_url}/extract", files=files)
        
        print(f"Extract endpoint (non-PDF) status: {response.status_code}")
        if response.status_code == 400:
            print("✅ Correctly rejected non-PDF file")
            print(f"Response: {response.json()}")
        else:
            print(f"Extract response: {response.text}")
            
        # Clean up test file
        import os
        os.remove("test.txt")
        
    except Exception as e:
        print(f"Error testing extract endpoint with non-PDF: {e}")
    
    return True

if __name__ == "__main__":
    if test_extract_endpoint():
        print("✅ API extraction endpoint tests completed")
    else:
        print("❌ API extraction endpoint tests failed")
        sys.exit(1)
