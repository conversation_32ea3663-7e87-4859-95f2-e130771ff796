#!/usr/bin/env python3
"""
Test script to verify the Leistungsort field extraction functionality.
"""

import sys
from pathlib import Path
import json

# Add app directory to path
sys.path.append('app')
from pdf_extractor import parse_pdf_file, parse_pdf_content

def test_leistungsort_extraction():
    """Test the Leistungsort field extraction with the example PDF."""
    
    print("=== Testing Leistungsort Extraction ===\n")
    
    # Test with the example PDF file
    pdf_path = Path('data/pdfs/rbu_19436543_0010624394.pdf')
    
    if not pdf_path.exists():
        print(f"❌ Test PDF file not found: {pdf_path}")
        return False
    
    print(f"📄 Testing with PDF file: {pdf_path}")
    
    # Extract data from PDF
    result = parse_pdf_file(pdf_path)
    
    # Display results
    print("\n=== Extraction Results ===")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    # Verify the Leistungsort field
    leistungsort = result.get('leistungsort')
    expected_value = "37242 Bad Sooden-All"
    
    print(f"\n=== Leistungsort Field Verification ===")
    print(f"Extracted value: {leistungsort}")
    print(f"Expected value: {expected_value}")
    
    if leistungsort == expected_value:
        print("✅ Leistungsort extraction successful!")
        return True
    elif leistungsort is None:
        print("❌ Leistungsort field not found in extraction")
        return False
    else:
        print(f"⚠️  Leistungsort value mismatch. Got: '{leistungsort}', Expected: '{expected_value}'")
        return False

def test_leistungsort_regex_patterns():
    """Test the regex pattern with various input formats."""
    
    print("\n=== Testing Leistungsort Regex Patterns ===\n")
    
    import re
    from pdf_extractor import RX_LEISTUNGSORT
    
    test_cases = [
        ("Bestell Nr.: 4213235521/00010 Leistungsort: 37242 Bad Sooden-All Rechnung-Nr: (nicht gesetzt)", "37242 Bad Sooden-All"),
        ("Leistungsort: 12345 Berlin", "12345 Berlin"),
        ("Leistungsort:10115 Berlin-Mitte", "10115 Berlin-Mitte"),
        ("Leistungsort : 80331 München Rechnung-Nr: 123", "80331 München"),
        ("Leistungsort: 50667 Köln-Innenstadt Rechnung-Nr: ABC", "50667 Köln-Innenstadt"),
        ("Some text Leistungsort: 20095 Hamburg", "20095 Hamburg"),
        ("No Leistungsort in this text", None),
    ]
    
    all_passed = True
    
    for i, (test_input, expected) in enumerate(test_cases, 1):
        match = RX_LEISTUNGSORT.search(test_input)
        result = match.group(1) if match else None
        
        status = "✅" if result == expected else "❌"
        print(f"{status} Test {i}: '{test_input[:50]}...' -> '{result}'")
        
        if result != expected:
            print(f"   Expected: '{expected}'")
            all_passed = False
    
    return all_passed

if __name__ == "__main__":
    print("🧪 Leistungsort Extraction Test Suite\n")
    
    # Run tests
    pdf_test_passed = test_leistungsort_extraction()
    regex_test_passed = test_leistungsort_regex_patterns()
    
    print(f"\n=== Test Summary ===")
    print(f"PDF Extraction Test: {'✅ PASSED' if pdf_test_passed else '❌ FAILED'}")
    print(f"Regex Pattern Test: {'✅ PASSED' if regex_test_passed else '❌ FAILED'}")
    
    if pdf_test_passed and regex_test_passed:
        print("\n🎉 All tests passed! Leistungsort extraction is working correctly.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
        sys.exit(1)
