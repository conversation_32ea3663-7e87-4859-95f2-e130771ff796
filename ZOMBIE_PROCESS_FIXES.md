# Zombie Process Fixes Implementation

## Problem Summary
The FastAPI application was experiencing zombie process accumulation due to improper browser process management. Headless Chrome/Chromium processes spawned by <PERSON><PERSON> were not being properly cleaned up, leading to 50+ zombie processes.

## Root Cause Analysis
1. **Exception Handling**: Browser processes weren't cleaned up when exceptions occurred
2. **High Concurrency**: Multiple simultaneous requests overwhelmed the system
3. **Missing Signal Handlers**: No proper SIGCHLD handling to reap zombie children
4. **Lack of Process Monitoring**: No visibility into process health

## Implemented Fixes

### 1. Enhanced Browser Process Management (`app/playwright_job.py`)

**Before:**
```python
async def run_playwright(sheet_id: str) -> str:
    async with async_playwright() as pw:
        browser = await pw.chromium.launch(headless=True)
        # ... operations ...
        await browser.close()  # Only called if no exceptions
```

**After:**
```python
async def run_playwright(sheet_id: str) -> str:
    browser = None
    context = None
    
    try:
        async with async_playwright() as pw:
            browser = await pw.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            # ... operations ...
    finally:
        # Guaranteed cleanup in all cases
        if context:
            await context.close()
        if browser:
            await browser.close()
            await asyncio.sleep(0.1)  # Allow process cleanup
```

**Key Improvements:**
- Explicit try/finally blocks ensure cleanup even on exceptions
- Additional Chrome args for better process management
- Small delay after browser.close() for process cleanup
- Detailed logging for debugging

### 2. Signal Handlers & Process Management (`app/main.py`)

**Added:**
```python
def setup_signal_handlers():
    def signal_handler(signum, frame):
        # Graceful shutdown
        shutdown_event.set()
        # Reap zombie children
        while True:
            try:
                pid, status = os.waitpid(-1, os.WNOHANG)
                if pid == 0:
                    break
                logger.info(f"Reaped zombie process PID {pid}")
            except OSError:
                break
    
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    # Handle SIGCHLD to reap zombie children
    def sigchld_handler(signum, frame):
        try:
            while True:
                pid, status = os.waitpid(-1, os.WNOHANG)
                if pid == 0:
                    break
        except OSError:
            pass
    
    signal.signal(signal.SIGCHLD, sigchld_handler)
```

### 3. Concurrency Control

**Added semaphore-based limiting:**
```python
MAX_CONCURRENT_BROWSERS = 3
browser_semaphore = asyncio.Semaphore(MAX_CONCURRENT_BROWSERS)

@app.post("/export")
async def export(sheet_id: str = Form(...)):
    async with browser_semaphore:  # Limit concurrent operations
        # ... browser operations ...
```

### 4. Health Monitoring Endpoint

**New `/health` endpoint:**
```python
@app.get("/health")
async def health_check():
    zombie_count = 0
    headless_shell_count = 0
    
    for proc in psutil.process_iter(['pid', 'name', 'status']):
        if proc.info['status'] == psutil.STATUS_ZOMBIE:
            zombie_count += 1
        if 'headless_shell' in proc.info['name']:
            headless_shell_count += 1
    
    return {
        "status": "healthy",
        "zombie_processes": zombie_count,
        "headless_shell_processes": headless_shell_count,
        "concurrent_browser_slots_available": browser_semaphore._value
    }
```

### 5. Application Lifecycle Management

**Added FastAPI lifespan:**
```python
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    setup_signal_handlers()
    yield
    # Shutdown
    shutdown_event.set()

app = FastAPI(lifespan=lifespan)
```

## Dependencies Added
- `psutil>=5.9.0` for process monitoring

## Testing
- `test_zombie_fix.py` - Comprehensive test script
- Monitors process counts before/after operations
- Tests concurrent requests
- Validates health endpoint

## Monitoring Commands

**Check zombie processes:**
```bash
ps aux | grep '<defunct>' | wc -l
```

**Check headless_shell processes:**
```bash
pgrep -c headless_shell
```

**Health endpoint:**
```bash
curl http://localhost:8080/health
```

## Expected Results
1. **Zero zombie process accumulation** during normal operations
2. **Proper cleanup** even when exceptions occur
3. **Concurrency limits** prevent resource exhaustion
4. **Real-time monitoring** of process health
5. **Graceful shutdown** with proper cleanup

## Deployment Notes
- No changes needed to Docker configuration
- Signal handlers work properly in containers
- Health endpoint can be used for container health checks
- Concurrency limits prevent memory/CPU exhaustion

## Verification
After deployment, monitor:
1. Zombie process count should remain stable
2. Health endpoint should show low/zero zombie counts
3. Browser processes should be cleaned up after requests
4. Application should handle concurrent requests gracefully
