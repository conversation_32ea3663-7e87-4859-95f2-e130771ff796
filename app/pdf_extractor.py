import re
import datetime as dt
from pathlib import Path
from typing import Dict, Any, Optional
import pdfplumber

# Regular expressions for extracting data from German Telekom invoices
RX_BESTELL = re.compile(r"Bestell\s+Nr\.\s*:\s*([\d/]+)")
# Multiple patterns for service period (Leistungszeitraum)
RX_PERIOD = re.compile(r"Leistungszeitraum\s*:\s*(\d{2}\.\d{2}\.\d{4})\s*-\s*(\d{2}\.\d{2}\.\d{4})")
RX_PERIOD_BIS = re.compile(r"Leistungszeitraum\s*:\s*(\d{2}\.\d{2}\.\d{4})\s*bis\s*(\d{2}\.\d{2}\.\d{4})")
RX_PERIOD_ALT = re.compile(r"Leistungszeitraum\s*:\s*(\d{2}\.\d{2}\.\d{4})\s*(?:bis|-)?\s*(\d{2}\.\d{2}\.\d{4})")
RX_LEB_DATE = re.compile(r"LEB\-Erfassung\s+am:\s*(\d{2}\.\d{2}\.\d{4})")
RX_NETTO = re.compile(r"Summe\s+Netto\s+€\s*([\d\.,]+)")
# More specific LEB number patterns - try multiple variations
RX_LEB_NR = re.compile(r"LEB\s+Nr\.\s*:\s*(\d{10})")  # Primary: "LEB Nr.: 0010624394"
RX_LEB_NR_ALT = re.compile(r"LEB\s*[-\s]*(\d{10})")   # Alternative LEB pattern
RX_LEB_NR_GENERIC = re.compile(r"\bLEB\s+Nr\.\s*:\s*([\d/]+)")  # Fallback pattern
RX_AG_LEB_NR = re.compile(r"AG\s+LEB\s+Nr\.\s*:\s*([\d/]+)")
RX_STEUERNUMMER = re.compile(r"Steuernummer\s*:\s*([\d/]+)")
# Pattern for service location (Leistungsort)
RX_LEISTUNGSORT = re.compile(r"Leistungsort\s*:\s*([^\s]+(?:\s+[^\s]+)*?)(?:\s+Rechnung|$)")

def parse_pdf_content(pdf_content: bytes) -> Dict[str, Any]:
    """
    Parse PDF content and extract invoice data.

    Args:
        pdf_content: Binary PDF content

    Returns:
        Dictionary with extracted invoice data
    """
    try:
        # Open PDF from bytes using BytesIO
        import io
        pdf_stream = io.BytesIO(pdf_content)
        with pdfplumber.open(pdf_stream) as pdf:
            # Extract text from all pages
            pages = [page.extract_text() or "" for page in pdf.pages]
            raw = " ".join(pages)
            text = raw.replace("\n", " ")
        
        def find(rx) -> Optional[str]:
            """Helper function to find regex matches."""
            m = rx.search(text)
            return m.group(1) if m else None


        
        # Extract data using regex patterns
        bestell = find(RX_BESTELL)
        leistungsort = find(RX_LEISTUNGSORT)

        # Try multiple patterns for service period
        period_vals = RX_PERIOD.search(text) or RX_PERIOD_BIS.search(text) or RX_PERIOD_ALT.search(text)

        leb_date = find(RX_LEB_DATE)
        net_raw = find(RX_NETTO)

        # Try multiple patterns for LEB number - prioritize the correct pattern
        leb_nr = find(RX_LEB_NR) or find(RX_LEB_NR_ALT) or find(RX_LEB_NR_GENERIC)

        # Special handling: if we found a LEB number but it doesn't start with 00,
        # look for a 10-digit number that starts with 00 (more likely to be the correct LEB)
        if leb_nr and not leb_nr.startswith('00'):
            import re as re_fallback
            # Look for 10-digit numbers starting with 00 (common LEB pattern)
            leb_match = re_fallback.search(r'\b(00\d{8})\b', text)
            if leb_match:
                leb_nr = leb_match.group(1)
        elif not leb_nr:
            # If no LEB found at all, try the 00-pattern as fallback
            import re as re_fallback
            leb_match = re_fallback.search(r'\b(00\d{8})\b', text)
            if leb_match:
                leb_nr = leb_match.group(1)

        ag_leb_nr = find(RX_AG_LEB_NR)
        steuernr = find(RX_STEUERNUMMER)
        
        # Process period dates
        start_date = period_vals.group(1) if period_vals else None
        end_date = period_vals.group(2) if period_vals else None
        
        # Process net amount
        netto = 0.0
        if net_raw:
            try:
                netto = float(net_raw.replace(".", "").replace(",", "."))
            except ValueError:
                netto = 0.0
        
        # Return structured data
        return {
            "bestell_nr": bestell,
            "leistungsort": leistungsort,
            "startdatum": start_date,
            "slutdatum": end_date,  # End date (Swedish naming from original)
            "leb_erfassung_datum": leb_date,
            "summa_netto_eur": netto,  # Net sum in EUR
            "leb_nr": leb_nr,
            "ag_leb_nr": ag_leb_nr,
            "steuernummer": steuernr,
            "extraction_timestamp": dt.datetime.now().isoformat(),
            "success": True,
            "error": None
        }
        
    except Exception as e:
        return {
            "bestell_nr": None,
            "leistungsort": None,
            "startdatum": None,
            "slutdatum": None,
            "leb_erfassung_datum": None,
            "summa_netto_eur": 0.0,
            "leb_nr": None,
            "ag_leb_nr": None,
            "steuernummer": None,
            "extraction_timestamp": dt.datetime.now().isoformat(),
            "success": False,
            "error": str(e)
        }

def parse_pdf_file(file_path: Path) -> Dict[str, Any]:
    """
    Parse PDF file and extract invoice data.
    
    Args:
        file_path: Path to PDF file
        
    Returns:
        Dictionary with extracted invoice data
    """
    try:
        with open(file_path, 'rb') as f:
            pdf_content = f.read()
        return parse_pdf_content(pdf_content)
    except Exception as e:
        return {
            "bestell_nr": None,
            "leistungsort": None,
            "startdatum": None,
            "slutdatum": None,
            "leb_erfassung_datum": None,
            "summa_netto_eur": 0.0,
            "leb_nr": None,
            "ag_leb_nr": None,
            "steuernummer": None,
            "extraction_timestamp": dt.datetime.now().isoformat(),
            "success": False,
            "error": f"File error: {str(e)}"
        }
