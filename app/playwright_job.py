import os
import asyncio
import logging
from playwright.async_api import async_playwright, TimeoutError as PWTimeout
from .settings import USERNAME, PASSWORD

logger = logging.getLogger(__name__)

async def login(page):
    """Handle login to the Telekom portal."""
    await page.fill('#loginform-username', USERNAME)
    await page.fill('#loginform-password', PASSWORD)
    await page.click('#w0 > div:nth-child(4) > div > button')
    # Wait until the export link appears
    await page.wait_for_selector('#w0 > li:nth-child(5) > a', timeout=10_000)

async def run_playwright(sheet_id: str) -> str:
    """
    Navigate to sheet, handle login if needed, and download PDF.
    Returns the local path to the downloaded PDF.

    Implements robust browser process management to prevent zombie processes.
    """
    browser = None
    context = None

    try:
        async with async_playwright() as pw:
            # Launch browser with additional args for better process management
            browser = await pw.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )

            logger.info(f"Browser launched for sheet_id: {sheet_id}")

            # Check if state.json exists, if not create context without it
            storage_state = "state.json" if os.path.exists("state.json") else None
            context = await browser.new_context(storage_state=storage_state)
            page = await context.new_page()

            # 1. Go to sheet URL
            await page.goto(f"https://www.evergabe.telekom.de/sheet/header?c=1&sheetId={sheet_id}")

            # 2. Try clicking "Export" menu link, else login
            try:
                await page.click("#w0 > li:nth-child(5) > a", timeout=5_000)
            except PWTimeout:
                await login(page)
                await page.click("#w0 > li:nth-child(5) > a")
                # Save the session state after successful login
                await context.storage_state(path="state.json")

            # 3. Wait for and click export button
            await page.wait_for_selector("#export", state="visible", timeout=10_000)
            await page.click("#export")

            # 4. Wait for the PDF export button to be visible and clickable
            await page.wait_for_selector("#export-pdf", state="visible", timeout=10_000)

            # 5. Handle the download
            async with page.expect_download() as download_info:
                await page.click("#export-pdf")
            download = await download_info.value

            # 6. Get the original filename from the download
            original_filename = download.suggested_filename

            # 7. Save the download to our data directory with original filename
            os.makedirs("data/pdfs", exist_ok=True)
            download_path = f"data/pdfs/{original_filename}"
            await download.save_as(download_path)

            logger.info(f"PDF successfully downloaded: {download_path}")
            return download_path, original_filename

    except Exception as e:
        logger.error(f"Error in run_playwright for sheet_id {sheet_id}: {str(e)}")
        raise
    finally:
        # Ensure proper cleanup in all cases
        try:
            if context:
                await context.close()
                logger.debug("Browser context closed")
            if browser:
                await browser.close()
                logger.debug("Browser closed")
                # Give a small delay for process cleanup
                await asyncio.sleep(0.1)
        except Exception as cleanup_error:
            logger.error(f"Error during browser cleanup: {cleanup_error}")
            # Force kill browser processes if normal cleanup fails
            try:
                if browser:
                    await browser.close()
            except:
                pass
