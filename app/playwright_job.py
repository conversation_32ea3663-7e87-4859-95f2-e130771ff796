import os
from playwright.async_api import async_playwright, TimeoutError as PWTimeout
from .settings import USERNAME, PASSWORD

async def login(page):
    """Handle login to the Telekom portal."""
    await page.fill('#loginform-username', USERNAME)
    await page.fill('#loginform-password', PASSWORD)
    await page.click('#w0 > div:nth-child(4) > div > button')
    # Wait until the export link appears
    await page.wait_for_selector('#w0 > li:nth-child(5) > a', timeout=10_000)

async def run_playwright(sheet_id: str) -> str:
    """
    Navigate to sheet, handle login if needed, and download PDF.
    Returns the local path to the downloaded PDF.
    """
    async with async_playwright() as pw:
        browser = await pw.chromium.launch(headless=True)

        # Check if state.json exists, if not create context without it
        storage_state = "state.json" if os.path.exists("state.json") else None
        context = await browser.new_context(storage_state=storage_state)
        page = await context.new_page()

        # 1. Go to sheet URL
        await page.goto(f"https://www.evergabe.telekom.de/sheet/header?c=1&sheetId={sheet_id}")

        # 2. Try clicking "Export" menu link, else login
        try:
            await page.click("#w0 > li:nth-child(5) > a", timeout=5_000)
        except PWTimeout:
            await login(page)
            await page.click("#w0 > li:nth-child(5) > a")
            # Save the session state after successful login
            await context.storage_state(path="state.json")

        # 3. Wait for and click export button
        await page.wait_for_selector("#export", state="visible", timeout=10_000)
        await page.click("#export")

        # 4. Wait for the PDF export button to be visible and clickable
        await page.wait_for_selector("#export-pdf", state="visible", timeout=10_000)

        # 5. Handle the download
        async with page.expect_download() as download_info:
            await page.click("#export-pdf")
        download = await download_info.value

        # 6. Get the original filename from the download
        original_filename = download.suggested_filename

        # 7. Save the download to our data directory with original filename
        os.makedirs("data/pdfs", exist_ok=True)
        download_path = f"data/pdfs/{original_filename}"
        await download.save_as(download_path)

        # 8. Close browser and return both the path and original filename
        await browser.close()
        return download_path, original_filename
