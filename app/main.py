import logging
import signal
import sys
import os
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, Form, HTTPException, UploadFile, File
from fastapi.responses import FileResponse
from .playwright_job import run_playwright
from .storage import rotate_pdfs
from .pdf_extractor import parse_pdf_content

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global flag for graceful shutdown
shutdown_event = asyncio.Event()

def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown and zombie process cleanup."""
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        shutdown_event.set()

        # Clean up any remaining child processes
        try:
            # Reap any zombie children
            while True:
                try:
                    pid, status = os.waitpid(-1, os.WNOHANG)
                    if pid == 0:
                        break
                    logger.info(f"Reaped zombie process PID {pid}")
                except OSError:
                    break
        except Exception as e:
            logger.error(f"Error during signal cleanup: {e}")

        sys.exit(0)

    # Handle SIGTERM (Docker stop) and SIGINT (Ctrl+C)
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)

    # Handle SIGCHLD to reap zombie children
    def sigchld_handler(signum, frame):
        try:
            while True:
                pid, status = os.waitpid(-1, os.WNOHANG)
                if pid == 0:
                    break
                logger.debug(f"Reaped child process PID {pid}")
        except OSError:
            pass

    signal.signal(signal.SIGCHLD, sigchld_handler)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown."""
    # Startup
    logger.info("Starting Telekom RBU Downloader...")
    setup_signal_handlers()
    logger.info("Signal handlers configured")

    yield

    # Shutdown
    logger.info("Shutting down Telekom RBU Downloader...")
    shutdown_event.set()

app = FastAPI(
    title="Telekom RBU Downloader",
    version="1.0.0",
    lifespan=lifespan
)

# Semaphore to limit concurrent browser operations
MAX_CONCURRENT_BROWSERS = 3
browser_semaphore = asyncio.Semaphore(MAX_CONCURRENT_BROWSERS)

@app.get("/")
async def root():
    return {"message": "Telekom RBU Downloader API"}

@app.get("/health")
async def health_check():
    """Health check endpoint that monitors for zombie processes."""
    try:
        import psutil

        # Count zombie processes
        zombie_count = 0
        headless_shell_count = 0

        for proc in psutil.process_iter(['pid', 'name', 'status']):
            try:
                if proc.info['status'] == psutil.STATUS_ZOMBIE:
                    zombie_count += 1
                if 'headless_shell' in proc.info['name']:
                    headless_shell_count += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        return {
            "status": "healthy",
            "zombie_processes": zombie_count,
            "headless_shell_processes": headless_shell_count,
            "concurrent_browser_slots_available": browser_semaphore._value,
            "max_concurrent_browsers": MAX_CONCURRENT_BROWSERS
        }
    except ImportError:
        return {
            "status": "healthy",
            "note": "psutil not available for process monitoring",
            "concurrent_browser_slots_available": browser_semaphore._value,
            "max_concurrent_browsers": MAX_CONCURRENT_BROWSERS
        }

@app.post("/export", response_class=FileResponse)
async def export(sheet_id: str = Form(...)):
    """
    Export a sheet as PDF.

    Args:
        sheet_id: The ID of the sheet to export (form data)

    Returns:
        PDF file as binary response
    """
    # Use semaphore to limit concurrent browser operations
    async with browser_semaphore:
        try:
            logger.info(f"Starting export for sheet_id: {sheet_id} (slots available: {browser_semaphore._value})")

            # 1. Run the browser automation
            pdf_path, original_filename = await run_playwright(sheet_id)
            logger.info(f"PDF downloaded to: {pdf_path} with original filename: {original_filename}")

            # 2. Remove old PDFs
            rotate_pdfs()

            # 3. Return the PDF binary with original filename
            return FileResponse(
                path=pdf_path,
                media_type="application/pdf",
                filename=original_filename
            )
        except Exception as e:
            logger.error(f"Error exporting sheet {sheet_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Export failed: {str(e)}")

@app.post("/extract")
async def extract_pdf_data(file: UploadFile = File(...)):
    """
    Extract data from uploaded PDF invoice.

    Args:
        file: Uploaded PDF file

    Returns:
        JSON with extracted invoice data
    """
    try:
        logger.info(f"Starting PDF extraction for file: {file.filename}")

        # Validate file type
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="File must be a PDF")

        # Read file content
        pdf_content = await file.read()
        logger.info(f"Read {len(pdf_content)} bytes from uploaded file")

        # Extract data from PDF
        extracted_data = parse_pdf_content(pdf_content)
        logger.info(f"Extraction completed for {file.filename}: success={extracted_data.get('success', False)}")

        return extracted_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error extracting PDF data from {file.filename}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Extraction failed: {str(e)}")
