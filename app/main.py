import logging
from fastapi import <PERSON><PERSON><PERSON>, Form, HTTPException, UploadFile, File
from fastapi.responses import FileResponse
from .playwright_job import run_playwright
from .storage import rotate_pdfs
from .pdf_extractor import parse_pdf_content

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Telekom RBU Downloader", version="1.0.0")

@app.get("/")
async def root():
    return {"message": "Telekom RBU Downloader API"}

@app.post("/export", response_class=FileResponse)
async def export(sheet_id: str = Form(...)):
    """
    Export a sheet as PDF.

    Args:
        sheet_id: The ID of the sheet to export (form data)

    Returns:
        PDF file as binary response
    """
    try:
        logger.info(f"Starting export for sheet_id: {sheet_id}")

        # 1. Run the browser automation
        pdf_path, original_filename = await run_playwright(sheet_id)
        logger.info(f"PDF downloaded to: {pdf_path} with original filename: {original_filename}")

        # 2. Remove old PDFs
        rotate_pdfs()

        # 3. Return the PDF binary with original filename
        return FileResponse(
            path=pdf_path,
            media_type="application/pdf",
            filename=original_filename
        )
    except Exception as e:
        logger.error(f"Error exporting sheet {sheet_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Export failed: {str(e)}")

@app.post("/extract")
async def extract_pdf_data(file: UploadFile = File(...)):
    """
    Extract data from uploaded PDF invoice.

    Args:
        file: Uploaded PDF file

    Returns:
        JSON with extracted invoice data
    """
    try:
        logger.info(f"Starting PDF extraction for file: {file.filename}")

        # Validate file type
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="File must be a PDF")

        # Read file content
        pdf_content = await file.read()
        logger.info(f"Read {len(pdf_content)} bytes from uploaded file")

        # Extract data from PDF
        extracted_data = parse_pdf_content(pdf_content)
        logger.info(f"Extraction completed for {file.filename}: success={extracted_data.get('success', False)}")

        return extracted_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error extracting PDF data from {file.filename}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Extraction failed: {str(e)}")
