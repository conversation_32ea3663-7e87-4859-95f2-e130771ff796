Coolify
v4.0.0-beta.420.6

Current Team

Root Team
Dashboard
Projects
Servers
Sources
Destinations
S3 Storages
Shared Variables
Notifications
Keys & Tokens
Tags
Terminal
Profile
Teams
Settings
Sponsor us
Feedback
Logout
Deployment
Anesa
production
TelekomRBUDownloader
Running

Configuration
Deployments
Logs
Terminal

Links

Advanced
Redeploy
Restart
Stop
Deployment Log
Hide Debug Logs
Deployment is Failed.




2025-Aug-18 12:18:54.020876
Starting deployment of hakanhakan/TelekomRBUDownloader:master to localhost.
2025-Aug-18 12:18:54.211295
Preparing container with helper image: ghcr.io/coollabsio/coolify-helper:1.0.8.
2025-Aug-18 12:18:54.322395
[CMD]: docker stop --time=30 w0gwk0wcos0s8cswsck4c0g0
2025-Aug-18 12:18:54.322395
Flag --time has been deprecated, use --timeout instead
2025-Aug-18 12:18:54.326223
Error response from daemon: No such container: w0gwk0wcos0s8cswsck4c0g0
2025-Aug-18 12:18:54.436453
[CMD]: docker rm -f w0gwk0wcos0s8cswsck4c0g0
2025-Aug-18 12:18:54.436453
Error response from daemon: No such container: w0gwk0wcos0s8cswsck4c0g0
2025-Aug-18 12:18:54.598926
[CMD]: docker run -d --network coolify --name w0gwk0wcos0s8cswsck4c0g0 --rm -v /var/run/docker.sock:/var/run/docker.sock ghcr.io/coollabsio/coolify-helper:1.0.8
2025-Aug-18 12:18:54.598926
9125bae19c69bc91f8ab2c3fb6788650c576a812bb1cbf72217da4d04abcd383
2025-Aug-18 12:18:56.505829
[CMD]: docker exec w0gwk0wcos0s8cswsck4c0g0 bash -c 'GIT_SSH_COMMAND="ssh -o ConnectTimeout=30 -p 22 -o Port=22 -o LogLevel=ERROR -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git ls-remote https://x-access-token:<REDACTED>@github.com/hakanhakan/TelekomRBUDownloader.git master'
2025-Aug-18 12:18:56.505829
a6df31bd657e7ea5f67566f06dceec1411ceece2	refs/heads/master
2025-Aug-18 12:18:56.778715
----------------------------------------
2025-Aug-18 12:18:56.784089
Importing hakanhakan/TelekomRBUDownloader:master (commit sha HEAD) to /artifacts/w0gwk0wcos0s8cswsck4c0g0.
2025-Aug-18 12:18:56.997006
[CMD]: docker exec w0gwk0wcos0s8cswsck4c0g0 bash -c 'git clone -b "master" https://x-access-token:<REDACTED>@github.com/hakanhakan/TelekomRBUDownloader.git /artifacts/w0gwk0wcos0s8cswsck4c0g0 && cd /artifacts/w0gwk0wcos0s8cswsck4c0g0 && GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git submodule update --init --recursive && cd /artifacts/w0gwk0wcos0s8cswsck4c0g0 && GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git lfs pull'
2025-Aug-18 12:18:56.997006
Cloning into '/artifacts/w0gwk0wcos0s8cswsck4c0g0'...
2025-Aug-18 12:18:58.571245
[CMD]: docker exec w0gwk0wcos0s8cswsck4c0g0 bash -c 'cd /artifacts/w0gwk0wcos0s8cswsck4c0g0 && git log -1 a6df31bd657e7ea5f67566f06dceec1411ceece2 --pretty=%B'
2025-Aug-18 12:18:58.571245
fix: Update Dockerfile to handle Playwright installation issues in Debian Trixie
2025-Aug-18 12:18:58.571245
2025-Aug-18 12:18:58.571245
- Install Playwright browsers without --with-deps to avoid missing package errors
2025-Aug-18 12:18:58.571245
- Add comprehensive system dependencies for Chromium browser
2025-Aug-18 12:18:58.571245
- Use alternative font packages that exist in Debian Trixie
2025-Aug-18 12:18:58.571245
- Install only chromium browser instead of all browsers to reduce build time
2025-Aug-18 12:18:58.571245
- Add fallback package installation with || true for optional dependencies
2025-Aug-18 12:18:59.500767
[CMD]: docker exec w0gwk0wcos0s8cswsck4c0g0 bash -c 'cat /artifacts/w0gwk0wcos0s8cswsck4c0g0/Dockerfile'
2025-Aug-18 12:18:59.500767
FROM python:3.12-slim
2025-Aug-18 12:18:59.500767
2025-Aug-18 12:18:59.500767
# Telekom RBU Downloader - FastAPI service with Playwright automation and PDF extraction
2025-Aug-18 12:18:59.500767
# Features: PDF download from Telekom portal + PDF data extraction
2025-Aug-18 12:18:59.500767
2025-Aug-18 12:18:59.500767
# Install uv
2025-Aug-18 12:18:59.500767
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /usr/local/bin/
2025-Aug-18 12:18:59.500767
2025-Aug-18 12:18:59.500767
# System deps for Playwright and PDF processing
2025-Aug-18 12:18:59.500767
RUN apt-get update && apt-get install -y \
2025-Aug-18 12:18:59.500767
# Basic utilities
2025-Aug-18 12:18:59.500767
wget gnupg curl ca-certificates \
2025-Aug-18 12:18:59.500767
# Playwright dependencies
2025-Aug-18 12:18:59.500767
libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1 \
2025-Aug-18 12:18:59.500767
libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2 \
2025-Aug-18 12:18:59.500767
libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0 \
2025-Aug-18 12:18:59.500767
libwayland-server0 libxshmfence1 libegl1 \
2025-Aug-18 12:18:59.500767
# Additional Playwright dependencies for Chromium
2025-Aug-18 12:18:59.500767
libxss1 libgconf-2-4 libxtst6 libxrandr2 libasound2 libpangocairo-1.0-0 \
2025-Aug-18 12:18:59.500767
libatk1.0-0 libcairo-gobject2 libgtk-3-0 libgdk-pixbuf2.0-0 \
2025-Aug-18 12:18:59.500767
# Font packages (alternative names for missing packages)
2025-Aug-18 12:18:59.500767
fonts-liberation fonts-noto-color-emoji fonts-noto-cjk \
2025-Aug-18 12:18:59.500767
# PDF processing dependencies
2025-Aug-18 12:18:59.500767
libpoppler-cpp-dev libpoppler-dev poppler-utils \
2025-Aug-18 12:18:59.500767
# General utilities
2025-Aug-18 12:18:59.500767
build-essential \
2025-Aug-18 12:18:59.500767
&& rm -rf /var/lib/apt/lists/*
2025-Aug-18 12:18:59.500767
2025-Aug-18 12:18:59.500767
# Set working directory
2025-Aug-18 12:18:59.500767
WORKDIR /app
2025-Aug-18 12:18:59.500767
2025-Aug-18 12:18:59.500767
# Copy project files
2025-Aug-18 12:18:59.500767
COPY pyproject.toml uv.lock* ./
2025-Aug-18 12:18:59.500767
2025-Aug-18 12:18:59.500767
# Install dependencies with uv
2025-Aug-18 12:18:59.500767
RUN uv sync --frozen --no-cache
2025-Aug-18 12:18:59.500767
2025-Aug-18 12:18:59.500767
# Install Playwright browsers without system dependencies first
2025-Aug-18 12:18:59.500767
RUN uv run playwright install chromium
2025-Aug-18 12:18:59.500767
2025-Aug-18 12:18:59.500767
# Install additional system dependencies that Playwright might need
2025-Aug-18 12:18:59.500767
RUN apt-get update && apt-get install -y \
2025-Aug-18 12:18:59.500767
# Try to install additional packages that might be needed
2025-Aug-18 12:18:59.500767
libwebp7 || libwebp6 || true \
2025-Aug-18 12:18:59.500767
&& rm -rf /var/lib/apt/lists/* || true
2025-Aug-18 12:18:59.500767
2025-Aug-18 12:18:59.500767
# Create data directory for PDFs
2025-Aug-18 12:18:59.500767
RUN mkdir -p /data/pdfs
2025-Aug-18 12:18:59.500767
2025-Aug-18 12:18:59.500767
# Copy application code
2025-Aug-18 12:18:59.500767
COPY . .
2025-Aug-18 12:18:59.500767
2025-Aug-18 12:18:59.500767
# Expose port 8080
2025-Aug-18 12:18:59.500767
EXPOSE 8080
2025-Aug-18 12:18:59.500767
2025-Aug-18 12:18:59.500767
# Use uv to run the application
2025-Aug-18 12:18:59.500767
CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080"]
2025-Aug-18 12:18:59.864921
[CMD]: docker exec w0gwk0wcos0s8cswsck4c0g0 bash -c 'cat /artifacts/w0gwk0wcos0s8cswsck4c0g0/Dockerfile'
2025-Aug-18 12:18:59.864921
FROM python:3.12-slim
2025-Aug-18 12:18:59.864921
2025-Aug-18 12:18:59.864921
# Telekom RBU Downloader - FastAPI service with Playwright automation and PDF extraction
2025-Aug-18 12:18:59.864921
# Features: PDF download from Telekom portal + PDF data extraction
2025-Aug-18 12:18:59.864921
2025-Aug-18 12:18:59.864921
# Install uv
2025-Aug-18 12:18:59.864921
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /usr/local/bin/
2025-Aug-18 12:18:59.864921
2025-Aug-18 12:18:59.864921
# System deps for Playwright and PDF processing
2025-Aug-18 12:18:59.864921
RUN apt-get update && apt-get install -y \
2025-Aug-18 12:18:59.864921
# Basic utilities
2025-Aug-18 12:18:59.864921
wget gnupg curl ca-certificates \
2025-Aug-18 12:18:59.864921
# Playwright dependencies
2025-Aug-18 12:18:59.864921
libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1 \
2025-Aug-18 12:18:59.864921
libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2 \
2025-Aug-18 12:18:59.864921
libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0 \
2025-Aug-18 12:18:59.864921
libwayland-server0 libxshmfence1 libegl1 \
2025-Aug-18 12:18:59.864921
# Additional Playwright dependencies for Chromium
2025-Aug-18 12:18:59.864921
libxss1 libgconf-2-4 libxtst6 libxrandr2 libasound2 libpangocairo-1.0-0 \
2025-Aug-18 12:18:59.864921
libatk1.0-0 libcairo-gobject2 libgtk-3-0 libgdk-pixbuf2.0-0 \
2025-Aug-18 12:18:59.864921
# Font packages (alternative names for missing packages)
2025-Aug-18 12:18:59.864921
fonts-liberation fonts-noto-color-emoji fonts-noto-cjk \
2025-Aug-18 12:18:59.864921
# PDF processing dependencies
2025-Aug-18 12:18:59.864921
libpoppler-cpp-dev libpoppler-dev poppler-utils \
2025-Aug-18 12:18:59.864921
# General utilities
2025-Aug-18 12:18:59.864921
build-essential \
2025-Aug-18 12:18:59.864921
&& rm -rf /var/lib/apt/lists/*
2025-Aug-18 12:18:59.864921
2025-Aug-18 12:18:59.864921
# Set working directory
2025-Aug-18 12:18:59.864921
WORKDIR /app
2025-Aug-18 12:18:59.864921
2025-Aug-18 12:18:59.864921
# Copy project files
2025-Aug-18 12:18:59.864921
COPY pyproject.toml uv.lock* ./
2025-Aug-18 12:18:59.864921
2025-Aug-18 12:18:59.864921
# Install dependencies with uv
2025-Aug-18 12:18:59.864921
RUN uv sync --frozen --no-cache
2025-Aug-18 12:18:59.864921
2025-Aug-18 12:18:59.864921
# Install Playwright browsers without system dependencies first
2025-Aug-18 12:18:59.864921
RUN uv run playwright install chromium
2025-Aug-18 12:18:59.864921
2025-Aug-18 12:18:59.864921
# Install additional system dependencies that Playwright might need
2025-Aug-18 12:18:59.864921
RUN apt-get update && apt-get install -y \
2025-Aug-18 12:18:59.864921
# Try to install additional packages that might be needed
2025-Aug-18 12:18:59.864921
libwebp7 || libwebp6 || true \
2025-Aug-18 12:18:59.864921
&& rm -rf /var/lib/apt/lists/* || true
2025-Aug-18 12:18:59.864921
2025-Aug-18 12:18:59.864921
# Create data directory for PDFs
2025-Aug-18 12:18:59.864921
RUN mkdir -p /data/pdfs
2025-Aug-18 12:18:59.864921
2025-Aug-18 12:18:59.864921
# Copy application code
2025-Aug-18 12:18:59.864921
COPY . .
2025-Aug-18 12:18:59.864921
2025-Aug-18 12:18:59.864921
# Expose port 8080
2025-Aug-18 12:18:59.864921
EXPOSE 8080
2025-Aug-18 12:18:59.864921
2025-Aug-18 12:18:59.864921
# Use uv to run the application
2025-Aug-18 12:18:59.864921
CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080"]
2025-Aug-18 12:19:00.065112
----------------------------------------
2025-Aug-18 12:19:00.071097
Building docker image started.
2025-Aug-18 12:19:00.077147
To check the current progress, click on Show Debug Logs.
2025-Aug-18 12:19:00.463499
[CMD]: docker exec w0gwk0wcos0s8cswsck4c0g0 bash -c 'cat /artifacts/build.sh'
2025-Aug-18 12:19:00.463499
docker build --no-cache  --add-host coolify:******** --add-host coolify-db:******** --add-host coolify-realtime:******** --add-host coolify-redis:******** --network host -f /artifacts/w0gwk0wcos0s8cswsck4c0g0/Dockerfile --build-arg SOURCE_COMMIT='a6df31bd657e7ea5f67566f06dceec1411ceece2' --build-arg 'COOLIFY_URL=http://rbu.anesa.dev' --build-arg 'COOLIFY_FQDN=rbu.anesa.dev' --build-arg 'COOLIFY_BRANCH=master' --build-arg 'COOLIFY_RESOURCE_UUID=uwcw80cc400scwckgwo8wo0g' --build-arg 'COOLIFY_CONTAINER_NAME=uwcw80cc400scwckgwo8wo0g-121852275710' --progress plain -t uwcw80cc400scwckgwo8wo0g:a6df31bd657e7ea5f67566f06dceec1411ceece2 /artifacts/w0gwk0wcos0s8cswsck4c0g0
2025-Aug-18 12:19:01.138903
[CMD]: docker exec w0gwk0wcos0s8cswsck4c0g0 bash -c 'bash /artifacts/build.sh'
2025-Aug-18 12:19:01.138903
#0 building with "default" instance using docker driver
2025-Aug-18 12:19:01.138903
2025-Aug-18 12:19:01.138903
#1 [internal] load build definition from Dockerfile
2025-Aug-18 12:19:01.138903
#1 transferring dockerfile: 1.97kB done
2025-Aug-18 12:19:01.138903
#1 DONE 0.0s
2025-Aug-18 12:19:01.138903
2025-Aug-18 12:19:01.138903
#2 [internal] load metadata for docker.io/library/python:3.12-slim
2025-Aug-18 12:19:01.467302
#2 ...
2025-Aug-18 12:19:01.467302
2025-Aug-18 12:19:01.467302
#3 [internal] load metadata for ghcr.io/astral-sh/uv:latest
2025-Aug-18 12:19:01.467302
#3 DONE 0.5s
2025-Aug-18 12:19:01.618764
#2 [internal] load metadata for docker.io/library/python:3.12-slim
2025-Aug-18 12:19:01.733084
#2 DONE 0.7s
2025-Aug-18 12:19:01.946713
#4 [internal] load .dockerignore
2025-Aug-18 12:19:01.946713
#4 transferring context: 2B done
2025-Aug-18 12:19:01.946713
#4 DONE 0.0s
2025-Aug-18 12:19:01.946713
2025-Aug-18 12:19:01.946713
#5 FROM ghcr.io/astral-sh/uv:latest@sha256:8101ad825250a114e7bef89eefaa73c31e34e10ffbe5aff01562740bac97553c
2025-Aug-18 12:19:01.946713
#5 CACHED
2025-Aug-18 12:19:01.946713
2025-Aug-18 12:19:01.946713
#6 [stage-0  1/10] FROM docker.io/library/python:3.12-slim@sha256:d67a7b66b989ad6b6d6b10d428dcc5e0bfc3e5f88906e67d490c4d3daac57047
2025-Aug-18 12:19:01.946713
#6 CACHED
2025-Aug-18 12:19:01.946713
2025-Aug-18 12:19:01.946713
#7 [internal] load build context
2025-Aug-18 12:19:01.946713
#7 transferring context: 350.78kB 0.0s done
2025-Aug-18 12:19:01.946713
#7 DONE 0.0s
2025-Aug-18 12:19:01.946713
2025-Aug-18 12:19:01.946713
#8 [stage-0  2/10] COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /usr/local/bin/
2025-Aug-18 12:19:01.946713
#8 DONE 0.2s
2025-Aug-18 12:19:02.102146
#9 [stage-0  3/10] RUN apt-get update && apt-get install -y     wget gnupg curl ca-certificates     libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1     libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2     libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0     libwayland-server0 libxshmfence1 libegl1     libxss1 libgconf-2-4 libxtst6 libxrandr2 libasound2 libpangocairo-1.0-0     libatk1.0-0 libcairo-gobject2 libgtk-3-0 libgdk-pixbuf2.0-0     fonts-liberation fonts-noto-color-emoji fonts-noto-cjk     libpoppler-cpp-dev libpoppler-dev poppler-utils     build-essential  && rm -rf /var/lib/apt/lists/*
2025-Aug-18 12:19:02.116684
#9 0.165 Hit:1 http://deb.debian.org/debian trixie InRelease
2025-Aug-18 12:19:02.235691
#9 0.166 Get:2 http://deb.debian.org/debian trixie-updates InRelease [47.1 kB]
2025-Aug-18 12:19:02.235691
#9 0.172 Get:3 http://deb.debian.org/debian-security trixie-security InRelease [43.4 kB]
2025-Aug-18 12:19:02.235691
#9 0.196 Get:4 http://deb.debian.org/debian trixie/main arm64 Packages [9604 kB]
2025-Aug-18 12:19:02.235691
#9 0.285 Get:5 http://deb.debian.org/debian trixie-updates/main arm64 Packages [2432 B]
2025-Aug-18 12:19:02.235691
#9 0.285 Get:6 http://deb.debian.org/debian-security trixie-security/main arm64 Packages [9464 B]
2025-Aug-18 12:19:03.232650
#9 1.281 Fetched 9706 kB in 1s (8422 kB/s)
2025-Aug-18 12:19:03.232650
#9 1.281 Reading package lists...
2025-Aug-18 12:19:04.032683
2025-Aug-18 12:19:04.213077
#9 2.110 Reading package lists...
2025-Aug-18 12:19:04.770962
2025-Aug-18 12:19:04.933465
#9 2.832 Building dependency tree...
2025-Aug-18 12:19:05.026382
#9 3.075 Reading state information...
2025-Aug-18 12:19:05.139784
#9 3.118 Package libgdk-pixbuf2.0-0 is not available, but is referred to by another package.
2025-Aug-18 12:19:05.139784
#9 3.118 This may mean that the package is missing, has been obsoleted, or
2025-Aug-18 12:19:05.139784
#9 3.118 is only available from another source
2025-Aug-18 12:19:05.139784
#9 3.118 However the following packages replace it:
2025-Aug-18 12:19:05.139784
#9 3.118   libgdk-pixbuf-xlib-2.0-0
2025-Aug-18 12:19:05.139784
#9 3.118
2025-Aug-18 12:19:05.139784
#9 3.121 E: Unable to locate package libgconf-2-4
2025-Aug-18 12:19:05.139784
#9 3.121 E: Package 'libgdk-pixbuf2.0-0' has no installation candidate
2025-Aug-18 12:19:05.139784
#9 ERROR: process "/bin/sh -c apt-get update && apt-get install -y     wget gnupg curl ca-certificates     libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1     libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2     libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0     libwayland-server0 libxshmfence1 libegl1     libxss1 libgconf-2-4 libxtst6 libxrandr2 libasound2 libpangocairo-1.0-0     libatk1.0-0 libcairo-gobject2 libgtk-3-0 libgdk-pixbuf2.0-0     fonts-liberation fonts-noto-color-emoji fonts-noto-cjk     libpoppler-cpp-dev libpoppler-dev poppler-utils     build-essential  && rm -rf /var/lib/apt/lists/*" did not complete successfully: exit code: 100
2025-Aug-18 12:19:05.139784
------
2025-Aug-18 12:19:05.139784
> [stage-0  3/10] RUN apt-get update && apt-get install -y     wget gnupg curl ca-certificates     libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1     libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2     libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0     libwayland-server0 libxshmfence1 libegl1     libxss1 libgconf-2-4 libxtst6 libxrandr2 libasound2 libpangocairo-1.0-0     libatk1.0-0 libcairo-gobject2 libgtk-3-0 libgdk-pixbuf2.0-0     fonts-liberation fonts-noto-color-emoji fonts-noto-cjk     libpoppler-cpp-dev libpoppler-dev poppler-utils     build-essential  && rm -rf /var/lib/apt/lists/*:
2025-Aug-18 12:19:05.139784
2025-Aug-18 12:19:05.139784
2025-Aug-18 12:19:05.139784
3.118 Package libgdk-pixbuf2.0-0 is not available, but is referred to by another package.
2025-Aug-18 12:19:05.139784
3.118 This may mean that the package is missing, has been obsoleted, or
2025-Aug-18 12:19:05.139784
3.118 is only available from another source
2025-Aug-18 12:19:05.139784
3.118 However the following packages replace it:
2025-Aug-18 12:19:05.139784
3.118   libgdk-pixbuf-xlib-2.0-0
2025-Aug-18 12:19:05.139784
3.118
2025-Aug-18 12:19:05.139784
3.121 E: Unable to locate package libgconf-2-4
2025-Aug-18 12:19:05.139784
3.121 E: Package 'libgdk-pixbuf2.0-0' has no installation candidate
2025-Aug-18 12:19:05.139784
------
2025-Aug-18 12:19:05.148127
Dockerfile:10
2025-Aug-18 12:19:05.148127
--------------------
2025-Aug-18 12:19:05.148127
9 |     # System deps for Playwright and PDF processing
2025-Aug-18 12:19:05.148127
10 | >>> RUN apt-get update && apt-get install -y \
2025-Aug-18 12:19:05.148127
11 | >>>     # Basic utilities
2025-Aug-18 12:19:05.148127
12 | >>>     wget gnupg curl ca-certificates \
2025-Aug-18 12:19:05.148127
13 | >>>     # Playwright dependencies
2025-Aug-18 12:19:05.148127
14 | >>>     libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1 \
2025-Aug-18 12:19:05.148127
15 | >>>     libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2 \
2025-Aug-18 12:19:05.148127
16 | >>>     libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0 \
2025-Aug-18 12:19:05.148127
17 | >>>     libwayland-server0 libxshmfence1 libegl1 \
2025-Aug-18 12:19:05.148127
18 | >>>     # Additional Playwright dependencies for Chromium
2025-Aug-18 12:19:05.148127
19 | >>>     libxss1 libgconf-2-4 libxtst6 libxrandr2 libasound2 libpangocairo-1.0-0 \
2025-Aug-18 12:19:05.148127
20 | >>>     libatk1.0-0 libcairo-gobject2 libgtk-3-0 libgdk-pixbuf2.0-0 \
2025-Aug-18 12:19:05.148127
21 | >>>     # Font packages (alternative names for missing packages)
2025-Aug-18 12:19:05.148127
22 | >>>     fonts-liberation fonts-noto-color-emoji fonts-noto-cjk \
2025-Aug-18 12:19:05.148127
23 | >>>     # PDF processing dependencies
2025-Aug-18 12:19:05.148127
24 | >>>     libpoppler-cpp-dev libpoppler-dev poppler-utils \
2025-Aug-18 12:19:05.148127
25 | >>>     # General utilities
2025-Aug-18 12:19:05.148127
26 | >>>     build-essential \
2025-Aug-18 12:19:05.148127
27 | >>>  && rm -rf /var/lib/apt/lists/*
2025-Aug-18 12:19:05.148127
28 |
2025-Aug-18 12:19:05.148127
--------------------
2025-Aug-18 12:19:05.148127
ERROR: failed to solve: process "/bin/sh -c apt-get update && apt-get install -y     wget gnupg curl ca-certificates     libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1     libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2     libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0     libwayland-server0 libxshmfence1 libegl1     libxss1 libgconf-2-4 libxtst6 libxrandr2 libasound2 libpangocairo-1.0-0     libatk1.0-0 libcairo-gobject2 libgtk-3-0 libgdk-pixbuf2.0-0     fonts-liberation fonts-noto-color-emoji fonts-noto-cjk     libpoppler-cpp-dev libpoppler-dev poppler-utils     build-essential  && rm -rf /var/lib/apt/lists/*" did not complete successfully: exit code: 100
2025-Aug-18 12:19:05.148127
exit status 1
2025-Aug-18 12:19:05.206484
Oops something is not okay, are you okay? 😢
2025-Aug-18 12:19:05.212358
#0 building with "default" instance using docker driver
2025-Aug-18 12:19:05.212358
2025-Aug-18 12:19:05.212358
#1 [internal] load build definition from Dockerfile
2025-Aug-18 12:19:05.212358
#1 transferring dockerfile: 1.97kB done
2025-Aug-18 12:19:05.212358
#1 DONE 0.0s
2025-Aug-18 12:19:05.212358
2025-Aug-18 12:19:05.212358
#2 [internal] load metadata for docker.io/library/python:3.12-slim
2025-Aug-18 12:19:05.212358
#2 ...
2025-Aug-18 12:19:05.212358
2025-Aug-18 12:19:05.212358
#3 [internal] load metadata for ghcr.io/astral-sh/uv:latest
2025-Aug-18 12:19:05.212358
#3 DONE 0.5s
2025-Aug-18 12:19:05.212358
2025-Aug-18 12:19:05.212358
#2 [internal] load metadata for docker.io/library/python:3.12-slim
2025-Aug-18 12:19:05.212358
#2 DONE 0.7s
2025-Aug-18 12:19:05.212358
2025-Aug-18 12:19:05.212358
#4 [internal] load .dockerignore
2025-Aug-18 12:19:05.212358
#4 transferring context: 2B done
2025-Aug-18 12:19:05.212358
#4 DONE 0.0s
2025-Aug-18 12:19:05.212358
2025-Aug-18 12:19:05.212358
#5 FROM ghcr.io/astral-sh/uv:latest@sha256:8101ad825250a114e7bef89eefaa73c31e34e10ffbe5aff01562740bac97553c
2025-Aug-18 12:19:05.212358
#5 CACHED
2025-Aug-18 12:19:05.212358
2025-Aug-18 12:19:05.212358
#6 [stage-0  1/10] FROM docker.io/library/python:3.12-slim@sha256:d67a7b66b989ad6b6d6b10d428dcc5e0bfc3e5f88906e67d490c4d3daac57047
2025-Aug-18 12:19:05.212358
#6 CACHED
2025-Aug-18 12:19:05.212358
2025-Aug-18 12:19:05.212358
#7 [internal] load build context
2025-Aug-18 12:19:05.212358
#7 transferring context: 350.78kB 0.0s done
2025-Aug-18 12:19:05.212358
#7 DONE 0.0s
2025-Aug-18 12:19:05.212358
2025-Aug-18 12:19:05.212358
#8 [stage-0  2/10] COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /usr/local/bin/
2025-Aug-18 12:19:05.212358
#8 DONE 0.2s
2025-Aug-18 12:19:05.212358
2025-Aug-18 12:19:05.212358
#9 [stage-0  3/10] RUN apt-get update && apt-get install -y     wget gnupg curl ca-certificates     libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1     libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2     libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0     libwayland-server0 libxshmfence1 libegl1     libxss1 libgconf-2-4 libxtst6 libxrandr2 libasound2 libpangocairo-1.0-0     libatk1.0-0 libcairo-gobject2 libgtk-3-0 libgdk-pixbuf2.0-0     fonts-liberation fonts-noto-color-emoji fonts-noto-cjk     libpoppler-cpp-dev libpoppler-dev poppler-utils     build-essential  && rm -rf /var/lib/apt/lists/*
2025-Aug-18 12:19:05.212358
#9 0.165 Hit:1 http://deb.debian.org/debian trixie InRelease
2025-Aug-18 12:19:05.212358
#9 0.166 Get:2 http://deb.debian.org/debian trixie-updates InRelease [47.1 kB]
2025-Aug-18 12:19:05.212358
#9 0.172 Get:3 http://deb.debian.org/debian-security trixie-security InRelease [43.4 kB]
2025-Aug-18 12:19:05.212358
#9 0.196 Get:4 http://deb.debian.org/debian trixie/main arm64 Packages [9604 kB]
2025-Aug-18 12:19:05.212358
#9 0.285 Get:5 http://deb.debian.org/debian trixie-updates/main arm64 Packages [2432 B]
2025-Aug-18 12:19:05.212358
#9 0.285 Get:6 http://deb.debian.org/debian-security trixie-security/main arm64 Packages [9464 B]
2025-Aug-18 12:19:05.212358
#9 1.281 Fetched 9706 kB in 1s (8422 kB/s)
2025-Aug-18 12:19:05.212358
#9 1.281 Reading package lists...
2025-Aug-18 12:19:05.212358
#9 2.110 Reading package lists...
2025-Aug-18 12:19:05.212358
#9 2.832 Building dependency tree...
2025-Aug-18 12:19:05.212358
#9 3.075 Reading state information...
2025-Aug-18 12:19:05.212358
#9 3.118 Package libgdk-pixbuf2.0-0 is not available, but is referred to by another package.
2025-Aug-18 12:19:05.212358
#9 3.118 This may mean that the package is missing, has been obsoleted, or
2025-Aug-18 12:19:05.212358
#9 3.118 is only available from another source
2025-Aug-18 12:19:05.212358
#9 3.118 However the following packages replace it:
2025-Aug-18 12:19:05.212358
#9 3.118   libgdk-pixbuf-xlib-2.0-0
2025-Aug-18 12:19:05.212358
#9 3.118
2025-Aug-18 12:19:05.212358
#9 3.121 E: Unable to locate package libgconf-2-4
2025-Aug-18 12:19:05.212358
#9 3.121 E: Package 'libgdk-pixbuf2.0-0' has no installation candidate
2025-Aug-18 12:19:05.212358
#9 ERROR: process "/bin/sh -c apt-get update && apt-get install -y     wget gnupg curl ca-certificates     libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1     libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2     libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0     libwayland-server0 libxshmfence1 libegl1     libxss1 libgconf-2-4 libxtst6 libxrandr2 libasound2 libpangocairo-1.0-0     libatk1.0-0 libcairo-gobject2 libgtk-3-0 libgdk-pixbuf2.0-0     fonts-liberation fonts-noto-color-emoji fonts-noto-cjk     libpoppler-cpp-dev libpoppler-dev poppler-utils     build-essential  && rm -rf /var/lib/apt/lists/*" did not complete successfully: exit code: 100
2025-Aug-18 12:19:05.212358
------
2025-Aug-18 12:19:05.212358
> [stage-0  3/10] RUN apt-get update && apt-get install -y     wget gnupg curl ca-certificates     libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1     libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2     libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0     libwayland-server0 libxshmfence1 libegl1     libxss1 libgconf-2-4 libxtst6 libxrandr2 libasound2 libpangocairo-1.0-0     libatk1.0-0 libcairo-gobject2 libgtk-3-0 libgdk-pixbuf2.0-0     fonts-liberation fonts-noto-color-emoji fonts-noto-cjk     libpoppler-cpp-dev libpoppler-dev poppler-utils     build-essential  && rm -rf /var/lib/apt/lists/*:
2025-Aug-18 12:19:05.212358
2025-Aug-18 12:19:05.212358
2025-Aug-18 12:19:05.212358
3.118 Package libgdk-pixbuf2.0-0 is not available, but is referred to by another package.
2025-Aug-18 12:19:05.212358
3.118 This may mean that the package is missing, has been obsoleted, or
2025-Aug-18 12:19:05.212358
3.118 is only available from another source
2025-Aug-18 12:19:05.212358
3.118 However the following packages replace it:
2025-Aug-18 12:19:05.212358
3.118   libgdk-pixbuf-xlib-2.0-0
2025-Aug-18 12:19:05.212358
3.118
2025-Aug-18 12:19:05.212358
3.121 E: Unable to locate package libgconf-2-4
2025-Aug-18 12:19:05.212358
3.121 E: Package 'libgdk-pixbuf2.0-0' has no installation candidate
2025-Aug-18 12:19:05.212358
------
2025-Aug-18 12:19:05.212358
Dockerfile:10
2025-Aug-18 12:19:05.212358
--------------------
2025-Aug-18 12:19:05.212358
9 |     # System deps for Playwright and PDF processing
2025-Aug-18 12:19:05.212358
10 | >>> RUN apt-get update && apt-get install -y \
2025-Aug-18 12:19:05.212358
11 | >>>     # Basic utilities
2025-Aug-18 12:19:05.212358
12 | >>>     wget gnupg curl ca-certificates \
2025-Aug-18 12:19:05.212358
13 | >>>     # Playwright dependencies
2025-Aug-18 12:19:05.212358
14 | >>>     libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1 \
2025-Aug-18 12:19:05.212358
15 | >>>     libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2 \
2025-Aug-18 12:19:05.212358
16 | >>>     libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0 \
2025-Aug-18 12:19:05.212358
17 | >>>     libwayland-server0 libxshmfence1 libegl1 \
2025-Aug-18 12:19:05.212358
18 | >>>     # Additional Playwright dependencies for Chromium
2025-Aug-18 12:19:05.212358
19 | >>>     libxss1 libgconf-2-4 libxtst6 libxrandr2 libasound2 libpangocairo-1.0-0 \
2025-Aug-18 12:19:05.212358
20 | >>>     libatk1.0-0 libcairo-gobject2 libgtk-3-0 libgdk-pixbuf2.0-0 \
2025-Aug-18 12:19:05.212358
21 | >>>     # Font packages (alternative names for missing packages)
2025-Aug-18 12:19:05.212358
22 | >>>     fonts-liberation fonts-noto-color-emoji fonts-noto-cjk \
2025-Aug-18 12:19:05.212358
23 | >>>     # PDF processing dependencies
2025-Aug-18 12:19:05.212358
24 | >>>     libpoppler-cpp-dev libpoppler-dev poppler-utils \
2025-Aug-18 12:19:05.212358
25 | >>>     # General utilities
2025-Aug-18 12:19:05.212358
26 | >>>     build-essential \
2025-Aug-18 12:19:05.212358
27 | >>>  && rm -rf /var/lib/apt/lists/*
2025-Aug-18 12:19:05.212358
28 |
2025-Aug-18 12:19:05.212358
--------------------
2025-Aug-18 12:19:05.212358
ERROR: failed to solve: process "/bin/sh -c apt-get update && apt-get install -y     wget gnupg curl ca-certificates     libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1     libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2     libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0     libwayland-server0 libxshmfence1 libegl1     libxss1 libgconf-2-4 libxtst6 libxrandr2 libasound2 libpangocairo-1.0-0     libatk1.0-0 libcairo-gobject2 libgtk-3-0 libgdk-pixbuf2.0-0     fonts-liberation fonts-noto-color-emoji fonts-noto-cjk     libpoppler-cpp-dev libpoppler-dev poppler-utils     build-essential  && rm -rf /var/lib/apt/lists/*" did not complete successfully: exit code: 100
2025-Aug-18 12:19:05.212358
exit status 1
2025-Aug-18 12:19:05.218467
Deployment failed. Removing the new version of your application.
2025-Aug-18 12:19:05.696157
Gracefully shutting down build container: w0gwk0wcos0s8cswsck4c0g0
2025-Aug-18 12:19:05.803454
[CMD]: docker stop --time=30 w0gwk0wcos0s8cswsck4c0g0
2025-Aug-18 12:19:05.803454
Flag --time has been deprecated, use --timeout instead
2025-Aug-18 12:19:05.951930
w0gwk0wcos0s8cswsck4c0g0
2025-Aug-18 12:19:06.075326
[CMD]: docker rm -f w0gwk0wcos0s8cswsck4c0g0
2025-Aug-18 12:19:06.075326
Error response from daemon: No such container: w0gwk0wcos0s8cswsck4c0g0