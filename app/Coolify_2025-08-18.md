Coolify
v4.0.0-beta.420.6

Current Team

Root Team
Dashboard
Projects
Servers
Sources
Destinations
S3 Storages
Shared Variables
Notifications
Keys & Tokens
Tags
Terminal
Profile
Teams
Settings
Sponsor us
Feedback
Logout
Deployment
Anesa
production
TelekomRBUDownloader
Running

Configuration
Deployments
Logs
Terminal

Links

Advanced
Redeploy
Restart
Stop
Deployment Log
Hide Debug Logs
Deployment is Failed.




2025-Aug-18 12:12:20.512130
Starting deployment of hakanhakan/TelekomRBUDownloader:master to localhost.
2025-Aug-18 12:12:20.707303
Preparing container with helper image: ghcr.io/coollabsio/coolify-helper:1.0.8.
2025-Aug-18 12:12:20.817342
[CMD]: docker stop --time=30 pcwwc0w8gc0ck4gsgsk0cg4o
2025-Aug-18 12:12:20.817342
Flag --time has been deprecated, use --timeout instead
2025-Aug-18 12:12:20.819661
Error response from daemon: No such container: pcwwc0w8gc0ck4gsgsk0cg4o
2025-Aug-18 12:12:20.938539
[CMD]: docker rm -f pcwwc0w8gc0ck4gsgsk0cg4o
2025-Aug-18 12:12:20.938539
Error response from daemon: No such container: pcwwc0w8gc0ck4gsgsk0cg4o
2025-Aug-18 12:12:21.127718
[CMD]: docker run -d --network coolify --name pcwwc0w8gc0ck4gsgsk0cg4o --rm -v /var/run/docker.sock:/var/run/docker.sock ghcr.io/coollabsio/coolify-helper:1.0.8
2025-Aug-18 12:12:21.127718
a16b0b4c0f98d8724db62d19daf22c56cfe0347b37b78048f4cb10da8bb2a287
2025-Aug-18 12:12:23.267865
[CMD]: docker exec pcwwc0w8gc0ck4gsgsk0cg4o bash -c 'GIT_SSH_COMMAND="ssh -o ConnectTimeout=30 -p 22 -o Port=22 -o LogLevel=ERROR -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git ls-remote https://x-access-token:<REDACTED>@github.com/hakanhakan/TelekomRBUDownloader.git master'
2025-Aug-18 12:12:23.267865
1cde377098244ef4e29b09c3a5d4139345491123	refs/heads/master
2025-Aug-18 12:12:23.569912
----------------------------------------
2025-Aug-18 12:12:23.575387
Importing hakanhakan/TelekomRBUDownloader:master (commit sha HEAD) to /artifacts/pcwwc0w8gc0ck4gsgsk0cg4o.
2025-Aug-18 12:12:23.737801
[CMD]: docker exec pcwwc0w8gc0ck4gsgsk0cg4o bash -c 'git clone -b "master" https://x-access-token:<REDACTED>@github.com/hakanhakan/TelekomRBUDownloader.git /artifacts/pcwwc0w8gc0ck4gsgsk0cg4o && cd /artifacts/pcwwc0w8gc0ck4gsgsk0cg4o && GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git submodule update --init --recursive && cd /artifacts/pcwwc0w8gc0ck4gsgsk0cg4o && GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git lfs pull'
2025-Aug-18 12:12:23.737801
Cloning into '/artifacts/pcwwc0w8gc0ck4gsgsk0cg4o'...
2025-Aug-18 12:12:25.243078
[CMD]: docker exec pcwwc0w8gc0ck4gsgsk0cg4o bash -c 'cd /artifacts/pcwwc0w8gc0ck4gsgsk0cg4o && git log -1 1cde377098244ef4e29b09c3a5d4139345491123 --pretty=%B'
2025-Aug-18 12:12:25.243078
feat: Implement robust process management and health monitoring to prevent zombie processes
2025-Aug-18 12:12:26.188796
[CMD]: docker exec pcwwc0w8gc0ck4gsgsk0cg4o bash -c 'cat /artifacts/pcwwc0w8gc0ck4gsgsk0cg4o/Dockerfile'
2025-Aug-18 12:12:26.188796
FROM python:3.12-slim
2025-Aug-18 12:12:26.***********-Aug-18 12:12:26.188796
# Telekom RBU Downloader - FastAPI service with Playwright automation and PDF extraction
2025-Aug-18 12:12:26.188796
# Features: PDF download from Telekom portal + PDF data extraction
2025-Aug-18 12:12:26.***********-Aug-18 12:12:26.188796
# Install uv
2025-Aug-18 12:12:26.188796
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /usr/local/bin/
2025-Aug-18 12:12:26.***********-Aug-18 12:12:26.188796
# System deps for Playwright and PDF processing
2025-Aug-18 12:12:26.188796
RUN apt-get update && apt-get install -y \
2025-Aug-18 12:12:26.188796
# Playwright dependencies
2025-Aug-18 12:12:26.188796
wget gnupg libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1 \
2025-Aug-18 12:12:26.188796
libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2 \
2025-Aug-18 12:12:26.188796
libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0 \
2025-Aug-18 12:12:26.188796
libwayland-server0 libxshmfence1 libegl1 \
2025-Aug-18 12:12:26.188796
# PDF processing dependencies
2025-Aug-18 12:12:26.188796
libpoppler-cpp-dev libpoppler-dev poppler-utils \
2025-Aug-18 12:12:26.188796
# General utilities
2025-Aug-18 12:12:26.188796
build-essential \
2025-Aug-18 12:12:26.188796
&& rm -rf /var/lib/apt/lists/*
2025-Aug-18 12:12:26.***********-Aug-18 12:12:26.188796
# Set working directory
2025-Aug-18 12:12:26.188796
WORKDIR /app
2025-Aug-18 12:12:26.***********-Aug-18 12:12:26.188796
# Copy project files
2025-Aug-18 12:12:26.188796
COPY pyproject.toml uv.lock* ./
2025-Aug-18 12:12:26.***********-Aug-18 12:12:26.188796
# Install dependencies with uv
2025-Aug-18 12:12:26.188796
RUN uv sync --frozen --no-cache
2025-Aug-18 12:12:26.188796
RUN uv run playwright install --with-deps
2025-Aug-18 12:12:26.***********-Aug-18 12:12:26.188796
# Create data directory for PDFs
2025-Aug-18 12:12:26.188796
RUN mkdir -p /data/pdfs
2025-Aug-18 12:12:26.***********-Aug-18 12:12:26.188796
# Copy application code
2025-Aug-18 12:12:26.188796
COPY . .
2025-Aug-18 12:12:26.***********-Aug-18 12:12:26.188796
# Expose port 8080
2025-Aug-18 12:12:26.188796
EXPOSE 8080
2025-Aug-18 12:12:26.***********-Aug-18 12:12:26.188796
# Use uv to run the application
2025-Aug-18 12:12:26.188796
CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080"]
2025-Aug-18 12:12:26.525120
[CMD]: docker exec pcwwc0w8gc0ck4gsgsk0cg4o bash -c 'cat /artifacts/pcwwc0w8gc0ck4gsgsk0cg4o/Dockerfile'
2025-Aug-18 12:12:26.525120
FROM python:3.12-slim
2025-Aug-18 12:12:26.525120
2025-Aug-18 12:12:26.525120
# Telekom RBU Downloader - FastAPI service with Playwright automation and PDF extraction
2025-Aug-18 12:12:26.525120
# Features: PDF download from Telekom portal + PDF data extraction
2025-Aug-18 12:12:26.525120
2025-Aug-18 12:12:26.525120
# Install uv
2025-Aug-18 12:12:26.525120
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /usr/local/bin/
2025-Aug-18 12:12:26.525120
2025-Aug-18 12:12:26.525120
# System deps for Playwright and PDF processing
2025-Aug-18 12:12:26.525120
RUN apt-get update && apt-get install -y \
2025-Aug-18 12:12:26.525120
# Playwright dependencies
2025-Aug-18 12:12:26.525120
wget gnupg libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1 \
2025-Aug-18 12:12:26.525120
libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2 \
2025-Aug-18 12:12:26.525120
libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0 \
2025-Aug-18 12:12:26.525120
libwayland-server0 libxshmfence1 libegl1 \
2025-Aug-18 12:12:26.525120
# PDF processing dependencies
2025-Aug-18 12:12:26.525120
libpoppler-cpp-dev libpoppler-dev poppler-utils \
2025-Aug-18 12:12:26.525120
# General utilities
2025-Aug-18 12:12:26.525120
build-essential \
2025-Aug-18 12:12:26.525120
&& rm -rf /var/lib/apt/lists/*
2025-Aug-18 12:12:26.525120
2025-Aug-18 12:12:26.525120
# Set working directory
2025-Aug-18 12:12:26.525120
WORKDIR /app
2025-Aug-18 12:12:26.525120
2025-Aug-18 12:12:26.525120
# Copy project files
2025-Aug-18 12:12:26.525120
COPY pyproject.toml uv.lock* ./
2025-Aug-18 12:12:26.525120
2025-Aug-18 12:12:26.525120
# Install dependencies with uv
2025-Aug-18 12:12:26.525120
RUN uv sync --frozen --no-cache
2025-Aug-18 12:12:26.525120
RUN uv run playwright install --with-deps
2025-Aug-18 12:12:26.525120
2025-Aug-18 12:12:26.525120
# Create data directory for PDFs
2025-Aug-18 12:12:26.525120
RUN mkdir -p /data/pdfs
2025-Aug-18 12:12:26.525120
2025-Aug-18 12:12:26.525120
# Copy application code
2025-Aug-18 12:12:26.525120
COPY . .
2025-Aug-18 12:12:26.525120
2025-Aug-18 12:12:26.525120
# Expose port 8080
2025-Aug-18 12:12:26.525120
EXPOSE 8080
2025-Aug-18 12:12:26.525120
2025-Aug-18 12:12:26.525120
# Use uv to run the application
2025-Aug-18 12:12:26.525120
CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080"]
2025-Aug-18 12:12:26.693594
----------------------------------------
2025-Aug-18 12:12:26.698393
Building docker image started.
2025-Aug-18 12:12:26.702728
To check the current progress, click on Show Debug Logs.
2025-Aug-18 12:12:27.036946
[CMD]: docker exec pcwwc0w8gc0ck4gsgsk0cg4o bash -c 'cat /artifacts/build.sh'
2025-Aug-18 12:12:27.036946
docker build --no-cache  --add-host coolify:******** --add-host coolify-db:******** --add-host coolify-realtime:******** --add-host coolify-redis:******** --network host -f /artifacts/pcwwc0w8gc0ck4gsgsk0cg4o/Dockerfile --build-arg SOURCE_COMMIT='1cde377098244ef4e29b09c3a5d4139345491123' --build-arg 'COOLIFY_URL=http://rbu.anesa.dev' --build-arg 'COOLIFY_FQDN=rbu.anesa.dev' --build-arg 'COOLIFY_BRANCH=master' --build-arg 'COOLIFY_RESOURCE_UUID=uwcw80cc400scwckgwo8wo0g' --build-arg 'COOLIFY_CONTAINER_NAME=uwcw80cc400scwckgwo8wo0g-121218881051' --progress plain -t uwcw80cc400scwckgwo8wo0g:1cde377098244ef4e29b09c3a5d4139345491123 /artifacts/pcwwc0w8gc0ck4gsgsk0cg4o
2025-Aug-18 12:12:27.965726
[CMD]: docker exec pcwwc0w8gc0ck4gsgsk0cg4o bash -c 'bash /artifacts/build.sh'
2025-Aug-18 12:12:27.965726
#0 building with "default" instance using docker driver
2025-Aug-18 12:12:27.965726
2025-Aug-18 12:12:27.965726
#1 [internal] load build definition from Dockerfile
2025-Aug-18 12:12:27.965726
#1 transferring dockerfile: 1.29kB done
2025-Aug-18 12:12:27.965726
#1 DONE 0.0s
2025-Aug-18 12:12:27.965726
2025-Aug-18 12:12:27.965726
#2 [internal] load metadata for docker.io/library/python:3.12-slim
2025-Aug-18 12:12:29.169603
#2 DONE 1.4s
2025-Aug-18 12:12:29.169603
2025-Aug-18 12:12:29.169603
#3 [internal] load metadata for ghcr.io/astral-sh/uv:latest
2025-Aug-18 12:12:29.278109
#3 DONE 1.5s
2025-Aug-18 12:12:29.378676
#4 [internal] load .dockerignore
2025-Aug-18 12:12:29.378676
#4 transferring context: 2B done
2025-Aug-18 12:12:29.378676
#4 DONE 0.0s
2025-Aug-18 12:12:29.378676
2025-Aug-18 12:12:29.378676
#5 [stage-0 1/9] FROM docker.io/library/python:3.12-slim@sha256:d67a7b66b989ad6b6d6b10d428dcc5e0bfc3e5f88906e67d490c4d3daac57047
2025-Aug-18 12:12:29.378676
#5 resolve docker.io/library/python:3.12-slim@sha256:d67a7b66b989ad6b6d6b10d428dcc5e0bfc3e5f88906e67d490c4d3daac57047 0.0s done
2025-Aug-18 12:12:29.574265
#5 ...
2025-Aug-18 12:12:29.574265
2025-Aug-18 12:12:29.574265
#6 [internal] load build context
2025-Aug-18 12:12:29.574265
#6 transferring context: 350.10kB 0.0s done
2025-Aug-18 12:12:29.574265
#6 DONE 0.1s
2025-Aug-18 12:12:29.574265
2025-Aug-18 12:12:29.574265
#5 [stage-0 1/9] FROM docker.io/library/python:3.12-slim@sha256:d67a7b66b989ad6b6d6b10d428dcc5e0bfc3e5f88906e67d490c4d3daac57047
2025-Aug-18 12:12:29.574265
#5 sha256:960281d68bf0f3705eb82c5694af05c40f74e6336b9e9ae4643f134de19f1b9c 5.59kB / 5.59kB done
2025-Aug-18 12:12:29.574265
#5 sha256:9a6263cdeaa5d408640880103ee36920ef814974ca8e2674412ad6460e8968c9 0B / 30.14MB 0.2s
2025-Aug-18 12:12:29.574265
#5 sha256:d67a7b66b989ad6b6d6b10d428dcc5e0bfc3e5f88906e67d490c4d3daac57047 10.37kB / 10.37kB done
2025-Aug-18 12:12:29.574265
#5 sha256:8f5e6d99738f82eed0a35db3adf5e20358c324b62a5f25af486928867a028e66 1.75kB / 1.75kB done
2025-Aug-18 12:12:29.672263
#5 sha256:3cd5add3ba3330f959ed1b27096bfb25e2f29a4cf327009d69855a903036f3af 0B / 1.27MB 0.3s
2025-Aug-18 12:12:29.672263
#5 sha256:7b5dfa58994b71320b90f4e4e2ced96f1ae3f6468038e81cf1e27a0e5d7b3c8d 0B / 12.04MB 0.3s
2025-Aug-18 12:12:29.798863
#5 sha256:9a6263cdeaa5d408640880103ee36920ef814974ca8e2674412ad6460e8968c9 5.24MB / 30.14MB 0.4s
2025-Aug-18 12:12:29.904823
#5 sha256:9a6263cdeaa5d408640880103ee36920ef814974ca8e2674412ad6460e8968c9 12.58MB / 30.14MB 0.5s
2025-Aug-18 12:12:30.052858
#5 sha256:9a6263cdeaa5d408640880103ee36920ef814974ca8e2674412ad6460e8968c9 18.87MB / 30.14MB 0.6s
2025-Aug-18 12:12:30.151169
#5 sha256:9a6263cdeaa5d408640880103ee36920ef814974ca8e2674412ad6460e8968c9 25.48MB / 30.14MB 0.7s
2025-Aug-18 12:12:30.151169
#5 sha256:3cd5add3ba3330f959ed1b27096bfb25e2f29a4cf327009d69855a903036f3af 1.27MB / 1.27MB 0.6s done
2025-Aug-18 12:12:30.151169
#5 sha256:36d983c178a06f2be8270a47a21f02ba9618771919e20e0ba847592826b79c2a 0B / 249B 0.7s
2025-Aug-18 12:12:30.286825
#5 sha256:9a6263cdeaa5d408640880103ee36920ef814974ca8e2674412ad6460e8968c9 30.14MB / 30.14MB 0.7s done
2025-Aug-18 12:12:30.286825
#5 sha256:7b5dfa58994b71320b90f4e4e2ced96f1ae3f6468038e81cf1e27a0e5d7b3c8d 12.04MB / 12.04MB 0.9s
2025-Aug-18 12:12:30.286825
#5 extracting sha256:9a6263cdeaa5d408640880103ee36920ef814974ca8e2674412ad6460e8968c9
2025-Aug-18 12:12:30.385535
#5 sha256:7b5dfa58994b71320b90f4e4e2ced96f1ae3f6468038e81cf1e27a0e5d7b3c8d 12.04MB / 12.04MB 0.9s done
2025-Aug-18 12:12:30.385535
#5 sha256:36d983c178a06f2be8270a47a21f02ba9618771919e20e0ba847592826b79c2a 249B / 249B 0.9s done
2025-Aug-18 12:12:31.039508
#5 ...
2025-Aug-18 12:12:31.039508
2025-Aug-18 12:12:31.039508
#7 FROM ghcr.io/astral-sh/uv:latest@sha256:8101ad825250a114e7bef89eefaa73c31e34e10ffbe5aff01562740bac97553c
2025-Aug-18 12:12:31.039508
#7 resolve ghcr.io/astral-sh/uv:latest@sha256:8101ad825250a114e7bef89eefaa73c31e34e10ffbe5aff01562740bac97553c 0.0s done
2025-Aug-18 12:12:31.039508
#7 sha256:8101ad825250a114e7bef89eefaa73c31e34e10ffbe5aff01562740bac97553c 2.19kB / 2.19kB done
2025-Aug-18 12:12:31.039508
#7 sha256:b06eb096b84c24d6461013a8419653c7c4d2319ac654d5eb3a4e86f543d8990b 669B / 669B done
2025-Aug-18 12:12:31.039508
#7 sha256:9d1ca2305d96406febc1337cc23300de9e212d4ae55e49898f94b11ec0aa4958 1.30kB / 1.30kB done
2025-Aug-18 12:12:31.039508
#7 sha256:7b33eb96cdab840115fb426ed199fefd757a2264dc88570221d62228c9fc8dfc 18.45MB / 18.45MB 0.2s done
2025-Aug-18 12:12:31.039508
#7 sha256:6d3d194a07a996d9c394e9be87c64752b7e89fbc3e4674b6c64e2828c959be42 98B / 98B 0.2s done
2025-Aug-18 12:12:31.039508
#7 extracting sha256:7b33eb96cdab840115fb426ed199fefd757a2264dc88570221d62228c9fc8dfc 1.2s done
2025-Aug-18 12:12:31.039508
#7 extracting sha256:6d3d194a07a996d9c394e9be87c64752b7e89fbc3e4674b6c64e2828c959be42 done
2025-Aug-18 12:12:31.039508
#7 DONE 1.6s
2025-Aug-18 12:12:31.039508
2025-Aug-18 12:12:31.039508
#5 [stage-0 1/9] FROM docker.io/library/python:3.12-slim@sha256:d67a7b66b989ad6b6d6b10d428dcc5e0bfc3e5f88906e67d490c4d3daac57047
2025-Aug-18 12:12:32.606745
#5 extracting sha256:9a6263cdeaa5d408640880103ee36920ef814974ca8e2674412ad6460e8968c9 2.2s done
2025-Aug-18 12:12:32.606745
#5 extracting sha256:3cd5add3ba3330f959ed1b27096bfb25e2f29a4cf327009d69855a903036f3af 0.1s
2025-Aug-18 12:12:32.807786
#5 extracting sha256:3cd5add3ba3330f959ed1b27096bfb25e2f29a4cf327009d69855a903036f3af 0.2s done
2025-Aug-18 12:12:32.807786
#5 extracting sha256:7b5dfa58994b71320b90f4e4e2ced96f1ae3f6468038e81cf1e27a0e5d7b3c8d 0.1s
2025-Aug-18 12:12:33.665372
#5 extracting sha256:7b5dfa58994b71320b90f4e4e2ced96f1ae3f6468038e81cf1e27a0e5d7b3c8d 0.9s done
2025-Aug-18 12:12:33.665372
#5 extracting sha256:36d983c178a06f2be8270a47a21f02ba9618771919e20e0ba847592826b79c2a done
2025-Aug-18 12:12:33.890040
#5 DONE 4.4s
2025-Aug-18 12:12:33.890040
2025-Aug-18 12:12:33.890040
#8 [stage-0 2/9] COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /usr/local/bin/
2025-Aug-18 12:12:33.977051
#8 DONE 0.2s
2025-Aug-18 12:12:34.129322
#9 [stage-0 3/9] RUN apt-get update && apt-get install -y     wget gnupg libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1     libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2     libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0     libwayland-server0 libxshmfence1 libegl1     libpoppler-cpp-dev libpoppler-dev poppler-utils     build-essential  && rm -rf /var/lib/apt/lists/*
2025-Aug-18 12:12:34.129322
#9 0.148 Hit:1 http://deb.debian.org/debian trixie InRelease
2025-Aug-18 12:12:34.259957
#9 0.150 Get:2 http://deb.debian.org/debian trixie-updates InRelease [47.1 kB]
2025-Aug-18 12:12:34.259957
#9 0.152 Get:3 http://deb.debian.org/debian-security trixie-security InRelease [43.4 kB]
2025-Aug-18 12:12:34.259957
#9 0.179 Get:4 http://deb.debian.org/debian trixie/main arm64 Packages [9604 kB]
2025-Aug-18 12:12:34.259957
#9 0.278 Get:5 http://deb.debian.org/debian trixie-updates/main arm64 Packages [2432 B]
2025-Aug-18 12:12:34.410621
#9 0.279 Get:6 http://deb.debian.org/debian-security trixie-security/main arm64 Packages [9464 B]
2025-Aug-18 12:12:35.244728
#9 1.266 Fetched 9706 kB in 1s (8449 kB/s)
2025-Aug-18 12:12:35.244728
#9 1.266 Reading package lists...
2025-Aug-18 12:12:35.990665
2025-Aug-18 12:12:36.179994
#9 2.050 Reading package lists...
2025-Aug-18 12:12:36.800261
2025-Aug-18 12:12:36.966945
#9 2.836 Building dependency tree...
2025-Aug-18 12:12:37.074499
2025-Aug-18 12:12:37.080818
#9 3.094 Reading state information...
2025-Aug-18 12:12:37.719631
#9 3.739 The following additional packages will be installed:
2025-Aug-18 12:12:37.719631
#9 3.739   alsa-topology-conf alsa-ucm-conf at-spi2-common at-spi2-core binutils
2025-Aug-18 12:12:37.719631
#9 3.739   binutils-aarch64-linux-gnu binutils-common bzip2 cpp cpp-14
2025-Aug-18 12:12:37.719631
#9 3.739   cpp-14-aarch64-linux-gnu cpp-aarch64-linux-gnu dbus dbus-bin dbus-daemon
2025-Aug-18 12:12:37.719631
#9 3.739   dbus-session-bus-common dbus-system-bus-common dbus-user-session
2025-Aug-18 12:12:37.879525
#9 3.740   dconf-gsettings-backend dconf-service dirmngr dmsetup dpkg-dev fakeroot
2025-Aug-18 12:12:37.879525
#9 3.740   fontconfig fontconfig-config fonts-dejavu-core fonts-dejavu-mono g++ g++-14
2025-Aug-18 12:12:37.879525
#9 3.740   g++-14-aarch64-linux-gnu g++-aarch64-linux-gnu gcc gcc-14
2025-Aug-18 12:12:37.879525
#9 3.740   gcc-14-aarch64-linux-gnu gcc-aarch64-linux-gnu gnupg-l10n gnupg-utils gpg
2025-Aug-18 12:12:37.879525
#9 3.740   gpg-agent gpg-wks-client gpgconf gpgsm gpgv gsettings-desktop-schemas
2025-Aug-18 12:12:37.879525
#9 3.740   krb5-locales libalgorithm-diff-perl libalgorithm-diff-xs-perl
2025-Aug-18 12:12:37.879525
#9 3.740   libalgorithm-merge-perl libapparmor1 libasan8 libasound2-data libassuan9
2025-Aug-18 12:12:37.879525
#9 3.740   libatomic1 libatspi2.0-0t64 libavahi-client3 libavahi-common-data
2025-Aug-18 12:12:37.879525
#9 3.741   libavahi-common3 libbinutils libbrotli1 libc-dev-bin libc6-dev libcairo2
2025-Aug-18 12:12:37.879525
#9 3.741   libcc1-0 libcom-err2 libcrypt-dev libcryptsetup12 libctf-nobfd0 libctf0
2025-Aug-18 12:12:37.879525
#9 3.741   libcurl3t64-gnutls libdatrie1 libdbus-1-3 libdconf1 libdeflate0
2025-Aug-18 12:12:37.879525
#9 3.741   libdevmapper1.02.1 libdpkg-perl libdrm-amdgpu1 libdrm-common libedit2
2025-Aug-18 12:12:37.879525
#9 3.741   libegl-mesa0 libelf1t64 libexpat1 libfakeroot libfile-fcntllock-perl
2025-Aug-18 12:12:37.879525
#9 3.741   libfontconfig1 libfreetype6 libfribidi0 libgcc-14-dev libgcrypt20
2025-Aug-18 12:12:37.879525
#9 3.741   libgdbm-compat4t64 libglib2.0-0t64 libglib2.0-data libglvnd0 libgnutls30t64
2025-Aug-18 12:12:37.879525
#9 3.741   libgomp1 libgpg-error-l10n libgpg-error0 libgpgme11t64 libgpgmepp6t64
2025-Aug-18 12:12:37.879525
#9 3.741   libgprofng0 libgraphite2-3 libgssapi-krb5-2 libharfbuzz0b libhwasan0
2025-Aug-18 12:12:37.879525
#9 3.741   libidn2-0 libisl23 libitm1 libjansson4 libjbig0 libjpeg62-turbo libjson-c5
2025-Aug-18 12:12:37.879525
#9 3.741   libk5crypto3 libkeyutils1 libkmod2 libkrb5-3 libkrb5support0 libksba8
2025-Aug-18 12:12:37.879525
#9 3.741   liblcms2-2 libldap-common libldap2 liblerc4 libllvm19 liblocale-gettext-perl
2025-Aug-18 12:12:37.879525
#9 3.741   liblsan0 libmpc3 libmpfr6 libnghttp2-14 libnghttp3-9 libngtcp2-16
2025-Aug-18 12:12:37.879525
#9 3.741   libngtcp2-crypto-gnutls8 libnpth0t64 libnspr4 libnss-systemd libopenjp2-7
2025-Aug-18 12:12:37.879525
#9 3.741   libp11-kit0 libpam-systemd libpangoft2-1.0-0 libperl5.40 libpixman-1-0
2025-Aug-18 12:12:37.879525
#9 3.744   libpng16-16t64 libpoppler-cpp2 libpoppler147 libproc2-0 libpsl5t64 librtmp1
2025-Aug-18 12:12:37.879525
#9 3.744   libsasl2-2 libsasl2-modules libsasl2-modules-db libsensors-config
2025-Aug-18 12:12:37.879525
#9 3.744   libsensors5 libsframe1 libsharpyuv0 libssh2-1t64 libstdc++-14-dev
2025-Aug-18 12:12:37.879525
#9 3.744   libsystemd-shared libtasn1-6 libthai-data libthai0 libtiff6 libtsan2
2025-Aug-18 12:12:37.879525
#9 3.748   libubsan1 libunistring5 libwebp7 libx11-6 libx11-data libxau6 libxcb-dri3-0
2025-Aug-18 12:12:37.879525
#9 3.748   libxcb-present0 libxcb-randr0 libxcb-render0 libxcb-shm0 libxcb-sync1
2025-Aug-18 12:12:37.879525
#9 3.748   libxcb-xfixes0 libxcb1 libxdmcp6 libxext6 libxfixes3 libxi6 libxml2
2025-Aug-18 12:12:37.879525
#9 3.748   libxrender1 libxtst6 libz3-4 linux-libc-dev linux-sysctl-defaults make
2025-Aug-18 12:12:37.879525
#9 3.748   manpages manpages-dev mesa-libgallium patch perl perl-modules-5.40
2025-Aug-18 12:12:37.879525
#9 3.748   pinentry-curses poppler-data procps psmisc publicsuffix rpcsvc-proto
2025-Aug-18 12:12:37.879525
#9 3.748   shared-mime-info systemd systemd-cryptsetup systemd-sysv systemd-timesyncd
2025-Aug-18 12:12:37.879525
#9 3.748   x11-common xdg-user-dirs xz-utils
2025-Aug-18 12:12:37.879525
#9 3.748 Suggested packages:
2025-Aug-18 12:12:37.879525
#9 3.748   binutils-doc gprofng-gui binutils-gold bzip2-doc cpp-doc gcc-14-locales
2025-Aug-18 12:12:37.879525
#9 3.748   cpp-14-doc pinentry-gnome3 tor debian-keyring debian-tag2upload-keyring
2025-Aug-18 12:12:37.879525
#9 3.748   gcc-14-doc gcc-multilib autoconf automake libtool flex bison gdb gcc-doc
2025-Aug-18 12:12:37.879525
#9 3.748   gdb-aarch64-linux-gnu gpg-wks-server parcimonie xloadimage scdaemon
2025-Aug-18 12:12:37.879525
#9 3.748   tpm2daemon alsa-utils libasound2-plugins libc-devtools glibc-doc cups-common
2025-Aug-18 12:12:37.879525
#9 3.748   sensible-utils git bzr rng-tools low-memory-monitor gnutls-bin krb5-doc
2025-Aug-18 12:12:37.879525
#9 3.748   krb5-user liblcms2-utils libtss2-rc0t64 libsasl2-modules-gssapi-mit
2025-Aug-18 12:12:37.879525
#9 3.748   | libsasl2-modules-gssapi-heimdal libsasl2-modules-ldap libsasl2-modules-otp
2025-Aug-18 12:12:37.879525
#9 3.748   libsasl2-modules-sql lm-sensors libstdc++-14-doc libarchive13t64 libbpf1
2025-Aug-18 12:12:37.879525
#9 3.748   libdw1t64 libfido2-1 libip4tc2 libpwquality1 libqrencode4 make-doc
2025-Aug-18 12:12:37.879525
#9 3.748   man-browser ed diffutils-doc perl-doc libterm-readline-gnu-perl
2025-Aug-18 12:12:37.879525
#9 3.748   | libterm-readline-perl-perl libtap-harness-archive-perl pinentry-doc
2025-Aug-18 12:12:37.879525
#9 3.748   ghostscript fonts-japanese-mincho | fonts-ipafont-mincho
2025-Aug-18 12:12:37.879525
#9 3.748   fonts-japanese-gothic | fonts-ipafont-gothic fonts-arphic-ukai
2025-Aug-18 12:12:37.879525
#9 3.748   fonts-arphic-uming fonts-nanum systemd-container systemd-homed
2025-Aug-18 12:12:37.879525
#9 3.748   systemd-userdbd systemd-boot systemd-resolved systemd-repart
2025-Aug-18 12:12:37.879525
#9 3.748   libtss2-tcti-device0 polkitd
2025-Aug-18 12:12:38.833308
#9 4.854 The following NEW packages will be installed:
2025-Aug-18 12:12:38.965181
#9 4.855   alsa-topology-conf alsa-ucm-conf at-spi2-common at-spi2-core binutils
2025-Aug-18 12:12:38.965181
#9 4.855   binutils-aarch64-linux-gnu binutils-common build-essential bzip2 cpp cpp-14
2025-Aug-18 12:12:38.965181
#9 4.855   cpp-14-aarch64-linux-gnu cpp-aarch64-linux-gnu dbus dbus-bin dbus-daemon
2025-Aug-18 12:12:38.965181
#9 4.855   dbus-session-bus-common dbus-system-bus-common dbus-user-session
2025-Aug-18 12:12:38.965181
#9 4.855   dconf-gsettings-backend dconf-service dirmngr dmsetup dpkg-dev fakeroot
2025-Aug-18 12:12:38.965181
#9 4.855   fontconfig fontconfig-config fonts-dejavu-core fonts-dejavu-mono g++ g++-14
2025-Aug-18 12:12:38.965181
#9 4.855   g++-14-aarch64-linux-gnu g++-aarch64-linux-gnu gcc gcc-14
2025-Aug-18 12:12:38.965181
#9 4.855   gcc-14-aarch64-linux-gnu gcc-aarch64-linux-gnu gnupg gnupg-l10n gnupg-utils
2025-Aug-18 12:12:38.965181
#9 4.855   gpg gpg-agent gpg-wks-client gpgconf gpgsm gpgv gsettings-desktop-schemas
2025-Aug-18 12:12:38.965181
#9 4.855   krb5-locales libalgorithm-diff-perl libalgorithm-diff-xs-perl
2025-Aug-18 12:12:38.965181
#9 4.855   libalgorithm-merge-perl libapparmor1 libasan8 libasound2-data libasound2t64
2025-Aug-18 12:12:38.965181
#9 4.855   libassuan9 libatk-bridge2.0-0t64 libatk1.0-0t64 libatomic1 libatspi2.0-0t64
2025-Aug-18 12:12:38.965181
#9 4.855   libavahi-client3 libavahi-common-data libavahi-common3 libbinutils
2025-Aug-18 12:12:38.965181
#9 4.855   libbrotli1 libc-dev-bin libc6-dev libcairo2 libcc1-0 libcom-err2
2025-Aug-18 12:12:38.965181
#9 4.855   libcrypt-dev libcryptsetup12 libctf-nobfd0 libctf0 libcups2t64
2025-Aug-18 12:12:38.965181
#9 4.855   libcurl3t64-gnutls libdatrie1 libdbus-1-3 libdconf1 libdeflate0
2025-Aug-18 12:12:38.965181
#9 4.855   libdevmapper1.02.1 libdpkg-perl libdrm-amdgpu1 libdrm-common libdrm2
2025-Aug-18 12:12:38.965181
#9 4.855   libedit2 libegl-mesa0 libegl1 libelf1t64 libexpat1 libfakeroot
2025-Aug-18 12:12:38.965181
#9 4.855   libfile-fcntllock-perl libfontconfig1 libfreetype6 libfribidi0 libgbm1
2025-Aug-18 12:12:38.965181
#9 4.855   libgcc-14-dev libgcrypt20 libgdbm-compat4t64 libglib2.0-0t64 libglib2.0-data
2025-Aug-18 12:12:38.965181
#9 4.855   libglvnd0 libgnutls30t64 libgomp1 libgpg-error-l10n libgpg-error0
2025-Aug-18 12:12:38.965181
#9 4.855   libgpgme11t64 libgpgmepp6t64 libgprofng0 libgraphite2-3 libgssapi-krb5-2
2025-Aug-18 12:12:38.965181
#9 4.855   libharfbuzz0b libhwasan0 libidn2-0 libisl23 libitm1 libjansson4 libjbig0
2025-Aug-18 12:12:38.965181
#9 4.855   libjpeg62-turbo libjson-c5 libk5crypto3 libkeyutils1 libkmod2 libkrb5-3
2025-Aug-18 12:12:38.965181
#9 4.855   libkrb5support0 libksba8 liblcms2-2 libldap-common libldap2 liblerc4
2025-Aug-18 12:12:38.965181
#9 4.857   libllvm19 liblocale-gettext-perl liblsan0 libmpc3 libmpfr6 libnghttp2-14
2025-Aug-18 12:12:38.965181
#9 4.857   libnghttp3-9 libngtcp2-16 libngtcp2-crypto-gnutls8 libnpth0t64 libnspr4
2025-Aug-18 12:12:38.965181
#9 4.857   libnss-systemd libnss3 libopenjp2-7 libp11-kit0 libpam-systemd
2025-Aug-18 12:12:38.965181
#9 4.857   libpango-1.0-0 libpangocairo-1.0-0 libpangoft2-1.0-0 libperl5.40
2025-Aug-18 12:12:38.965181
#9 4.857   libpixman-1-0 libpng16-16t64 libpoppler-cpp-dev libpoppler-cpp2
2025-Aug-18 12:12:38.965181
#9 4.857   libpoppler-dev libpoppler147 libproc2-0 libpsl5t64 librtmp1 libsasl2-2
2025-Aug-18 12:12:38.965181
#9 4.857   libsasl2-modules libsasl2-modules-db libsensors-config libsensors5
2025-Aug-18 12:12:38.965181
#9 4.857   libsframe1 libsharpyuv0 libssh2-1t64 libstdc++-14-dev libsystemd-shared
2025-Aug-18 12:12:38.965181
#9 4.857   libtasn1-6 libthai-data libthai0 libtiff6 libtsan2 libubsan1 libunistring5
2025-Aug-18 12:12:38.965181
#9 4.857   libwayland-client0 libwayland-server0 libwebp7 libx11-6 libx11-data
2025-Aug-18 12:12:38.965181
#9 4.857   libx11-xcb1 libxau6 libxcb-dri3-0 libxcb-present0 libxcb-randr0
2025-Aug-18 12:12:38.965181
#9 4.857   libxcb-render0 libxcb-shm0 libxcb-sync1 libxcb-xfixes0 libxcb1
2025-Aug-18 12:12:38.965181
#9 4.857   libxcomposite1 libxcursor1 libxdamage1 libxdmcp6 libxext6 libxfixes3 libxi6
2025-Aug-18 12:12:38.965181
#9 4.857   libxml2 libxrandr2 libxrender1 libxshmfence1 libxtst6 libz3-4 linux-libc-dev
2025-Aug-18 12:12:38.965181
#9 4.857   linux-sysctl-defaults make manpages manpages-dev mesa-libgallium patch perl
2025-Aug-18 12:12:38.965181
#9 4.857   perl-modules-5.40 pinentry-curses poppler-data poppler-utils procps psmisc
2025-Aug-18 12:12:38.965181
#9 4.857   publicsuffix rpcsvc-proto shared-mime-info systemd systemd-cryptsetup
2025-Aug-18 12:12:38.965181
#9 4.858   systemd-sysv systemd-timesyncd wget x11-common xdg-user-dirs xz-utils
2025-Aug-18 12:12:38.965181
#9 4.950 0 upgraded, 229 newly installed, 0 to remove and 0 not upgraded.
2025-Aug-18 12:12:38.965181
#9 4.950 Need to get 156 MB of archives.
2025-Aug-18 12:12:38.965181
#9 4.950 After this operation, 675 MB of additional disk space will be used.
2025-Aug-18 12:12:38.965181
#9 4.950 Get:1 http://deb.debian.org/debian trixie/main arm64 libsystemd-shared arm64 257.7-1 [1916 kB]
2025-Aug-18 12:12:38.965181
#9 4.986 Get:2 http://deb.debian.org/debian trixie/main arm64 libapparmor1 arm64 4.1.0-1 [42.9 kB]
2025-Aug-18 12:12:39.086475
#9 4.990 Get:3 http://deb.debian.org/debian trixie/main arm64 systemd arm64 257.7-1 [2930 kB]
2025-Aug-18 12:12:39.091911
#9 5.013 Get:4 http://deb.debian.org/debian trixie/main arm64 systemd-sysv arm64 257.7-1 [64.2 kB]
2025-Aug-18 12:12:39.091911
#9 5.013 Get:5 http://deb.debian.org/debian trixie/main arm64 libdbus-1-3 arm64 1.16.2-2 [169 kB]
2025-Aug-18 12:12:39.091911
#9 5.016 Get:6 http://deb.debian.org/debian trixie/main arm64 dbus-bin arm64 1.16.2-2 [78.8 kB]
2025-Aug-18 12:12:39.091911
#9 5.016 Get:7 http://deb.debian.org/debian trixie/main arm64 dbus-session-bus-common all 1.16.2-2 [52.3 kB]
2025-Aug-18 12:12:39.091911
#9 5.018 Get:8 http://deb.debian.org/debian trixie/main arm64 libexpat1 arm64 2.7.1-2 [93.3 kB]
2025-Aug-18 12:12:39.091911
#9 5.019 Get:9 http://deb.debian.org/debian trixie/main arm64 dbus-daemon arm64 1.16.2-2 [152 kB]
2025-Aug-18 12:12:39.091911
#9 5.020 Get:10 http://deb.debian.org/debian trixie/main arm64 dbus-system-bus-common all 1.16.2-2 [53.5 kB]
2025-Aug-18 12:12:39.091911
#9 5.021 Get:11 http://deb.debian.org/debian trixie/main arm64 dbus arm64 1.16.2-2 [70.7 kB]
2025-Aug-18 12:12:39.091911
#9 5.023 Get:12 http://deb.debian.org/debian trixie/main arm64 liblocale-gettext-perl arm64 1.07-7+b1 [15.2 kB]
2025-Aug-18 12:12:39.091911
#9 5.024 Get:13 http://deb.debian.org/debian trixie/main arm64 poppler-data all 0.4.12-1 [1601 kB]
2025-Aug-18 12:12:39.091911
#9 5.038 Get:14 http://deb.debian.org/debian trixie/main arm64 linux-sysctl-defaults all 4.12 [5624 B]
2025-Aug-18 12:12:39.091911
#9 5.038 Get:15 http://deb.debian.org/debian trixie/main arm64 libproc2-0 arm64 2:4.0.4-9 [62.8 kB]
2025-Aug-18 12:12:39.091911
#9 5.039 Get:16 http://deb.debian.org/debian trixie/main arm64 procps arm64 2:4.0.4-9 [871 kB]
2025-Aug-18 12:12:39.091911
#9 5.043 Get:17 http://deb.debian.org/debian trixie/main arm64 bzip2 arm64 1.0.8-6 [39.5 kB]
2025-Aug-18 12:12:39.091911
#9 5.044 Get:18 http://deb.debian.org/debian trixie/main arm64 krb5-locales all 1.21.3-5 [101 kB]
2025-Aug-18 12:12:39.091911
#9 5.045 Get:19 http://deb.debian.org/debian trixie/main arm64 libnss-systemd arm64 257.7-1 [202 kB]
2025-Aug-18 12:12:39.091911
#9 5.047 Get:20 http://deb.debian.org/debian trixie/main arm64 libpam-systemd arm64 257.7-1 [275 kB]
2025-Aug-18 12:12:39.091911
#9 5.051 Get:21 http://deb.debian.org/debian trixie/main arm64 manpages all 6.9.1-1 [1393 kB]
2025-Aug-18 12:12:39.091911
#9 5.062 Get:22 http://deb.debian.org/debian trixie/main arm64 perl-modules-5.40 all 5.40.1-6 [3019 kB]
2025-Aug-18 12:12:39.091911
#9 5.076 Get:23 http://deb.debian.org/debian trixie/main arm64 libgdbm-compat4t64 arm64 1.24-2 [50.3 kB]
2025-Aug-18 12:12:39.091911
#9 5.076 Get:24 http://deb.debian.org/debian trixie/main arm64 libperl5.40 arm64 5.40.1-6 [4142 kB]
2025-Aug-18 12:12:39.091911
#9 5.106 Get:25 http://deb.debian.org/debian trixie/main arm64 perl arm64 5.40.1-6 [267 kB]
2025-Aug-18 12:12:39.188771
#9 5.107 Get:26 http://deb.debian.org/debian trixie/main arm64 systemd-timesyncd arm64 257.7-1 [90.8 kB]
2025-Aug-18 12:12:39.188771
#9 5.107 Get:27 http://deb.debian.org/debian trixie/main arm64 libunistring5 arm64 1.3-2 [453 kB]
2025-Aug-18 12:12:39.188771
#9 5.113 Get:28 http://deb.debian.org/debian trixie/main arm64 libidn2-0 arm64 2.3.8-2 [107 kB]
2025-Aug-18 12:12:39.188771
#9 5.113 Get:29 http://deb.debian.org/debian trixie/main arm64 libp11-kit0 arm64 0.25.5-3 [409 kB]
2025-Aug-18 12:12:39.188771
#9 5.116 Get:30 http://deb.debian.org/debian trixie/main arm64 libtasn1-6 arm64 4.20.0-2 [47.3 kB]
2025-Aug-18 12:12:39.188771
#9 5.116 Get:31 http://deb.debian.org/debian trixie/main arm64 libgnutls30t64 arm64 3.8.9-3 [1375 kB]
2025-Aug-18 12:12:39.188771
#9 5.124 Get:32 http://deb.debian.org/debian trixie/main arm64 libpsl5t64 arm64 0.21.2-1.1+b1 [57.1 kB]
2025-Aug-18 12:12:39.188771
#9 5.124 Get:33 http://deb.debian.org/debian trixie/main arm64 wget arm64 1.25.0-2 [970 kB]
2025-Aug-18 12:12:39.188771
#9 5.129 Get:34 http://deb.debian.org/debian trixie/main arm64 xz-utils arm64 5.8.1-1 [657 kB]
2025-Aug-18 12:12:39.188771
#9 5.133 Get:35 http://deb.debian.org/debian trixie/main arm64 alsa-topology-conf all 1.2.5.1-3 [15.3 kB]
2025-Aug-18 12:12:39.188771
#9 5.133 Get:36 http://deb.debian.org/debian trixie/main arm64 libasound2-data all 1.2.14-1 [21.1 kB]
2025-Aug-18 12:12:39.188771
#9 5.135 Get:37 http://deb.debian.org/debian trixie/main arm64 libasound2t64 arm64 1.2.14-1 [342 kB]
2025-Aug-18 12:12:39.188771
#9 5.137 Get:38 http://deb.debian.org/debian trixie/main arm64 alsa-ucm-conf all 1.2.14-1 [92.5 kB]
2025-Aug-18 12:12:39.188771
#9 5.139 Get:39 http://deb.debian.org/debian trixie/main arm64 at-spi2-common all 2.56.2-1 [171 kB]
2025-Aug-18 12:12:39.188771
#9 5.140 Get:40 http://deb.debian.org/debian trixie/main arm64 libatomic1 arm64 14.2.0-19 [10.1 kB]
2025-Aug-18 12:12:39.188771
#9 5.141 Get:41 http://deb.debian.org/debian trixie/main arm64 libglib2.0-0t64 arm64 2.84.3-1 [1426 kB]
2025-Aug-18 12:12:39.188771
#9 5.147 Get:42 http://deb.debian.org/debian trixie/main arm64 libxau6 arm64 1:1.0.11-1 [20.6 kB]
2025-Aug-18 12:12:39.188771
#9 5.147 Get:43 http://deb.debian.org/debian trixie/main arm64 libxdmcp6 arm64 1:1.1.5-1 [27.8 kB]
2025-Aug-18 12:12:39.188771
#9 5.147 Get:44 http://deb.debian.org/debian trixie/main arm64 libxcb1 arm64 1.17.0-2+b1 [143 kB]
2025-Aug-18 12:12:39.188771
#9 5.149 Get:45 http://deb.debian.org/debian trixie/main arm64 libx11-data all 2:1.8.12-1 [343 kB]
2025-Aug-18 12:12:39.188771
#9 5.152 Get:46 http://deb.debian.org/debian trixie/main arm64 libx11-6 arm64 2:1.8.12-1 [795 kB]
2025-Aug-18 12:12:39.188771
#9 5.156 Get:47 http://deb.debian.org/debian trixie/main arm64 libxext6 arm64 2:1.3.4-1+b3 [49.2 kB]
2025-Aug-18 12:12:39.188771
#9 5.156 Get:48 http://deb.debian.org/debian trixie/main arm64 libxi6 arm64 2:1.8.2-1 [77.8 kB]
2025-Aug-18 12:12:39.188771
#9 5.159 Get:49 http://deb.debian.org/debian trixie/main arm64 libatspi2.0-0t64 arm64 2.56.2-1 [76.5 kB]
2025-Aug-18 12:12:39.188771
#9 5.160 Get:50 http://deb.debian.org/debian trixie/main arm64 x11-common all 1:7.7+24 [217 kB]
2025-Aug-18 12:12:39.188771
#9 5.165 Get:51 http://deb.debian.org/debian trixie/main arm64 libxtst6 arm64 2:1.2.5-1 [25.7 kB]
2025-Aug-18 12:12:39.188771
#9 5.170 Get:52 http://deb.debian.org/debian trixie/main arm64 dbus-user-session arm64 1.16.2-2 [52.1 kB]
2025-Aug-18 12:12:39.188771
#9 5.172 Get:53 http://deb.debian.org/debian trixie/main arm64 libdconf1 arm64 0.40.0-5 [40.4 kB]
2025-Aug-18 12:12:39.188771
#9 5.174 Get:54 http://deb.debian.org/debian trixie/main arm64 dconf-service arm64 0.40.0-5 [30.9 kB]
2025-Aug-18 12:12:39.188771
#9 5.177 Get:55 http://deb.debian.org/debian trixie/main arm64 dconf-gsettings-backend arm64 0.40.0-5 [27.3 kB]
2025-Aug-18 12:12:39.188771
#9 5.184 Get:56 http://deb.debian.org/debian trixie/main arm64 gsettings-desktop-schemas all 48.0-1 [700 kB]
2025-Aug-18 12:12:39.188771
#9 5.190 Get:57 http://deb.debian.org/debian trixie/main arm64 at-spi2-core arm64 2.56.2-1 [59.7 kB]
2025-Aug-18 12:12:39.188771
#9 5.190 Get:58 http://deb.debian.org/debian trixie/main arm64 libsframe1 arm64 2.44-3 [77.8 kB]
2025-Aug-18 12:12:39.188771
#9 5.193 Get:59 http://deb.debian.org/debian trixie/main arm64 binutils-common arm64 2.44-3 [2509 kB]
2025-Aug-18 12:12:39.188771
#9 5.204 Get:60 http://deb.debian.org/debian trixie/main arm64 libbinutils arm64 2.44-3 [660 kB]
2025-Aug-18 12:12:39.188771
#9 5.209 Get:61 http://deb.debian.org/debian trixie/main arm64 libgprofng0 arm64 2.44-3 [668 kB]
2025-Aug-18 12:12:39.308524
#9 5.216 Get:62 http://deb.debian.org/debian trixie/main arm64 libctf-nobfd0 arm64 2.44-3 [152 kB]
2025-Aug-18 12:12:39.308524
#9 5.218 Get:63 http://deb.debian.org/debian trixie/main arm64 libctf0 arm64 2.44-3 [84.2 kB]
2025-Aug-18 12:12:39.308524
#9 5.221 Get:64 http://deb.debian.org/debian trixie/main arm64 libjansson4 arm64 2.14-2+b3 [39.2 kB]
2025-Aug-18 12:12:39.308524
#9 5.221 Get:65 http://deb.debian.org/debian trixie/main arm64 binutils-aarch64-linux-gnu arm64 2.44-3 [820 kB]
2025-Aug-18 12:12:39.308524
#9 5.226 Get:66 http://deb.debian.org/debian trixie/main arm64 binutils arm64 2.44-3 [262 kB]
2025-Aug-18 12:12:39.308524
#9 5.228 Get:67 http://deb.debian.org/debian trixie/main arm64 libc-dev-bin arm64 2.41-12 [57.4 kB]
2025-Aug-18 12:12:39.308524
#9 5.229 Get:68 http://deb.debian.org/debian-security trixie-security/main arm64 linux-libc-dev all 6.12.41-1 [2637 kB]
2025-Aug-18 12:12:39.308524
#9 5.244 Get:69 http://deb.debian.org/debian trixie/main arm64 libcrypt-dev arm64 1:4.4.38-1 [123 kB]
2025-Aug-18 12:12:39.308524
#9 5.245 Get:70 http://deb.debian.org/debian trixie/main arm64 rpcsvc-proto arm64 1.4.3-1+b1 [60.5 kB]
2025-Aug-18 12:12:39.308524
#9 5.247 Get:71 http://deb.debian.org/debian trixie/main arm64 libc6-dev arm64 2.41-12 [1621 kB]
2025-Aug-18 12:12:39.308524
#9 5.255 Get:72 http://deb.debian.org/debian trixie/main arm64 libisl23 arm64 0.27-1 [601 kB]
2025-Aug-18 12:12:39.308524
#9 5.260 Get:73 http://deb.debian.org/debian trixie/main arm64 libmpfr6 arm64 4.2.2-1 [685 kB]
2025-Aug-18 12:12:39.308524
#9 5.263 Get:74 http://deb.debian.org/debian trixie/main arm64 libmpc3 arm64 1.3.1-1+b3 [50.5 kB]
2025-Aug-18 12:12:39.308524
#9 5.263 Get:75 http://deb.debian.org/debian trixie/main arm64 cpp-14-aarch64-linux-gnu arm64 14.2.0-19 [9169 kB]
2025-Aug-18 12:12:39.308524
#9 5.301 Get:76 http://deb.debian.org/debian trixie/main arm64 cpp-14 arm64 14.2.0-19 [1276 B]
2025-Aug-18 12:12:39.308524
#9 5.302 Get:77 http://deb.debian.org/debian trixie/main arm64 cpp-aarch64-linux-gnu arm64 4:14.2.0-1 [4832 B]
2025-Aug-18 12:12:39.308524
#9 5.303 Get:78 http://deb.debian.org/debian trixie/main arm64 cpp arm64 4:14.2.0-1 [1568 B]
2025-Aug-18 12:12:39.308524
#9 5.304 Get:79 http://deb.debian.org/debian trixie/main arm64 libcc1-0 arm64 14.2.0-19 [42.2 kB]
2025-Aug-18 12:12:39.308524
#9 5.305 Get:80 http://deb.debian.org/debian trixie/main arm64 libgomp1 arm64 14.2.0-19 [124 kB]
2025-Aug-18 12:12:39.308524
#9 5.306 Get:81 http://deb.debian.org/debian trixie/main arm64 libitm1 arm64 14.2.0-19 [24.2 kB]
2025-Aug-18 12:12:39.308524
#9 5.307 Get:82 http://deb.debian.org/debian trixie/main arm64 libasan8 arm64 14.2.0-19 [2578 kB]
2025-Aug-18 12:12:39.308524
#9 5.320 Get:83 http://deb.debian.org/debian trixie/main arm64 liblsan0 arm64 14.2.0-19 [1161 kB]
2025-Aug-18 12:12:39.416879
#9 5.327 Get:84 http://deb.debian.org/debian trixie/main arm64 libtsan2 arm64 14.2.0-19 [2383 kB]
2025-Aug-18 12:12:39.416879
#9 5.336 Get:85 http://deb.debian.org/debian trixie/main arm64 libubsan1 arm64 14.2.0-19 [1039 kB]
2025-Aug-18 12:12:39.416879
#9 5.341 Get:86 http://deb.debian.org/debian trixie/main arm64 libhwasan0 arm64 14.2.0-19 [1442 kB]
2025-Aug-18 12:12:39.416879
#9 5.349 Get:87 http://deb.debian.org/debian trixie/main arm64 libgcc-14-dev arm64 14.2.0-19 [2359 kB]
2025-Aug-18 12:12:39.416879
#9 5.359 Get:88 http://deb.debian.org/debian trixie/main arm64 gcc-14-aarch64-linux-gnu arm64 14.2.0-19 [17.7 MB]
2025-Aug-18 12:12:39.416879
#9 5.435 Get:89 http://deb.debian.org/debian trixie/main arm64 gcc-14 arm64 14.2.0-19 [529 kB]
2025-Aug-18 12:12:39.526042
#9 5.441 Get:90 http://deb.debian.org/debian trixie/main arm64 gcc-aarch64-linux-gnu arm64 4:14.2.0-1 [1440 B]
2025-Aug-18 12:12:39.526042
#9 5.442 Get:91 http://deb.debian.org/debian trixie/main arm64 gcc arm64 4:14.2.0-1 [5136 B]
2025-Aug-18 12:12:39.526042
#9 5.446 Get:92 http://deb.debian.org/debian trixie/main arm64 libstdc++-14-dev arm64 14.2.0-19 [2295 kB]
2025-Aug-18 12:12:39.526042
#9 5.451 Get:93 http://deb.debian.org/debian trixie/main arm64 g++-14-aarch64-linux-gnu arm64 14.2.0-19 [10.1 MB]
2025-Aug-18 12:12:39.526042
#9 5.498 Get:94 http://deb.debian.org/debian trixie/main arm64 g++-14 arm64 14.2.0-19 [22.5 kB]
2025-Aug-18 12:12:39.526042
#9 5.498 Get:95 http://deb.debian.org/debian trixie/main arm64 g++-aarch64-linux-gnu arm64 4:14.2.0-1 [1200 B]
2025-Aug-18 12:12:39.526042
#9 5.498 Get:96 http://deb.debian.org/debian trixie/main arm64 g++ arm64 4:14.2.0-1 [1332 B]
2025-Aug-18 12:12:39.526042
#9 5.498 Get:97 http://deb.debian.org/debian trixie/main arm64 make arm64 4.4.1-2 [452 kB]
2025-Aug-18 12:12:39.526042
#9 5.498 Get:98 http://deb.debian.org/debian trixie/main arm64 libdpkg-perl all 1.22.21 [650 kB]
2025-Aug-18 12:12:39.526042
#9 5.500 Get:99 http://deb.debian.org/debian trixie/main arm64 patch arm64 2.8-2 [128 kB]
2025-Aug-18 12:12:39.526042
#9 5.520 Get:100 http://deb.debian.org/debian trixie/main arm64 dpkg-dev all 1.22.21 [1338 kB]
2025-Aug-18 12:12:39.526042
#9 5.520 Get:101 http://deb.debian.org/debian trixie/main arm64 build-essential arm64 12.12 [4624 B]
2025-Aug-18 12:12:39.526042
#9 5.520 Get:102 http://deb.debian.org/debian trixie/main arm64 libgpg-error0 arm64 1.51-4 [78.5 kB]
2025-Aug-18 12:12:39.526042
#9 5.520 Get:103 http://deb.debian.org/debian trixie/main arm64 libassuan9 arm64 3.0.2-2 [59.1 kB]
2025-Aug-18 12:12:39.526042
#9 5.520 Get:104 http://deb.debian.org/debian trixie/main arm64 libgcrypt20 arm64 1.11.0-7 [742 kB]
2025-Aug-18 12:12:39.526042
#9 5.520 Get:105 http://deb.debian.org/debian trixie/main arm64 gpgconf arm64 2.4.7-21+b3 [121 kB]
2025-Aug-18 12:12:39.526042
#9 5.520 Get:106 http://deb.debian.org/debian trixie/main arm64 libksba8 arm64 1.6.7-2+b1 [125 kB]
2025-Aug-18 12:12:39.526042
#9 5.520 Get:107 http://deb.debian.org/debian trixie/main arm64 libsasl2-modules-db arm64 2.1.28+dfsg1-9 [20.1 kB]
2025-Aug-18 12:12:39.526042
#9 5.520 Get:108 http://deb.debian.org/debian trixie/main arm64 libsasl2-2 arm64 2.1.28+dfsg1-9 [55.6 kB]
2025-Aug-18 12:12:39.526042
#9 5.520 Get:109 http://deb.debian.org/debian trixie/main arm64 libldap2 arm64 2.6.10+dfsg-1 [179 kB]
2025-Aug-18 12:12:39.526042
#9 5.520 Get:110 http://deb.debian.org/debian trixie/main arm64 libnpth0t64 arm64 1.8-3 [22.9 kB]
2025-Aug-18 12:12:39.526042
#9 5.520 Get:111 http://deb.debian.org/debian trixie/main arm64 dirmngr arm64 2.4.7-21+b3 [359 kB]
2025-Aug-18 12:12:39.526042
#9 5.520 Get:112 http://deb.debian.org/debian trixie/main arm64 libdevmapper1.02.1 arm64 2:1.02.205-2 [118 kB]
2025-Aug-18 12:12:39.526042
#9 5.530 Get:113 http://deb.debian.org/debian trixie/main arm64 dmsetup arm64 2:1.02.205-2 [76.1 kB]
2025-Aug-18 12:12:39.526042
#9 5.530 Get:114 http://deb.debian.org/debian trixie/main arm64 libfakeroot arm64 ********-1 [29.6 kB]
2025-Aug-18 12:12:39.526042
#9 5.532 Get:115 http://deb.debian.org/debian trixie/main arm64 fakeroot arm64 ********-1 [75.4 kB]
2025-Aug-18 12:12:39.526042
#9 5.535 Get:116 http://deb.debian.org/debian trixie/main arm64 libbrotli1 arm64 1.1.0-2+b7 [308 kB]
2025-Aug-18 12:12:39.526042
#9 5.545 Get:117 http://deb.debian.org/debian trixie/main arm64 libpng16-16t64 arm64 1.6.48-1 [274 kB]
2025-Aug-18 12:12:39.624988
#9 5.550 Get:118 http://deb.debian.org/debian trixie/main arm64 libfreetype6 arm64 2.13.3+dfsg-1 [422 kB]
2025-Aug-18 12:12:39.624988
#9 5.558 Get:119 http://deb.debian.org/debian trixie/main arm64 fonts-dejavu-mono all 2.37-8 [489 kB]
2025-Aug-18 12:12:39.624988
#9 5.564 Get:120 http://deb.debian.org/debian trixie/main arm64 fonts-dejavu-core all 2.37-8 [840 kB]
2025-Aug-18 12:12:39.624988
#9 5.574 Get:121 http://deb.debian.org/debian trixie/main arm64 fontconfig-config arm64 2.15.0-2.3 [318 kB]
2025-Aug-18 12:12:39.624988
#9 5.578 Get:122 http://deb.debian.org/debian trixie/main arm64 libfontconfig1 arm64 2.15.0-2.3 [387 kB]
2025-Aug-18 12:12:39.624988
#9 5.580 Get:123 http://deb.debian.org/debian trixie/main arm64 fontconfig arm64 2.15.0-2.3 [463 kB]
2025-Aug-18 12:12:39.624988
#9 5.586 Get:124 http://deb.debian.org/debian trixie/main arm64 gnupg-l10n all 2.4.7-21 [747 kB]
2025-Aug-18 12:12:39.624988
#9 5.590 Get:125 http://deb.debian.org/debian trixie/main arm64 gpg arm64 2.4.7-21+b3 [578 kB]
2025-Aug-18 12:12:39.624988
#9 5.598 Get:126 http://deb.debian.org/debian trixie/main arm64 pinentry-curses arm64 1.3.1-2 [83.5 kB]
2025-Aug-18 12:12:39.624988
#9 5.598 Get:127 http://deb.debian.org/debian trixie/main arm64 gpg-agent arm64 2.4.7-21+b3 [249 kB]
2025-Aug-18 12:12:39.624988
#9 5.598 Get:128 http://deb.debian.org/debian trixie/main arm64 gpgsm arm64 2.4.7-21+b3 [251 kB]
2025-Aug-18 12:12:39.624988
#9 5.599 Get:129 http://deb.debian.org/debian trixie/main arm64 gnupg all 2.4.7-21 [417 kB]
2025-Aug-18 12:12:39.624988
#9 5.603 Get:130 http://deb.debian.org/debian trixie/main arm64 gpg-wks-client arm64 2.4.7-21+b3 [101 kB]
2025-Aug-18 12:12:39.624988
#9 5.605 Get:131 http://deb.debian.org/debian trixie/main arm64 gpgv arm64 2.4.7-21+b3 [220 kB]
2025-Aug-18 12:12:39.624988
#9 5.609 Get:132 http://deb.debian.org/debian trixie/main arm64 libalgorithm-diff-perl all 1.201-1 [43.3 kB]
2025-Aug-18 12:12:39.624988
#9 5.612 Get:133 http://deb.debian.org/debian trixie/main arm64 libalgorithm-diff-xs-perl arm64 0.04-9 [10.9 kB]
2025-Aug-18 12:12:39.624988
#9 5.612 Get:134 http://deb.debian.org/debian trixie/main arm64 libalgorithm-merge-perl all 0.08-5 [11.8 kB]
2025-Aug-18 12:12:39.624988
#9 5.612 Get:135 http://deb.debian.org/debian trixie/main arm64 libatk1.0-0t64 arm64 2.56.2-1 [50.1 kB]
2025-Aug-18 12:12:39.624988
#9 5.619 Get:136 http://deb.debian.org/debian trixie/main arm64 libatk-bridge2.0-0t64 arm64 2.56.2-1 [64.8 kB]
2025-Aug-18 12:12:39.624988
#9 5.619 Get:137 http://deb.debian.org/debian trixie/main arm64 libavahi-common-data arm64 0.8-16 [112 kB]
2025-Aug-18 12:12:39.624988
#9 5.619 Get:138 http://deb.debian.org/debian trixie/main arm64 libavahi-common3 arm64 0.8-16 [43.3 kB]
2025-Aug-18 12:12:39.624988
#9 5.619 Get:139 http://deb.debian.org/debian trixie/main arm64 libavahi-client3 arm64 0.8-16 [46.7 kB]
2025-Aug-18 12:12:39.624988
#9 5.622 Get:140 http://deb.debian.org/debian trixie/main arm64 libpixman-1-0 arm64 0.44.0-3 [168 kB]
2025-Aug-18 12:12:39.624988
#9 5.629 Get:141 http://deb.debian.org/debian trixie/main arm64 libxcb-render0 arm64 1.17.0-2+b1 [115 kB]
2025-Aug-18 12:12:39.624988
#9 5.633 Get:142 http://deb.debian.org/debian trixie/main arm64 libxcb-shm0 arm64 1.17.0-2+b1 [105 kB]
2025-Aug-18 12:12:39.624988
#9 5.637 Get:143 http://deb.debian.org/debian trixie/main arm64 libxrender1 arm64 1:0.9.12-1 [27.0 kB]
2025-Aug-18 12:12:39.624988
#9 5.638 Get:144 http://deb.debian.org/debian trixie/main arm64 libcairo2 arm64 1.18.4-1+b1 [483 kB]
2025-Aug-18 12:12:39.624988
#9 5.642 Get:145 http://deb.debian.org/debian trixie/main arm64 libcom-err2 arm64 1.47.2-3+b3 [24.9 kB]
2025-Aug-18 12:12:39.624988
#9 5.643 Get:146 http://deb.debian.org/debian trixie/main arm64 libjson-c5 arm64 0.18+ds-1 [45.2 kB]
2025-Aug-18 12:12:39.624988
#9 5.645 Get:147 http://deb.debian.org/debian trixie/main arm64 libcryptsetup12 arm64 2:2.7.5-2 [232 kB]
2025-Aug-18 12:12:39.849662
#9 5.652 Get:148 http://deb.debian.org/debian trixie/main arm64 libkrb5support0 arm64 1.21.3-5 [32.4 kB]
2025-Aug-18 12:12:39.849662
#9 5.659 Get:149 http://deb.debian.org/debian trixie/main arm64 libk5crypto3 arm64 1.21.3-5 [81.2 kB]
2025-Aug-18 12:12:39.849662
#9 5.659 Get:150 http://deb.debian.org/debian trixie/main arm64 libkeyutils1 arm64 1.6.3-6 [9716 B]
2025-Aug-18 12:12:39.849662
#9 5.659 Get:151 http://deb.debian.org/debian trixie/main arm64 libkrb5-3 arm64 1.21.3-5 [308 kB]
2025-Aug-18 12:12:39.849662
#9 5.660 Get:152 http://deb.debian.org/debian trixie/main arm64 libgssapi-krb5-2 arm64 1.21.3-5 [127 kB]
2025-Aug-18 12:12:39.849662
#9 5.666 Get:153 http://deb.debian.org/debian trixie/main arm64 libcups2t64 arm64 2.4.10-3 [235 kB]
2025-Aug-18 12:12:39.849662
#9 5.668 Get:154 http://deb.debian.org/debian trixie/main arm64 libnghttp2-14 arm64 1.64.0-1.1 [71.4 kB]
2025-Aug-18 12:12:39.849662
#9 5.673 Get:155 http://deb.debian.org/debian trixie/main arm64 libnghttp3-9 arm64 1.8.0-1 [63.2 kB]
2025-Aug-18 12:12:39.849662
#9 5.673 Get:156 http://deb.debian.org/debian trixie/main arm64 libngtcp2-16 arm64 1.11.0-1 [121 kB]
2025-Aug-18 12:12:39.849662
#9 5.673 Get:157 http://deb.debian.org/debian trixie/main arm64 libngtcp2-crypto-gnutls8 arm64 1.11.0-1 [28.2 kB]
2025-Aug-18 12:12:39.849662
#9 5.673 Get:158 http://deb.debian.org/debian trixie/main arm64 librtmp1 arm64 2.4+20151223.gitfa8646d.1-2+b5 [56.8 kB]
2025-Aug-18 12:12:39.849662
#9 5.673 Get:159 http://deb.debian.org/debian trixie/main arm64 libssh2-1t64 arm64 1.11.1-1 [235 kB]
2025-Aug-18 12:12:39.849662
#9 5.673 Get:160 http://deb.debian.org/debian trixie/main arm64 libcurl3t64-gnutls arm64 8.14.1-2 [353 kB]
2025-Aug-18 12:12:39.849662
#9 5.682 Get:161 http://deb.debian.org/debian trixie/main arm64 libdatrie1 arm64 0.2.13-3+b1 [37.6 kB]
2025-Aug-18 12:12:39.849662
#9 5.687 Get:162 http://deb.debian.org/debian trixie/main arm64 libdeflate0 arm64 1.23-2 [42.4 kB]
2025-Aug-18 12:12:39.849662
#9 5.688 Get:163 http://deb.debian.org/debian trixie/main arm64 libdrm-common all 2.4.124-2 [8288 B]
2025-Aug-18 12:12:39.849662
#9 5.688 Get:164 http://deb.debian.org/debian trixie/main arm64 libdrm2 arm64 2.4.124-2 [38.3 kB]
2025-Aug-18 12:12:39.849662
#9 5.690 Get:165 http://deb.debian.org/debian trixie/main arm64 libdrm-amdgpu1 arm64 2.4.124-2 [21.8 kB]
2025-Aug-18 12:12:39.849662
#9 5.691 Get:166 http://deb.debian.org/debian trixie/main arm64 libedit2 arm64 3.1-20250104-1 [89.3 kB]
2025-Aug-18 12:12:39.849662
#9 5.692 Get:167 http://deb.debian.org/debian trixie/main arm64 libwayland-server0 arm64 1.23.1-3 [33.7 kB]
2025-Aug-18 12:12:39.849662
#9 5.698 Get:168 http://deb.debian.org/debian trixie/main arm64 libelf1t64 arm64 0.192-4 [189 kB]
2025-Aug-18 12:12:39.849662
#9 5.702 Get:169 http://deb.debian.org/debian trixie/main arm64 libxml2 arm64 2.12.7+dfsg+really2.9.14-2.1 [630 kB]
2025-Aug-18 12:12:39.849662
#9 5.705 Get:170 http://deb.debian.org/debian trixie/main arm64 libz3-4 arm64 4.13.3-1 [7507 kB]
2025-Aug-18 12:12:39.849662
#9 5.742 Get:171 http://deb.debian.org/debian trixie/main arm64 libllvm19 arm64 1:19.1.7-3+b1 [23.3 MB]
2025-Aug-18 12:12:39.849662
#9 5.870 Get:172 http://deb.debian.org/debian trixie/main arm64 libsensors-config all 1:3.6.2-2 [16.2 kB]
2025-Aug-18 12:12:39.951890
#9 5.871 Get:173 http://deb.debian.org/debian trixie/main arm64 libsensors5 arm64 1:3.6.2-2 [36.4 kB]
2025-Aug-18 12:12:39.951890
#9 5.871 Get:174 http://deb.debian.org/debian trixie/main arm64 libx11-xcb1 arm64 2:1.8.12-1 [247 kB]
2025-Aug-18 12:12:39.951890
#9 5.874 Get:175 http://deb.debian.org/debian trixie/main arm64 libxcb-dri3-0 arm64 1.17.0-2+b1 [107 kB]
2025-Aug-18 12:12:39.951890
#9 5.878 Get:176 http://deb.debian.org/debian trixie/main arm64 libxcb-present0 arm64 1.17.0-2+b1 [106 kB]
2025-Aug-18 12:12:39.951890
#9 5.881 Get:177 http://deb.debian.org/debian trixie/main arm64 libxcb-randr0 arm64 1.17.0-2+b1 [117 kB]
2025-Aug-18 12:12:39.951890
#9 5.881 Get:178 http://deb.debian.org/debian trixie/main arm64 libxcb-sync1 arm64 1.17.0-2+b1 [109 kB]
2025-Aug-18 12:12:39.951890
#9 5.881 Get:179 http://deb.debian.org/debian trixie/main arm64 libxcb-xfixes0 arm64 1.17.0-2+b1 [110 kB]
2025-Aug-18 12:12:39.951890
#9 5.882 Get:180 http://deb.debian.org/debian trixie/main arm64 libxshmfence1 arm64 1.3.3-1 [11.1 kB]
2025-Aug-18 12:12:39.951890
#9 5.884 Get:181 http://deb.debian.org/debian trixie/main arm64 mesa-libgallium arm64 25.0.7-2 [8032 kB]
2025-Aug-18 12:12:39.951890
#9 5.932 Get:182 http://deb.debian.org/debian trixie/main arm64 libgbm1 arm64 25.0.7-2 [43.9 kB]
2025-Aug-18 12:12:39.951890
#9 5.933 Get:183 http://deb.debian.org/debian trixie/main arm64 libwayland-client0 arm64 1.23.1-3 [26.1 kB]
2025-Aug-18 12:12:39.951890
#9 5.933 Get:184 http://deb.debian.org/debian trixie/main arm64 libegl-mesa0 arm64 25.0.7-2 [121 kB]
2025-Aug-18 12:12:39.951890
#9 5.935 Get:185 http://deb.debian.org/debian trixie/main arm64 libfile-fcntllock-perl arm64 0.22-4+b4 [34.6 kB]
2025-Aug-18 12:12:39.951890
#9 5.937 Get:186 http://deb.debian.org/debian trixie/main arm64 libfribidi0 arm64 1.0.16-1 [26.5 kB]
2025-Aug-18 12:12:39.951890
#9 5.938 Get:187 http://deb.debian.org/debian trixie/main arm64 libglib2.0-data all 2.84.3-1 [1285 kB]
2025-Aug-18 12:12:39.951890
#9 5.948 Get:188 http://deb.debian.org/debian trixie/main arm64 libglvnd0 arm64 1.7.0-1+b2 [41.6 kB]
2025-Aug-18 12:12:39.951890
#9 5.949 Get:189 http://deb.debian.org/debian trixie/main arm64 libgpg-error-l10n all 1.51-4 [114 kB]
2025-Aug-18 12:12:39.951890
#9 5.951 Get:190 http://deb.debian.org/debian trixie/main arm64 libgpgme11t64 arm64 1.24.2-3 [329 kB]
2025-Aug-18 12:12:39.951890
#9 5.954 Get:191 http://deb.debian.org/debian trixie/main arm64 libgpgmepp6t64 arm64 1.24.2-3 [328 kB]
2025-Aug-18 12:12:39.951890
#9 5.956 Get:192 http://deb.debian.org/debian trixie/main arm64 libgraphite2-3 arm64 1.3.14-2+b1 [70.4 kB]
2025-Aug-18 12:12:39.951890
#9 5.958 Get:193 http://deb.debian.org/debian trixie/main arm64 libharfbuzz0b arm64 10.2.0-1+b1 [442 kB]
2025-Aug-18 12:12:39.951890
#9 5.962 Get:194 http://deb.debian.org/debian trixie/main arm64 libjbig0 arm64 2.1-6.1+b2 [30.4 kB]
2025-Aug-18 12:12:39.951890
#9 5.964 Get:195 http://deb.debian.org/debian trixie/main arm64 libjpeg62-turbo arm64 1:2.1.5-4 [173 kB]
2025-Aug-18 12:12:39.951890
#9 5.967 Get:196 http://deb.debian.org/debian trixie/main arm64 libkmod2 arm64 34.2-2 [59.7 kB]
2025-Aug-18 12:12:39.951890
#9 5.968 Get:197 http://deb.debian.org/debian trixie/main arm64 liblcms2-2 arm64 2.16-2 [151 kB]
2025-Aug-18 12:12:39.951890
#9 5.972 Get:198 http://deb.debian.org/debian trixie/main arm64 libldap-common all 2.6.10+dfsg-1 [35.1 kB]
2025-Aug-18 12:12:40.053736
#9 5.976 Get:199 http://deb.debian.org/debian trixie/main arm64 liblerc4 arm64 4.0.0+ds-5 [146 kB]
2025-Aug-18 12:12:40.053736
#9 5.982 Get:200 http://deb.debian.org/debian trixie/main arm64 libnspr4 arm64 2:4.36-1 [102 kB]
2025-Aug-18 12:12:40.053736
#9 5.983 Get:201 http://deb.debian.org/debian trixie/main arm64 libnss3 arm64 2:3.110-1 [1292 kB]
2025-Aug-18 12:12:40.053736
#9 5.991 Get:202 http://deb.debian.org/debian trixie/main arm64 libopenjp2-7 arm64 2.5.3-2 [190 kB]
2025-Aug-18 12:12:40.053736
#9 5.995 Get:203 http://deb.debian.org/debian trixie/main arm64 libthai-data all 0.1.29-2 [168 kB]
2025-Aug-18 12:12:40.053736
#9 5.996 Get:204 http://deb.debian.org/debian trixie/main arm64 libthai0 arm64 0.1.29-2+b1 [48.4 kB]
2025-Aug-18 12:12:40.053736
#9 5.997 Get:205 http://deb.debian.org/debian trixie/main arm64 libpango-1.0-0 arm64 1.56.3-1 [213 kB]
2025-Aug-18 12:12:40.053736
#9 6.000 Get:206 http://deb.debian.org/debian trixie/main arm64 libpangoft2-1.0-0 arm64 1.56.3-1 [52.9 kB]
2025-Aug-18 12:12:40.053736
#9 6.003 Get:207 http://deb.debian.org/debian trixie/main arm64 libpangocairo-1.0-0 arm64 1.56.3-1 [33.7 kB]
2025-Aug-18 12:12:40.053736
#9 6.007 Get:208 http://deb.debian.org/debian trixie/main arm64 libsharpyuv0 arm64 1.5.0-0.1 [114 kB]
2025-Aug-18 12:12:40.053736
#9 6.008 Get:209 http://deb.debian.org/debian trixie/main arm64 libwebp7 arm64 1.5.0-0.1 [271 kB]
2025-Aug-18 12:12:40.053736
#9 6.011 Get:210 http://deb.debian.org/debian trixie/main arm64 libtiff6 arm64 4.7.0-3 [325 kB]
2025-Aug-18 12:12:40.053736
#9 6.015 Get:211 http://deb.debian.org/debian trixie/main arm64 libpoppler147 arm64 25.03.0-5 [1912 kB]
2025-Aug-18 12:12:40.053736
#9 6.030 Get:212 http://deb.debian.org/debian trixie/main arm64 libpoppler-cpp2 arm64 25.03.0-5 [41.1 kB]
2025-Aug-18 12:12:40.053736
#9 6.032 Get:213 http://deb.debian.org/debian trixie/main arm64 libpoppler-dev arm64 25.03.0-5 [9308 B]
2025-Aug-18 12:12:40.053736
#9 6.033 Get:214 http://deb.debian.org/debian trixie/main arm64 libpoppler-cpp-dev arm64 25.03.0-5 [16.0 kB]
2025-Aug-18 12:12:40.053736
#9 6.034 Get:215 http://deb.debian.org/debian trixie/main arm64 libsasl2-modules arm64 2.1.28+dfsg1-9 [62.9 kB]
2025-Aug-18 12:12:40.053736
#9 6.036 Get:216 http://deb.debian.org/debian trixie/main arm64 libxcomposite1 arm64 1:0.4.6-1 [16.4 kB]
2025-Aug-18 12:12:40.053736
#9 6.037 Get:217 http://deb.debian.org/debian trixie/main arm64 libxfixes3 arm64 1:6.0.0-2+b4 [20.5 kB]
2025-Aug-18 12:12:40.053736
#9 6.038 Get:218 http://deb.debian.org/debian trixie/main arm64 libxcursor1 arm64 1:1.2.3-1 [39.3 kB]
2025-Aug-18 12:12:40.053736
#9 6.040 Get:219 http://deb.debian.org/debian trixie/main arm64 libxdamage1 arm64 1:1.1.6-1+b2 [15.6 kB]
2025-Aug-18 12:12:40.053736
#9 6.043 Get:220 http://deb.debian.org/debian trixie/main arm64 libxrandr2 arm64 2:1.5.4-1+b3 [35.9 kB]
2025-Aug-18 12:12:40.053736
#9 6.043 Get:221 http://deb.debian.org/debian trixie/main arm64 manpages-dev all 6.9.1-1 [2122 kB]
2025-Aug-18 12:12:40.053736
#9 6.058 Get:222 http://deb.debian.org/debian trixie/main arm64 poppler-utils arm64 25.03.0-5 [193 kB]
2025-Aug-18 12:12:40.053736
#9 6.060 Get:223 http://deb.debian.org/debian trixie/main arm64 psmisc arm64 23.7-2 [265 kB]
2025-Aug-18 12:12:40.053736
#9 6.063 Get:224 http://deb.debian.org/debian trixie/main arm64 publicsuffix all 20250328.1952-0.1 [296 kB]
2025-Aug-18 12:12:40.053736
#9 6.065 Get:225 http://deb.debian.org/debian trixie/main arm64 shared-mime-info arm64 2.4-5+b2 [756 kB]
2025-Aug-18 12:12:40.053736
#9 6.069 Get:226 http://deb.debian.org/debian trixie/main arm64 systemd-cryptsetup arm64 257.7-1 [164 kB]
2025-Aug-18 12:12:40.053736
#9 6.071 Get:227 http://deb.debian.org/debian trixie/main arm64 xdg-user-dirs arm64 0.18-2 [53.2 kB]
2025-Aug-18 12:12:40.053736
#9 6.074 Get:228 http://deb.debian.org/debian trixie/main arm64 gnupg-utils arm64 2.4.7-21+b3 [181 kB]
2025-Aug-18 12:12:40.053736
#9 6.074 Get:229 http://deb.debian.org/debian trixie/main arm64 libegl1 arm64 1.7.0-1+b2 [34.0 kB]
2025-Aug-18 12:12:40.336220
#9 6.357 debconf: unable to initialize frontend: Dialog
2025-Aug-18 12:12:40.336220
#9 6.357 debconf: (TERM is not set, so the dialog frontend is not usable.)
2025-Aug-18 12:12:40.336220
#9 6.357 debconf: falling back to frontend: Readline
2025-Aug-18 12:12:40.495935
#9 6.358 debconf: unable to initialize frontend: Readline
2025-Aug-18 12:12:40.495935
#9 6.358 debconf: (Can't locate Term/ReadLine.pm in @INC (you may need to install the Term::ReadLine module) (@INC entries checked: /etc/perl /usr/local/lib/aarch64-linux-gnu/perl/5.40.1 /usr/local/share/perl/5.40.1 /usr/lib/aarch64-linux-gnu/perl5/5.40 /usr/share/perl5 /usr/lib/aarch64-linux-gnu/perl-base /usr/lib/aarch64-linux-gnu/perl/5.40 /usr/share/perl/5.40 /usr/local/lib/site_perl) at /usr/share/perl5/Debconf/FrontEnd/Readline.pm line 8, <STDIN> line 229.)
2025-Aug-18 12:12:40.495935
#9 6.358 debconf: falling back to frontend: Teletype
2025-Aug-18 12:12:40.495935
#9 6.366 debconf: unable to initialize frontend: Teletype
2025-Aug-18 12:12:40.495935
#9 6.366 debconf: (This frontend requires a controlling tty.)
2025-Aug-18 12:12:40.495935
#9 6.366 debconf: falling back to frontend: Noninteractive
2025-Aug-18 12:12:46.374635
#9 12.40 Preconfiguring packages ...
2025-Aug-18 12:12:46.490948
#9 12.51 Fetched 156 MB in 1s (133 MB/s)
2025-Aug-18 12:12:46.690407
#9 12.55 Selecting previously unselected package libsystemd-shared:arm64.
2025-Aug-18 12:12:46.690407
#9 12.55 (Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 5641 files and directories currently installed.)
2025-Aug-18 12:12:46.690407
#9 12.56 Preparing to unpack .../libsystemd-shared_257.7-1_arm64.deb ...
2025-Aug-18 12:12:46.690407
#9 12.56 Unpacking libsystemd-shared:arm64 (257.7-1) ...
2025-Aug-18 12:12:46.729028
#9 12.75 Selecting previously unselected package libapparmor1:arm64.
2025-Aug-18 12:12:46.729028
#9 12.75 Preparing to unpack .../libapparmor1_4.1.0-1_arm64.deb ...
2025-Aug-18 12:12:46.843638
#9 12.76 Unpacking libapparmor1:arm64 (4.1.0-1) ...
2025-Aug-18 12:12:46.850601
#9 12.79 Setting up libsystemd-shared:arm64 (257.7-1) ...
2025-Aug-18 12:12:46.850601
#9 12.84 Selecting previously unselected package systemd.
2025-Aug-18 12:12:46.850601
#9 12.84 (Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 5654 files and directories currently installed.)
2025-Aug-18 12:12:46.850601
#9 12.85 Preparing to unpack .../systemd_257.7-1_arm64.deb ...
2025-Aug-18 12:12:46.850601
#9 12.86 Unpacking systemd (257.7-1) ...
2025-Aug-18 12:12:47.093349
#9 13.11 Setting up libapparmor1:arm64 (4.1.0-1) ...
2025-Aug-18 12:12:47.203733
#9 13.12 Setting up systemd (257.7-1) ...
2025-Aug-18 12:12:47.203733
#9 13.17 Created symlink '/etc/systemd/system/getty.target.wants/<EMAIL>' → '/usr/lib/systemd/system/getty@.service'.
2025-Aug-18 12:12:47.203733
#9 13.18 Created symlink '/etc/systemd/system/multi-user.target.wants/remote-fs.target' → '/usr/lib/systemd/system/remote-fs.target'.
2025-Aug-18 12:12:47.203733
#9 13.19 Created symlink '/etc/systemd/system/sysinit.target.wants/systemd-pstore.service' → '/usr/lib/systemd/system/systemd-pstore.service'.
2025-Aug-18 12:12:47.203733
#9 13.19 Initializing machine ID from random generator.
2025-Aug-18 12:12:47.203733
#9 13.22 Creating group 'systemd-journal' with GID 999.
2025-Aug-18 12:12:47.303397
#9 13.22 Creating group 'systemd-network' with GID 998.
2025-Aug-18 12:12:47.303397
#9 13.22 Creating user 'systemd-network' (systemd Network Management) with UID 998 and GID 998.
2025-Aug-18 12:12:47.303397
#9 13.25 /usr/lib/tmpfiles.d/legacy.conf:14: Duplicate line for path "/run/lock", ignoring.
2025-Aug-18 12:12:47.303397
#9 13.31 Selecting previously unselected package systemd-sysv.
2025-Aug-18 12:12:47.303397
#9 13.31 (Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 6592 files and directories currently installed.)
2025-Aug-18 12:12:47.303397
#9 13.32 Preparing to unpack .../000-systemd-sysv_257.7-1_arm64.deb ...
2025-Aug-18 12:12:47.303397
#9 13.32 Unpacking systemd-sysv (257.7-1) ...
2025-Aug-18 12:12:47.425930
#9 13.36 Selecting previously unselected package libdbus-1-3:arm64.
2025-Aug-18 12:12:47.425930
#9 13.36 Preparing to unpack .../001-libdbus-1-3_1.16.2-2_arm64.deb ...
2025-Aug-18 12:12:47.425930
#9 13.37 Unpacking libdbus-1-3:arm64 (1.16.2-2) ...
2025-Aug-18 12:12:47.425930
#9 13.40 Selecting previously unselected package dbus-bin.
2025-Aug-18 12:12:47.425930
#9 13.41 Preparing to unpack .../002-dbus-bin_1.16.2-2_arm64.deb ...
2025-Aug-18 12:12:47.425930
#9 13.41 Unpacking dbus-bin (1.16.2-2) ...
2025-Aug-18 12:12:47.425930
#9 13.45 Selecting previously unselected package dbus-session-bus-common.
2025-Aug-18 12:12:47.561846
#9 13.45 Preparing to unpack .../003-dbus-session-bus-common_1.16.2-2_all.deb ...
2025-Aug-18 12:12:47.561846
#9 13.45 Unpacking dbus-session-bus-common (1.16.2-2) ...
2025-Aug-18 12:12:47.561846
#9 13.49 Selecting previously unselected package libexpat1:arm64.
2025-Aug-18 12:12:47.561846
#9 13.49 Preparing to unpack .../004-libexpat1_2.7.1-2_arm64.deb ...
2025-Aug-18 12:12:47.561846
#9 13.49 Unpacking libexpat1:arm64 (2.7.1-2) ...
2025-Aug-18 12:12:47.561846
#9 13.54 Selecting previously unselected package dbus-daemon.
2025-Aug-18 12:12:47.561846
#9 13.54 Preparing to unpack .../005-dbus-daemon_1.16.2-2_arm64.deb ...
2025-Aug-18 12:12:47.561846
#9 13.54 Unpacking dbus-daemon (1.16.2-2) ...
2025-Aug-18 12:12:47.561846
#9 13.58 Selecting previously unselected package dbus-system-bus-common.
2025-Aug-18 12:12:47.670126
#9 13.59 Preparing to unpack .../006-dbus-system-bus-common_1.16.2-2_all.deb ...
2025-Aug-18 12:12:47.670126
#9 13.59 Unpacking dbus-system-bus-common (1.16.2-2) ...
2025-Aug-18 12:12:47.670126
#9 13.64 Selecting previously unselected package dbus.
2025-Aug-18 12:12:47.670126
#9 13.65 Preparing to unpack .../007-dbus_1.16.2-2_arm64.deb ...
2025-Aug-18 12:12:47.670126
#9 13.65 Unpacking dbus (1.16.2-2) ...
2025-Aug-18 12:12:47.670126
#9 13.69 Selecting previously unselected package liblocale-gettext-perl.
2025-Aug-18 12:12:47.870403
#9 13.69 Preparing to unpack .../008-liblocale-gettext-perl_1.07-7+b1_arm64.deb ...
2025-Aug-18 12:12:47.870403
#9 13.70 Unpacking liblocale-gettext-perl (1.07-7+b1) ...
2025-Aug-18 12:12:47.870403
#9 13.73 Selecting previously unselected package poppler-data.
2025-Aug-18 12:12:47.870403
#9 13.73 Preparing to unpack .../009-poppler-data_0.4.12-1_all.deb ...
2025-Aug-18 12:12:47.870403
#9 13.74 Unpacking poppler-data (0.4.12-1) ...
2025-Aug-18 12:12:47.983898
#9 14.00 Selecting previously unselected package linux-sysctl-defaults.
2025-Aug-18 12:12:48.084018
#9 14.01 Preparing to unpack .../010-linux-sysctl-defaults_4.12_all.deb ...
2025-Aug-18 12:12:48.084018
#9 14.01 Unpacking linux-sysctl-defaults (4.12) ...
2025-Aug-18 12:12:48.084018
#9 14.05 Selecting previously unselected package libproc2-0:arm64.
2025-Aug-18 12:12:48.084018
#9 14.05 Preparing to unpack .../011-libproc2-0_2%3a4.0.4-9_arm64.deb ...
2025-Aug-18 12:12:48.084018
#9 14.05 Unpacking libproc2-0:arm64 (2:4.0.4-9) ...
2025-Aug-18 12:12:48.084018
#9 14.09 Selecting previously unselected package procps.
2025-Aug-18 12:12:48.084018
#9 14.09 Preparing to unpack .../012-procps_2%3a4.0.4-9_arm64.deb ...
2025-Aug-18 12:12:48.084018
#9 14.10 Unpacking procps (2:4.0.4-9) ...
2025-Aug-18 12:12:48.201732
#9 14.22 Selecting previously unselected package bzip2.
2025-Aug-18 12:12:48.302502
#9 14.23 Preparing to unpack .../013-bzip2_1.0.8-6_arm64.deb ...
2025-Aug-18 12:12:48.302502
#9 14.23 Unpacking bzip2 (1.0.8-6) ...
2025-Aug-18 12:12:48.302502
#9 14.27 Selecting previously unselected package krb5-locales.
2025-Aug-18 12:12:48.302502
#9 14.27 Preparing to unpack .../014-krb5-locales_1.21.3-5_all.deb ...
2025-Aug-18 12:12:48.302502
#9 14.28 Unpacking krb5-locales (1.21.3-5) ...
2025-Aug-18 12:12:48.302502
#9 14.32 Selecting previously unselected package libnss-systemd:arm64.
2025-Aug-18 12:12:48.302502
#9 14.32 Preparing to unpack .../015-libnss-systemd_257.7-1_arm64.deb ...
2025-Aug-18 12:12:48.425305
#9 14.33 Unpacking libnss-systemd:arm64 (257.7-1) ...
2025-Aug-18 12:12:48.425305
#9 14.39 Selecting previously unselected package libpam-systemd:arm64.
2025-Aug-18 12:12:48.425305
#9 14.39 Preparing to unpack .../016-libpam-systemd_257.7-1_arm64.deb ...
2025-Aug-18 12:12:48.425305
#9 14.39 Unpacking libpam-systemd:arm64 (257.7-1) ...
2025-Aug-18 12:12:48.425305
#9 14.45 Selecting previously unselected package manpages.
2025-Aug-18 12:12:48.531387
#9 14.45 Preparing to unpack .../017-manpages_6.9.1-1_all.deb ...
2025-Aug-18 12:12:48.531387
#9 14.45 Unpacking manpages (6.9.1-1) ...
2025-Aug-18 12:12:48.531387
#9 14.55 Selecting previously unselected package perl-modules-5.40.
2025-Aug-18 12:12:48.690825
#9 14.56 Preparing to unpack .../018-perl-modules-5.40_5.40.1-6_all.deb ...
2025-Aug-18 12:12:48.690825
#9 14.56 Unpacking perl-modules-5.40 (5.40.1-6) ...
2025-Aug-18 12:12:48.940224
#9 14.96 Selecting previously unselected package libgdbm-compat4t64:arm64.
2025-Aug-18 12:12:49.152847
#9 14.97 Preparing to unpack .../019-libgdbm-compat4t64_1.24-2_arm64.deb ...
2025-Aug-18 12:12:49.152847
#9 14.97 Unpacking libgdbm-compat4t64:arm64 (1.24-2) ...
2025-Aug-18 12:12:49.152847
#9 15.02 Selecting previously unselected package libperl5.40:arm64.
2025-Aug-18 12:12:49.152847
#9 15.02 Preparing to unpack .../020-libperl5.40_5.40.1-6_arm64.deb ...
2025-Aug-18 12:12:49.152847
#9 15.02 Unpacking libperl5.40:arm64 (5.40.1-6) ...
2025-Aug-18 12:12:49.403984
#9 15.42 Selecting previously unselected package perl.
2025-Aug-18 12:12:49.511491
#9 15.43 Preparing to unpack .../021-perl_5.40.1-6_arm64.deb ...
2025-Aug-18 12:12:49.518777
#9 15.43 Unpacking perl (5.40.1-6) ...
2025-Aug-18 12:12:49.518777
#9 15.48 Selecting previously unselected package systemd-timesyncd.
2025-Aug-18 12:12:49.518777
#9 15.49 Preparing to unpack .../022-systemd-timesyncd_257.7-1_arm64.deb ...
2025-Aug-18 12:12:49.518777
#9 15.49 Unpacking systemd-timesyncd (257.7-1) ...
2025-Aug-18 12:12:49.518777
#9 15.53 Selecting previously unselected package libunistring5:arm64.
2025-Aug-18 12:12:49.670974
#9 15.54 Preparing to unpack .../023-libunistring5_1.3-2_arm64.deb ...
2025-Aug-18 12:12:49.670974
#9 15.54 Unpacking libunistring5:arm64 (1.3-2) ...
2025-Aug-18 12:12:49.670974
#9 15.62 Selecting previously unselected package libidn2-0:arm64.
2025-Aug-18 12:12:49.670974
#9 15.63 Preparing to unpack .../024-libidn2-0_2.3.8-2_arm64.deb ...
2025-Aug-18 12:12:49.670974
#9 15.63 Unpacking libidn2-0:arm64 (2.3.8-2) ...
2025-Aug-18 12:12:49.670974
#9 15.69 Selecting previously unselected package libp11-kit0:arm64.
2025-Aug-18 12:12:49.788762
#9 15.70 Preparing to unpack .../025-libp11-kit0_0.25.5-3_arm64.deb ...
2025-Aug-18 12:12:49.788762
#9 15.70 Unpacking libp11-kit0:arm64 (0.25.5-3) ...
2025-Aug-18 12:12:49.788762
#9 15.81 Selecting previously unselected package libtasn1-6:arm64.
2025-Aug-18 12:12:50.001719
#9 15.82 Preparing to unpack .../026-libtasn1-6_4.20.0-2_arm64.deb ...
2025-Aug-18 12:12:50.001719
#9 15.83 Unpacking libtasn1-6:arm64 (4.20.0-2) ...
2025-Aug-18 12:12:50.001719
#9 15.89 Selecting previously unselected package libgnutls30t64:arm64.
2025-Aug-18 12:12:50.001719
#9 15.90 Preparing to unpack .../027-libgnutls30t64_3.8.9-3_arm64.deb ...
2025-Aug-18 12:12:50.001719
#9 15.90 Unpacking libgnutls30t64:arm64 (3.8.9-3) ...
2025-Aug-18 12:12:50.001719
#9 16.02 Selecting previously unselected package libpsl5t64:arm64.
2025-Aug-18 12:12:50.138797
#9 16.03 Preparing to unpack .../028-libpsl5t64_0.21.2-1.1+b1_arm64.deb ...
2025-Aug-18 12:12:50.138797
#9 16.03 Unpacking libpsl5t64:arm64 (0.21.2-1.1+b1) ...
2025-Aug-18 12:12:50.138797
#9 16.07 Selecting previously unselected package wget.
2025-Aug-18 12:12:50.138797
#9 16.07 Preparing to unpack .../029-wget_1.25.0-2_arm64.deb ...
2025-Aug-18 12:12:50.138797
#9 16.07 Unpacking wget (1.25.0-2) ...
2025-Aug-18 12:12:50.138797
#9 16.16 Selecting previously unselected package xz-utils.
2025-Aug-18 12:12:50.266832
#9 16.16 Preparing to unpack .../030-xz-utils_5.8.1-1_arm64.deb ...
2025-Aug-18 12:12:50.266832
#9 16.17 Unpacking xz-utils (5.8.1-1) ...
2025-Aug-18 12:12:50.266832
#9 16.24 Selecting previously unselected package alsa-topology-conf.
2025-Aug-18 12:12:50.266832
#9 16.24 Preparing to unpack .../031-alsa-topology-conf_1.2.5.1-3_all.deb ...
2025-Aug-18 12:12:50.266832
#9 16.24 Unpacking alsa-topology-conf (1.2.5.1-3) ...
2025-Aug-18 12:12:50.266832
#9 16.29 Selecting previously unselected package libasound2-data.
2025-Aug-18 12:12:50.266832
#9 16.29 Preparing to unpack .../032-libasound2-data_1.2.14-1_all.deb ...
2025-Aug-18 12:12:50.393871
#9 16.29 Unpacking libasound2-data (1.2.14-1) ...
2025-Aug-18 12:12:50.393871
#9 16.35 Selecting previously unselected package libasound2t64:arm64.
2025-Aug-18 12:12:50.393871
#9 16.35 Preparing to unpack .../033-libasound2t64_1.2.14-1_arm64.deb ...
2025-Aug-18 12:12:50.393871
#9 16.35 Unpacking libasound2t64:arm64 (1.2.14-1) ...
2025-Aug-18 12:12:50.393871
#9 16.41 Selecting previously unselected package alsa-ucm-conf.
2025-Aug-18 12:12:50.552817
#9 16.42 Preparing to unpack .../034-alsa-ucm-conf_1.2.14-1_all.deb ...
2025-Aug-18 12:12:50.552817
#9 16.42 Unpacking alsa-ucm-conf (1.2.14-1) ...
2025-Aug-18 12:12:50.562745
#9 16.58 Selecting previously unselected package at-spi2-common.
2025-Aug-18 12:12:50.670693
#9 16.59 Preparing to unpack .../035-at-spi2-common_2.56.2-1_all.deb ...
2025-Aug-18 12:12:50.670693
#9 16.59 Unpacking at-spi2-common (2.56.2-1) ...
2025-Aug-18 12:12:50.670693
#9 16.64 Selecting previously unselected package libatomic1:arm64.
2025-Aug-18 12:12:50.670693
#9 16.64 Preparing to unpack .../036-libatomic1_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:50.670693
#9 16.64 Unpacking libatomic1:arm64 (14.2.0-19) ...
2025-Aug-18 12:12:50.670693
#9 16.69 Selecting previously unselected package libglib2.0-0t64:arm64.
2025-Aug-18 12:12:50.846780
#9 16.70 Preparing to unpack .../037-libglib2.0-0t64_2.84.3-1_arm64.deb ...
2025-Aug-18 12:12:50.846780
#9 16.71 Unpacking libglib2.0-0t64:arm64 (2.84.3-1) ...
2025-Aug-18 12:12:50.856759
#9 16.88 Selecting previously unselected package libxau6:arm64.
2025-Aug-18 12:12:50.981815
#9 16.88 Preparing to unpack .../038-libxau6_1%3a1.0.11-1_arm64.deb ...
2025-Aug-18 12:12:50.981815
#9 16.89 Unpacking libxau6:arm64 (1:1.0.11-1) ...
2025-Aug-18 12:12:50.981815
#9 16.94 Selecting previously unselected package libxdmcp6:arm64.
2025-Aug-18 12:12:50.981815
#9 16.95 Preparing to unpack .../039-libxdmcp6_1%3a1.1.5-1_arm64.deb ...
2025-Aug-18 12:12:50.981815
#9 16.95 Unpacking libxdmcp6:arm64 (1:1.1.5-1) ...
2025-Aug-18 12:12:50.981815
#9 17.00 Selecting previously unselected package libxcb1:arm64.
2025-Aug-18 12:12:51.177916
#9 17.01 Preparing to unpack .../040-libxcb1_1.17.0-2+b1_arm64.deb ...
2025-Aug-18 12:12:51.177916
#9 17.01 Unpacking libxcb1:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:12:51.177916
#9 17.06 Selecting previously unselected package libx11-data.
2025-Aug-18 12:12:51.177916
#9 17.07 Preparing to unpack .../041-libx11-data_2%3a1.8.12-1_all.deb ...
2025-Aug-18 12:12:51.177916
#9 17.07 Unpacking libx11-data (2:1.8.12-1) ...
2025-Aug-18 12:12:51.177916
#9 17.20 Selecting previously unselected package libx11-6:arm64.
2025-Aug-18 12:12:51.338146
#9 17.20 Preparing to unpack .../042-libx11-6_2%3a1.8.12-1_arm64.deb ...
2025-Aug-18 12:12:51.338146
#9 17.21 Unpacking libx11-6:arm64 (2:1.8.12-1) ...
2025-Aug-18 12:12:51.338146
#9 17.36 Selecting previously unselected package libxext6:arm64.
2025-Aug-18 12:12:51.468876
#9 17.36 Preparing to unpack .../043-libxext6_2%3a1.3.4-1+b3_arm64.deb ...
2025-Aug-18 12:12:51.468876
#9 17.36 Unpacking libxext6:arm64 (2:1.3.4-1+b3) ...
2025-Aug-18 12:12:51.468876
#9 17.42 Selecting previously unselected package libxi6:arm64.
2025-Aug-18 12:12:51.468876
#9 17.42 Preparing to unpack .../044-libxi6_2%3a1.8.2-1_arm64.deb ...
2025-Aug-18 12:12:51.468876
#9 17.43 Unpacking libxi6:arm64 (2:1.8.2-1) ...
2025-Aug-18 12:12:51.468876
#9 17.49 Selecting previously unselected package libatspi2.0-0t64:arm64.
2025-Aug-18 12:12:51.573498
#9 17.50 Preparing to unpack .../045-libatspi2.0-0t64_2.56.2-1_arm64.deb ...
2025-Aug-18 12:12:51.573498
#9 17.50 Unpacking libatspi2.0-0t64:arm64 (2.56.2-1) ...
2025-Aug-18 12:12:51.573498
#9 17.59 Selecting previously unselected package x11-common.
2025-Aug-18 12:12:51.715272
#9 17.59 Preparing to unpack .../046-x11-common_1%3a7.7+24_all.deb ...
2025-Aug-18 12:12:51.715272
#9 17.60 Unpacking x11-common (1:7.7+24) ...
2025-Aug-18 12:12:51.715272
#9 17.67 Selecting previously unselected package libxtst6:arm64.
2025-Aug-18 12:12:51.715272
#9 17.68 Preparing to unpack .../047-libxtst6_2%3a1.2.5-1_arm64.deb ...
2025-Aug-18 12:12:51.715272
#9 17.68 Unpacking libxtst6:arm64 (2:1.2.5-1) ...
2025-Aug-18 12:12:51.715272
#9 17.74 Selecting previously unselected package dbus-user-session.
2025-Aug-18 12:12:51.839566
#9 17.74 Preparing to unpack .../048-dbus-user-session_1.16.2-2_arm64.deb ...
2025-Aug-18 12:12:51.839566
#9 17.74 Unpacking dbus-user-session (1.16.2-2) ...
2025-Aug-18 12:12:51.839566
#9 17.80 Selecting previously unselected package libdconf1:arm64.
2025-Aug-18 12:12:51.839566
#9 17.81 Preparing to unpack .../049-libdconf1_0.40.0-5_arm64.deb ...
2025-Aug-18 12:12:51.839566
#9 17.81 Unpacking libdconf1:arm64 (0.40.0-5) ...
2025-Aug-18 12:12:51.839566
#9 17.86 Selecting previously unselected package dconf-service.
2025-Aug-18 12:12:51.951031
#9 17.87 Preparing to unpack .../050-dconf-service_0.40.0-5_arm64.deb ...
2025-Aug-18 12:12:51.951031
#9 17.87 Unpacking dconf-service (0.40.0-5) ...
2025-Aug-18 12:12:51.951031
#9 17.92 Selecting previously unselected package dconf-gsettings-backend:arm64.
2025-Aug-18 12:12:51.951031
#9 17.92 Preparing to unpack .../051-dconf-gsettings-backend_0.40.0-5_arm64.deb ...
2025-Aug-18 12:12:51.951031
#9 17.92 Unpacking dconf-gsettings-backend:arm64 (0.40.0-5) ...
2025-Aug-18 12:12:51.951031
#9 17.97 Selecting previously unselected package gsettings-desktop-schemas.
2025-Aug-18 12:12:52.076197
#9 17.97 Preparing to unpack .../052-gsettings-desktop-schemas_48.0-1_all.deb ...
2025-Aug-18 12:12:52.076197
#9 17.98 Unpacking gsettings-desktop-schemas (48.0-1) ...
2025-Aug-18 12:12:52.076197
#9 18.09 Selecting previously unselected package at-spi2-core.
2025-Aug-18 12:12:52.184076
#9 18.10 Preparing to unpack .../053-at-spi2-core_2.56.2-1_arm64.deb ...
2025-Aug-18 12:12:52.184076
#9 18.10 Unpacking at-spi2-core (2.56.2-1) ...
2025-Aug-18 12:12:52.184076
#9 18.14 Selecting previously unselected package libsframe1:arm64.
2025-Aug-18 12:12:52.184076
#9 18.15 Preparing to unpack .../054-libsframe1_2.44-3_arm64.deb ...
2025-Aug-18 12:12:52.184076
#9 18.15 Unpacking libsframe1:arm64 (2.44-3) ...
2025-Aug-18 12:12:52.184076
#9 18.20 Selecting previously unselected package binutils-common:arm64.
2025-Aug-18 12:12:52.343003
#9 18.21 Preparing to unpack .../055-binutils-common_2.44-3_arm64.deb ...
2025-Aug-18 12:12:52.343003
#9 18.21 Unpacking binutils-common:arm64 (2.44-3) ...
2025-Aug-18 12:12:52.443147
#9 18.46 Selecting previously unselected package libbinutils:arm64.
2025-Aug-18 12:12:52.553925
#9 18.46 Preparing to unpack .../056-libbinutils_2.44-3_arm64.deb ...
2025-Aug-18 12:12:52.553925
#9 18.47 Unpacking libbinutils:arm64 (2.44-3) ...
2025-Aug-18 12:12:52.553925
#9 18.57 Selecting previously unselected package libgprofng0:arm64.
2025-Aug-18 12:12:52.693120
#9 18.58 Preparing to unpack .../057-libgprofng0_2.44-3_arm64.deb ...
2025-Aug-18 12:12:52.693120
#9 18.58 Unpacking libgprofng0:arm64 (2.44-3) ...
2025-Aug-18 12:12:52.693120
#9 18.66 Selecting previously unselected package libctf-nobfd0:arm64.
2025-Aug-18 12:12:52.693120
#9 18.67 Preparing to unpack .../058-libctf-nobfd0_2.44-3_arm64.deb ...
2025-Aug-18 12:12:52.693120
#9 18.67 Unpacking libctf-nobfd0:arm64 (2.44-3) ...
2025-Aug-18 12:12:52.693120
#9 18.71 Selecting previously unselected package libctf0:arm64.
2025-Aug-18 12:12:52.806322
#9 18.72 Preparing to unpack .../059-libctf0_2.44-3_arm64.deb ...
2025-Aug-18 12:12:52.806322
#9 18.72 Unpacking libctf0:arm64 (2.44-3) ...
2025-Aug-18 12:12:52.806322
#9 18.77 Selecting previously unselected package libjansson4:arm64.
2025-Aug-18 12:12:52.806322
#9 18.77 Preparing to unpack .../060-libjansson4_2.14-2+b3_arm64.deb ...
2025-Aug-18 12:12:52.806322
#9 18.77 Unpacking libjansson4:arm64 (2.14-2+b3) ...
2025-Aug-18 12:12:52.806322
#9 18.82 Selecting previously unselected package binutils-aarch64-linux-gnu.
2025-Aug-18 12:12:52.969768
#9 18.83 Preparing to unpack .../061-binutils-aarch64-linux-gnu_2.44-3_arm64.deb ...
2025-Aug-18 12:12:52.969768
#9 18.83 Unpacking binutils-aarch64-linux-gnu (2.44-3) ...
2025-Aug-18 12:12:53.082134
#9 19.10 Selecting previously unselected package binutils.
2025-Aug-18 12:12:53.196691
#9 19.11 Preparing to unpack .../062-binutils_2.44-3_arm64.deb ...
2025-Aug-18 12:12:53.196691
#9 19.11 Unpacking binutils (2.44-3) ...
2025-Aug-18 12:12:53.196691
#9 19.22 Selecting previously unselected package libc-dev-bin.
2025-Aug-18 12:12:53.401395
#9 19.22 Preparing to unpack .../063-libc-dev-bin_2.41-12_arm64.deb ...
2025-Aug-18 12:12:53.401395
#9 19.23 Unpacking libc-dev-bin (2.41-12) ...
2025-Aug-18 12:12:53.401395
#9 19.26 Selecting previously unselected package linux-libc-dev.
2025-Aug-18 12:12:53.401395
#9 19.27 Preparing to unpack .../064-linux-libc-dev_6.12.41-1_all.deb ...
2025-Aug-18 12:12:53.401395
#9 19.27 Unpacking linux-libc-dev (6.12.41-1) ...
2025-Aug-18 12:12:53.661747
#9 19.68 Selecting previously unselected package libcrypt-dev:arm64.
2025-Aug-18 12:12:53.786918
#9 19.69 Preparing to unpack .../065-libcrypt-dev_1%3a4.4.38-1_arm64.deb ...
2025-Aug-18 12:12:53.786918
#9 19.70 Unpacking libcrypt-dev:arm64 (1:4.4.38-1) ...
2025-Aug-18 12:12:53.786918
#9 19.75 Selecting previously unselected package rpcsvc-proto.
2025-Aug-18 12:12:53.786918
#9 19.75 Preparing to unpack .../066-rpcsvc-proto_1.4.3-1+b1_arm64.deb ...
2025-Aug-18 12:12:53.786918
#9 19.76 Unpacking rpcsvc-proto (1.4.3-1+b1) ...
2025-Aug-18 12:12:53.786918
#9 19.81 Selecting previously unselected package libc6-dev:arm64.
2025-Aug-18 12:12:53.948418
#9 19.81 Preparing to unpack .../067-libc6-dev_2.41-12_arm64.deb ...
2025-Aug-18 12:12:53.948418
#9 19.82 Unpacking libc6-dev:arm64 (2.41-12) ...
2025-Aug-18 12:12:54.061938
#9 20.08 Selecting previously unselected package libisl23:arm64.
2025-Aug-18 12:12:54.177910
#9 20.09 Preparing to unpack .../068-libisl23_0.27-1_arm64.deb ...
2025-Aug-18 12:12:54.177910
#9 20.09 Unpacking libisl23:arm64 (0.27-1) ...
2025-Aug-18 12:12:54.177910
#9 20.20 Selecting previously unselected package libmpfr6:arm64.
2025-Aug-18 12:12:54.289125
#9 20.20 Preparing to unpack .../069-libmpfr6_4.2.2-1_arm64.deb ...
2025-Aug-18 12:12:54.289125
#9 20.20 Unpacking libmpfr6:arm64 (4.2.2-1) ...
2025-Aug-18 12:12:54.289125
#9 20.27 Selecting previously unselected package libmpc3:arm64.
2025-Aug-18 12:12:54.289125
#9 20.27 Preparing to unpack .../070-libmpc3_1.3.1-1+b3_arm64.deb ...
2025-Aug-18 12:12:54.289125
#9 20.28 Unpacking libmpc3:arm64 (1.3.1-1+b3) ...
2025-Aug-18 12:12:54.289125
#9 20.31 Selecting previously unselected package cpp-14-aarch64-linux-gnu.
2025-Aug-18 12:12:54.448971
#9 20.31 Preparing to unpack .../071-cpp-14-aarch64-linux-gnu_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:54.448971
#9 20.32 Unpacking cpp-14-aarch64-linux-gnu (14.2.0-19) ...
2025-Aug-18 12:12:54.923764
#9 20.94 Selecting previously unselected package cpp-14.
2025-Aug-18 12:12:55.046048
#9 20.95 Preparing to unpack .../072-cpp-14_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:55.046048
#9 20.96 Unpacking cpp-14 (14.2.0-19) ...
2025-Aug-18 12:12:55.046048
#9 20.98 Selecting previously unselected package cpp-aarch64-linux-gnu.
2025-Aug-18 12:12:55.046048
#9 20.98 Preparing to unpack .../073-cpp-aarch64-linux-gnu_4%3a14.2.0-1_arm64.deb ...
2025-Aug-18 12:12:55.046048
#9 20.99 Unpacking cpp-aarch64-linux-gnu (4:14.2.0-1) ...
2025-Aug-18 12:12:55.046048
#9 21.02 Selecting previously unselected package cpp.
2025-Aug-18 12:12:55.046048
#9 21.02 Preparing to unpack .../074-cpp_4%3a14.2.0-1_arm64.deb ...
2025-Aug-18 12:12:55.046048
#9 21.03 Unpacking cpp (4:14.2.0-1) ...
2025-Aug-18 12:12:55.053155
#9 21.07 Selecting previously unselected package libcc1-0:arm64.
2025-Aug-18 12:12:55.159789
#9 21.07 Preparing to unpack .../075-libcc1-0_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:55.159789
#9 21.08 Unpacking libcc1-0:arm64 (14.2.0-19) ...
2025-Aug-18 12:12:55.159789
#9 21.12 Selecting previously unselected package libgomp1:arm64.
2025-Aug-18 12:12:55.159789
#9 21.13 Preparing to unpack .../076-libgomp1_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:55.159789
#9 21.14 Unpacking libgomp1:arm64 (14.2.0-19) ...
2025-Aug-18 12:12:55.159789
#9 21.18 Selecting previously unselected package libitm1:arm64.
2025-Aug-18 12:12:55.365082
#9 21.18 Preparing to unpack .../077-libitm1_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:55.365082
#9 21.19 Unpacking libitm1:arm64 (14.2.0-19) ...
2025-Aug-18 12:12:55.365082
#9 21.23 Selecting previously unselected package libasan8:arm64.
2025-Aug-18 12:12:55.365082
#9 21.23 Preparing to unpack .../078-libasan8_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:55.365082
#9 21.24 Unpacking libasan8:arm64 (14.2.0-19) ...
2025-Aug-18 12:12:55.441907
#9 21.46 Selecting previously unselected package liblsan0:arm64.
2025-Aug-18 12:12:55.565660
#9 21.47 Preparing to unpack .../079-liblsan0_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:55.565660
#9 21.47 Unpacking liblsan0:arm64 (14.2.0-19) ...
2025-Aug-18 12:12:55.565660
#9 21.59 Selecting previously unselected package libtsan2:arm64.
2025-Aug-18 12:12:55.723161
#9 21.59 Preparing to unpack .../080-libtsan2_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:55.723161
#9 21.59 Unpacking libtsan2:arm64 (14.2.0-19) ...
2025-Aug-18 12:12:55.762419
#9 21.78 Selecting previously unselected package libubsan1:arm64.
2025-Aug-18 12:12:55.867185
#9 21.79 Preparing to unpack .../081-libubsan1_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:55.867185
#9 21.79 Unpacking libubsan1:arm64 (14.2.0-19) ...
2025-Aug-18 12:12:55.867185
#9 21.89 Selecting previously unselected package libhwasan0:arm64.
2025-Aug-18 12:12:55.996764
#9 21.89 Preparing to unpack .../082-libhwasan0_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:55.996764
#9 21.89 Unpacking libhwasan0:arm64 (14.2.0-19) ...
2025-Aug-18 12:12:55.996764
#9 22.02 Selecting previously unselected package libgcc-14-dev:arm64.
2025-Aug-18 12:12:56.170064
#9 22.02 Preparing to unpack .../083-libgcc-14-dev_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:56.170064
#9 22.04 Unpacking libgcc-14-dev:arm64 (14.2.0-19) ...
2025-Aug-18 12:12:56.210569
#9 22.23 Selecting previously unselected package gcc-14-aarch64-linux-gnu.
2025-Aug-18 12:12:56.371499
#9 22.24 Preparing to unpack .../084-gcc-14-aarch64-linux-gnu_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:56.371499
#9 22.24 Unpacking gcc-14-aarch64-linux-gnu (14.2.0-19) ...
2025-Aug-18 12:12:57.183244
#9 23.20 Selecting previously unselected package gcc-14.
2025-Aug-18 12:12:57.283881
#9 23.21 Preparing to unpack .../085-gcc-14_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:57.283881
#9 23.21 Unpacking gcc-14 (14.2.0-19) ...
2025-Aug-18 12:12:57.283881
#9 23.26 Selecting previously unselected package gcc-aarch64-linux-gnu.
2025-Aug-18 12:12:57.283881
#9 23.26 Preparing to unpack .../086-gcc-aarch64-linux-gnu_4%3a14.2.0-1_arm64.deb ...
2025-Aug-18 12:12:57.283881
#9 23.26 Unpacking gcc-aarch64-linux-gnu (4:14.2.0-1) ...
2025-Aug-18 12:12:57.283881
#9 23.30 Selecting previously unselected package gcc.
2025-Aug-18 12:12:57.283881
#9 23.30 Preparing to unpack .../087-gcc_4%3a14.2.0-1_arm64.deb ...
2025-Aug-18 12:12:57.283881
#9 23.31 Unpacking gcc (4:14.2.0-1) ...
2025-Aug-18 12:12:57.481103
#9 23.34 Selecting previously unselected package libstdc++-14-dev:arm64.
2025-Aug-18 12:12:57.481103
#9 23.35 Preparing to unpack .../088-libstdc++-14-dev_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:57.481103
#9 23.35 Unpacking libstdc++-14-dev:arm64 (14.2.0-19) ...
2025-Aug-18 12:12:57.567900
#9 23.59 Selecting previously unselected package g++-14-aarch64-linux-gnu.
2025-Aug-18 12:12:57.727802
#9 23.59 Preparing to unpack .../089-g++-14-aarch64-linux-gnu_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:57.727802
#9 23.60 Unpacking g++-14-aarch64-linux-gnu (14.2.0-19) ...
2025-Aug-18 12:12:58.284126
#9 24.31 Selecting previously unselected package g++-14.
2025-Aug-18 12:12:58.408725
#9 24.31 Preparing to unpack .../090-g++-14_14.2.0-19_arm64.deb ...
2025-Aug-18 12:12:58.408725
#9 24.31 Unpacking g++-14 (14.2.0-19) ...
2025-Aug-18 12:12:58.408725
#9 24.35 Selecting previously unselected package g++-aarch64-linux-gnu.
2025-Aug-18 12:12:58.408725
#9 24.35 Preparing to unpack .../091-g++-aarch64-linux-gnu_4%3a14.2.0-1_arm64.deb ...
2025-Aug-18 12:12:58.408725
#9 24.35 Unpacking g++-aarch64-linux-gnu (4:14.2.0-1) ...
2025-Aug-18 12:12:58.408725
#9 24.38 Selecting previously unselected package g++.
2025-Aug-18 12:12:58.408725
#9 24.39 Preparing to unpack .../092-g++_4%3a14.2.0-1_arm64.deb ...
2025-Aug-18 12:12:58.408725
#9 24.39 Unpacking g++ (4:14.2.0-1) ...
2025-Aug-18 12:12:58.408725
#9 24.43 Selecting previously unselected package make.
2025-Aug-18 12:12:58.567935
#9 24.43 Preparing to unpack .../093-make_4.4.1-2_arm64.deb ...
2025-Aug-18 12:12:58.567935
#9 24.44 Unpacking make (4.4.1-2) ...
2025-Aug-18 12:12:58.567935
#9 24.49 Selecting previously unselected package libdpkg-perl.
2025-Aug-18 12:12:58.567935
#9 24.50 Preparing to unpack .../094-libdpkg-perl_1.22.21_all.deb ...
2025-Aug-18 12:12:58.567935
#9 24.50 Unpacking libdpkg-perl (1.22.21) ...
2025-Aug-18 12:12:58.567935
#9 24.59 Selecting previously unselected package patch.
2025-Aug-18 12:12:58.731886
#9 24.59 Preparing to unpack .../095-patch_2.8-2_arm64.deb ...
2025-Aug-18 12:12:58.731886
#9 24.60 Unpacking patch (2.8-2) ...
2025-Aug-18 12:12:58.731886
#9 24.64 Selecting previously unselected package dpkg-dev.
2025-Aug-18 12:12:58.731886
#9 24.65 Preparing to unpack .../096-dpkg-dev_1.22.21_all.deb ...
2025-Aug-18 12:12:58.731886
#9 24.65 Unpacking dpkg-dev (1.22.21) ...
2025-Aug-18 12:12:58.731886
#9 24.75 Selecting previously unselected package build-essential.
2025-Aug-18 12:12:58.874184
#9 24.76 Preparing to unpack .../097-build-essential_12.12_arm64.deb ...
2025-Aug-18 12:12:58.874184
#9 24.76 Unpacking build-essential (12.12) ...
2025-Aug-18 12:12:58.874184
#9 24.80 Selecting previously unselected package libgpg-error0:arm64.
2025-Aug-18 12:12:58.874184
#9 24.80 Preparing to unpack .../098-libgpg-error0_1.51-4_arm64.deb ...
2025-Aug-18 12:12:58.874184
#9 24.80 Unpacking libgpg-error0:arm64 (1.51-4) ...
2025-Aug-18 12:12:58.874184
#9 24.84 Selecting previously unselected package libassuan9:arm64.
2025-Aug-18 12:12:58.874184
#9 24.85 Preparing to unpack .../099-libassuan9_3.0.2-2_arm64.deb ...
2025-Aug-18 12:12:58.874184
#9 24.85 Unpacking libassuan9:arm64 (3.0.2-2) ...
2025-Aug-18 12:12:58.874184
#9 24.89 Selecting previously unselected package libgcrypt20:arm64.
2025-Aug-18 12:12:59.003997
#9 24.90 Preparing to unpack .../100-libgcrypt20_1.11.0-7_arm64.deb ...
2025-Aug-18 12:12:59.003997
#9 24.90 Unpacking libgcrypt20:arm64 (1.11.0-7) ...
2025-Aug-18 12:12:59.003997
#9 24.97 Selecting previously unselected package gpgconf.
2025-Aug-18 12:12:59.003997
#9 24.97 Preparing to unpack .../101-gpgconf_2.4.7-21+b3_arm64.deb ...
2025-Aug-18 12:12:59.003997
#9 24.97 Unpacking gpgconf (2.4.7-21+b3) ...
2025-Aug-18 12:12:59.003997
#9 25.02 Selecting previously unselected package libksba8:arm64.
2025-Aug-18 12:12:59.117642
#9 25.03 Preparing to unpack .../102-libksba8_1.6.7-2+b1_arm64.deb ...
2025-Aug-18 12:12:59.117642
#9 25.04 Unpacking libksba8:arm64 (1.6.7-2+b1) ...
2025-Aug-18 12:12:59.117642
#9 25.08 Selecting previously unselected package libsasl2-modules-db:arm64.
2025-Aug-18 12:12:59.117642
#9 25.09 Preparing to unpack .../103-libsasl2-modules-db_2.1.28+dfsg1-9_arm64.deb ...
2025-Aug-18 12:12:59.117642
#9 25.09 Unpacking libsasl2-modules-db:arm64 (2.1.28+dfsg1-9) ...
2025-Aug-18 12:12:59.117642
#9 25.14 Selecting previously unselected package libsasl2-2:arm64.
2025-Aug-18 12:12:59.215999
#9 25.14 Preparing to unpack .../104-libsasl2-2_2.1.28+dfsg1-9_arm64.deb ...
2025-Aug-18 12:12:59.215999
#9 25.14 Unpacking libsasl2-2:arm64 (2.1.28+dfsg1-9) ...
2025-Aug-18 12:12:59.215999
#9 25.18 Selecting previously unselected package libldap2:arm64.
2025-Aug-18 12:12:59.215999
#9 25.18 Preparing to unpack .../105-libldap2_2.6.10+dfsg-1_arm64.deb ...
2025-Aug-18 12:12:59.215999
#9 25.19 Unpacking libldap2:arm64 (2.6.10+dfsg-1) ...
2025-Aug-18 12:12:59.223424
#9 25.24 Selecting previously unselected package libnpth0t64:arm64.
2025-Aug-18 12:12:59.223424
#9 25.24 Preparing to unpack .../106-libnpth0t64_1.8-3_arm64.deb ...
2025-Aug-18 12:12:59.340710
#9 25.24 Unpacking libnpth0t64:arm64 (1.8-3) ...
2025-Aug-18 12:12:59.340710
#9 25.28 Selecting previously unselected package dirmngr.
2025-Aug-18 12:12:59.340710
#9 25.28 Preparing to unpack .../107-dirmngr_2.4.7-21+b3_arm64.deb ...
2025-Aug-18 12:12:59.340710
#9 25.30 Unpacking dirmngr (2.4.7-21+b3) ...
2025-Aug-18 12:12:59.340710
#9 25.36 Selecting previously unselected package libdevmapper1.02.1:arm64.
2025-Aug-18 12:12:59.448717
#9 25.36 Preparing to unpack .../108-libdevmapper1.02.1_2%3a1.02.205-2_arm64.deb ...
2025-Aug-18 12:12:59.454790
#9 25.37 Unpacking libdevmapper1.02.1:arm64 (2:1.02.205-2) ...
2025-Aug-18 12:12:59.454790
#9 25.42 Selecting previously unselected package dmsetup.
2025-Aug-18 12:12:59.454790
#9 25.42 Preparing to unpack .../109-dmsetup_2%3a1.02.205-2_arm64.deb ...
2025-Aug-18 12:12:59.454790
#9 25.42 Unpacking dmsetup (2:1.02.205-2) ...
2025-Aug-18 12:12:59.454790
#9 25.47 Selecting previously unselected package libfakeroot:arm64.
2025-Aug-18 12:12:59.558194
#9 25.47 Preparing to unpack .../110-libfakeroot_********-1_arm64.deb ...
2025-Aug-18 12:12:59.558194
#9 25.48 Unpacking libfakeroot:arm64 (********-1) ...
2025-Aug-18 12:12:59.558194
#9 25.52 Selecting previously unselected package fakeroot.
2025-Aug-18 12:12:59.558194
#9 25.52 Preparing to unpack .../111-fakeroot_********-1_arm64.deb ...
2025-Aug-18 12:12:59.558194
#9 25.53 Unpacking fakeroot (********-1) ...
2025-Aug-18 12:12:59.558194
#9 25.58 Selecting previously unselected package libbrotli1:arm64.
2025-Aug-18 12:12:59.698561
#9 25.58 Preparing to unpack .../112-libbrotli1_1.1.0-2+b7_arm64.deb ...
2025-Aug-18 12:12:59.698561
#9 25.59 Unpacking libbrotli1:arm64 (1.1.0-2+b7) ...
2025-Aug-18 12:12:59.698561
#9 25.65 Selecting previously unselected package libpng16-16t64:arm64.
2025-Aug-18 12:12:59.698561
#9 25.65 Preparing to unpack .../113-libpng16-16t64_1.6.48-1_arm64.deb ...
2025-Aug-18 12:12:59.698561
#9 25.66 Unpacking libpng16-16t64:arm64 (1.6.48-1) ...
2025-Aug-18 12:12:59.698561
#9 25.72 Selecting previously unselected package libfreetype6:arm64.
2025-Aug-18 12:12:59.871279
#9 25.72 Preparing to unpack .../114-libfreetype6_2.13.3+dfsg-1_arm64.deb ...
2025-Aug-18 12:12:59.871279
#9 25.73 Unpacking libfreetype6:arm64 (2.13.3+dfsg-1) ...
2025-Aug-18 12:12:59.871279
#9 25.79 Selecting previously unselected package fonts-dejavu-mono.
2025-Aug-18 12:12:59.871279
#9 25.80 Preparing to unpack .../115-fonts-dejavu-mono_2.37-8_all.deb ...
2025-Aug-18 12:12:59.871279
#9 25.80 Unpacking fonts-dejavu-mono (2.37-8) ...
2025-Aug-18 12:12:59.871279
#9 25.89 Selecting previously unselected package fonts-dejavu-core.
2025-Aug-18 12:12:59.994964
#9 25.89 Preparing to unpack .../116-fonts-dejavu-core_2.37-8_all.deb ...
2025-Aug-18 12:12:59.994964
#9 25.93 Unpacking fonts-dejavu-core (2.37-8) ...
2025-Aug-18 12:12:59.994964
#9 26.02 Selecting previously unselected package fontconfig-config.
2025-Aug-18 12:13:00.124749
#9 26.02 Preparing to unpack .../117-fontconfig-config_2.15.0-2.3_arm64.deb ...
2025-Aug-18 12:13:00.124749
#9 26.03 Unpacking fontconfig-config (2.15.0-2.3) ...
2025-Aug-18 12:13:00.124749
#9 26.08 Selecting previously unselected package libfontconfig1:arm64.
2025-Aug-18 12:13:00.124749
#9 26.09 Preparing to unpack .../118-libfontconfig1_2.15.0-2.3_arm64.deb ...
2025-Aug-18 12:13:00.124749
#9 26.09 Unpacking libfontconfig1:arm64 (2.15.0-2.3) ...
2025-Aug-18 12:13:00.124749
#9 26.14 Selecting previously unselected package fontconfig.
2025-Aug-18 12:13:00.326048
#9 26.15 Preparing to unpack .../119-fontconfig_2.15.0-2.3_arm64.deb ...
2025-Aug-18 12:13:00.326048
#9 26.15 Unpacking fontconfig (2.15.0-2.3) ...
2025-Aug-18 12:13:00.326048
#9 26.23 Selecting previously unselected package gnupg-l10n.
2025-Aug-18 12:13:00.326048
#9 26.23 Preparing to unpack .../120-gnupg-l10n_2.4.7-21_all.deb ...
2025-Aug-18 12:13:00.326048
#9 26.24 Unpacking gnupg-l10n (2.4.7-21) ...
2025-Aug-18 12:13:00.326048
#9 26.34 Selecting previously unselected package gpg.
2025-Aug-18 12:13:00.452450
#9 26.35 Preparing to unpack .../121-gpg_2.4.7-21+b3_arm64.deb ...
2025-Aug-18 12:13:00.452450
#9 26.36 Unpacking gpg (2.4.7-21+b3) ...
2025-Aug-18 12:13:00.452450
#9 26.46 Selecting previously unselected package pinentry-curses.
2025-Aug-18 12:13:00.548063
#9 26.47 Preparing to unpack .../122-pinentry-curses_1.3.1-2_arm64.deb ...
2025-Aug-18 12:13:00.548063
#9 26.48 Unpacking pinentry-curses (1.3.1-2) ...
2025-Aug-18 12:13:00.548063
#9 26.56 Selecting previously unselected package gpg-agent.
2025-Aug-18 12:13:00.548063
#9 26.57 Preparing to unpack .../123-gpg-agent_2.4.7-21+b3_arm64.deb ...
2025-Aug-18 12:13:00.660108
#9 26.57 Unpacking gpg-agent (2.4.7-21+b3) ...
2025-Aug-18 12:13:00.660108
#9 26.68 Selecting previously unselected package gpgsm.
2025-Aug-18 12:13:00.804542
#9 26.68 Preparing to unpack .../124-gpgsm_2.4.7-21+b3_arm64.deb ...
2025-Aug-18 12:13:00.804542
#9 26.69 Unpacking gpgsm (2.4.7-21+b3) ...
2025-Aug-18 12:13:00.804542
#9 26.76 Selecting previously unselected package gnupg.
2025-Aug-18 12:13:00.804542
#9 26.76 Preparing to unpack .../125-gnupg_2.4.7-21_all.deb ...
2025-Aug-18 12:13:00.804542
#9 26.76 Unpacking gnupg (2.4.7-21) ...
2025-Aug-18 12:13:00.804542
#9 26.83 Selecting previously unselected package gpg-wks-client.
2025-Aug-18 12:13:00.923053
#9 26.83 Preparing to unpack .../126-gpg-wks-client_2.4.7-21+b3_arm64.deb ...
2025-Aug-18 12:13:00.923053
#9 26.84 Unpacking gpg-wks-client (2.4.7-21+b3) ...
2025-Aug-18 12:13:00.923053
#9 26.89 Selecting previously unselected package gpgv.
2025-Aug-18 12:13:00.923053
#9 26.89 Preparing to unpack .../127-gpgv_2.4.7-21+b3_arm64.deb ...
2025-Aug-18 12:13:00.930039
#9 26.89 Unpacking gpgv (2.4.7-21+b3) ...
2025-Aug-18 12:13:00.930039
#9 26.94 Selecting previously unselected package libalgorithm-diff-perl.
2025-Aug-18 12:13:01.049525
#9 26.95 Preparing to unpack .../128-libalgorithm-diff-perl_1.201-1_all.deb ...
2025-Aug-18 12:13:01.049525
#9 26.95 Unpacking libalgorithm-diff-perl (1.201-1) ...
2025-Aug-18 12:13:01.049525
#9 26.99 Selecting previously unselected package libalgorithm-diff-xs-perl.
2025-Aug-18 12:13:01.049525
#9 26.99 Preparing to unpack .../129-libalgorithm-diff-xs-perl_0.04-9_arm64.deb ...
2025-Aug-18 12:13:01.049525
#9 26.99 Unpacking libalgorithm-diff-xs-perl (0.04-9) ...
2025-Aug-18 12:13:01.049525
#9 27.03 Selecting previously unselected package libalgorithm-merge-perl.
2025-Aug-18 12:13:01.049525
#9 27.03 Preparing to unpack .../130-libalgorithm-merge-perl_0.08-5_all.deb ...
2025-Aug-18 12:13:01.049525
#9 27.03 Unpacking libalgorithm-merge-perl (0.08-5) ...
2025-Aug-18 12:13:01.049525
#9 27.07 Selecting previously unselected package libatk1.0-0t64:arm64.
2025-Aug-18 12:13:01.161948
#9 27.08 Preparing to unpack .../131-libatk1.0-0t64_2.56.2-1_arm64.deb ...
2025-Aug-18 12:13:01.161948
#9 27.08 Unpacking libatk1.0-0t64:arm64 (2.56.2-1) ...
2025-Aug-18 12:13:01.161948
#9 27.13 Selecting previously unselected package libatk-bridge2.0-0t64:arm64.
2025-Aug-18 12:13:01.161948
#9 27.14 Preparing to unpack .../132-libatk-bridge2.0-0t64_2.56.2-1_arm64.deb ...
2025-Aug-18 12:13:01.161948
#9 27.14 Unpacking libatk-bridge2.0-0t64:arm64 (2.56.2-1) ...
2025-Aug-18 12:13:01.161948
#9 27.18 Selecting previously unselected package libavahi-common-data:arm64.
2025-Aug-18 12:13:01.264351
#9 27.19 Preparing to unpack .../133-libavahi-common-data_0.8-16_arm64.deb ...
2025-Aug-18 12:13:01.264351
#9 27.19 Unpacking libavahi-common-data:arm64 (0.8-16) ...
2025-Aug-18 12:13:01.264351
#9 27.24 Selecting previously unselected package libavahi-common3:arm64.
2025-Aug-18 12:13:01.264351
#9 27.24 Preparing to unpack .../134-libavahi-common3_0.8-16_arm64.deb ...
2025-Aug-18 12:13:01.264351
#9 27.24 Unpacking libavahi-common3:arm64 (0.8-16) ...
2025-Aug-18 12:13:01.264351
#9 27.29 Selecting previously unselected package libavahi-client3:arm64.
2025-Aug-18 12:13:01.373059
#9 27.29 Preparing to unpack .../135-libavahi-client3_0.8-16_arm64.deb ...
2025-Aug-18 12:13:01.373059
#9 27.29 Unpacking libavahi-client3:arm64 (0.8-16) ...
2025-Aug-18 12:13:01.373059
#9 27.34 Selecting previously unselected package libpixman-1-0:arm64.
2025-Aug-18 12:13:01.373059
#9 27.34 Preparing to unpack .../136-libpixman-1-0_0.44.0-3_arm64.deb ...
2025-Aug-18 12:13:01.373059
#9 27.35 Unpacking libpixman-1-0:arm64 (0.44.0-3) ...
2025-Aug-18 12:13:01.373059
#9 27.39 Selecting previously unselected package libxcb-render0:arm64.
2025-Aug-18 12:13:01.475886
#9 27.40 Preparing to unpack .../137-libxcb-render0_1.17.0-2+b1_arm64.deb ...
2025-Aug-18 12:13:01.475886
#9 27.40 Unpacking libxcb-render0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:01.475886
#9 27.44 Selecting previously unselected package libxcb-shm0:arm64.
2025-Aug-18 12:13:01.475886
#9 27.45 Preparing to unpack .../138-libxcb-shm0_1.17.0-2+b1_arm64.deb ...
2025-Aug-18 12:13:01.475886
#9 27.45 Unpacking libxcb-shm0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:01.475886
#9 27.49 Selecting previously unselected package libxrender1:arm64.
2025-Aug-18 12:13:01.607128
#9 27.50 Preparing to unpack .../139-libxrender1_1%3a0.9.12-1_arm64.deb ...
2025-Aug-18 12:13:01.615778
#9 27.50 Unpacking libxrender1:arm64 (1:0.9.12-1) ...
2025-Aug-18 12:13:01.615778
#9 27.54 Selecting previously unselected package libcairo2:arm64.
2025-Aug-18 12:13:01.615778
#9 27.55 Preparing to unpack .../140-libcairo2_1.18.4-1+b1_arm64.deb ...
2025-Aug-18 12:13:01.615778
#9 27.55 Unpacking libcairo2:arm64 (1.18.4-1+b1) ...
2025-Aug-18 12:13:01.615778
#9 27.63 Selecting previously unselected package libcom-err2:arm64.
2025-Aug-18 12:13:01.709040
#9 27.63 Preparing to unpack .../141-libcom-err2_1.47.2-3+b3_arm64.deb ...
2025-Aug-18 12:13:01.709040
#9 27.64 Unpacking libcom-err2:arm64 (1.47.2-3+b3) ...
2025-Aug-18 12:13:01.709040
#9 27.68 Selecting previously unselected package libjson-c5:arm64.
2025-Aug-18 12:13:01.709040
#9 27.68 Preparing to unpack .../142-libjson-c5_0.18+ds-1_arm64.deb ...
2025-Aug-18 12:13:01.709040
#9 27.69 Unpacking libjson-c5:arm64 (0.18+ds-1) ...
2025-Aug-18 12:13:01.709040
#9 27.72 Selecting previously unselected package libcryptsetup12:arm64.
2025-Aug-18 12:13:01.709040
#9 27.73 Preparing to unpack .../143-libcryptsetup12_2%3a2.7.5-2_arm64.deb ...
2025-Aug-18 12:13:01.709040
#9 27.73 Unpacking libcryptsetup12:arm64 (2:2.7.5-2) ...
2025-Aug-18 12:13:01.829253
#9 27.80 Selecting previously unselected package libkrb5support0:arm64.
2025-Aug-18 12:13:01.829253
#9 27.80 Preparing to unpack .../144-libkrb5support0_1.21.3-5_arm64.deb ...
2025-Aug-18 12:13:01.829253
#9 27.80 Unpacking libkrb5support0:arm64 (1.21.3-5) ...
2025-Aug-18 12:13:01.829253
#9 27.85 Selecting previously unselected package libk5crypto3:arm64.
2025-Aug-18 12:13:01.930037
#9 27.86 Preparing to unpack .../145-libk5crypto3_1.21.3-5_arm64.deb ...
2025-Aug-18 12:13:01.930037
#9 27.86 Unpacking libk5crypto3:arm64 (1.21.3-5) ...
2025-Aug-18 12:13:01.930037
#9 27.91 Selecting previously unselected package libkeyutils1:arm64.
2025-Aug-18 12:13:01.930037
#9 27.91 Preparing to unpack .../146-libkeyutils1_1.6.3-6_arm64.deb ...
2025-Aug-18 12:13:01.930037
#9 27.92 Unpacking libkeyutils1:arm64 (1.6.3-6) ...
2025-Aug-18 12:13:01.930037
#9 27.95 Selecting previously unselected package libkrb5-3:arm64.
2025-Aug-18 12:13:02.065404
#9 27.96 Preparing to unpack .../147-libkrb5-3_1.21.3-5_arm64.deb ...
2025-Aug-18 12:13:02.065404
#9 27.96 Unpacking libkrb5-3:arm64 (1.21.3-5) ...
2025-Aug-18 12:13:02.065404
#9 28.02 Selecting previously unselected package libgssapi-krb5-2:arm64.
2025-Aug-18 12:13:02.065404
#9 28.02 Preparing to unpack .../148-libgssapi-krb5-2_1.21.3-5_arm64.deb ...
2025-Aug-18 12:13:02.065404
#9 28.03 Unpacking libgssapi-krb5-2:arm64 (1.21.3-5) ...
2025-Aug-18 12:13:02.065404
#9 28.08 Selecting previously unselected package libcups2t64:arm64.
2025-Aug-18 12:13:02.184572
#9 28.09 Preparing to unpack .../149-libcups2t64_2.4.10-3_arm64.deb ...
2025-Aug-18 12:13:02.184572
#9 28.10 Unpacking libcups2t64:arm64 (2.4.10-3) ...
2025-Aug-18 12:13:02.184572
#9 28.15 Selecting previously unselected package libnghttp2-14:arm64.
2025-Aug-18 12:13:02.184572
#9 28.16 Preparing to unpack .../150-libnghttp2-14_1.64.0-1.1_arm64.deb ...
2025-Aug-18 12:13:02.184572
#9 28.16 Unpacking libnghttp2-14:arm64 (1.64.0-1.1) ...
2025-Aug-18 12:13:02.184572
#9 28.20 Selecting previously unselected package libnghttp3-9:arm64.
2025-Aug-18 12:13:02.320501
#9 28.22 Preparing to unpack .../151-libnghttp3-9_1.8.0-1_arm64.deb ...
2025-Aug-18 12:13:02.320501
#9 28.22 Unpacking libnghttp3-9:arm64 (1.8.0-1) ...
2025-Aug-18 12:13:02.320501
#9 28.26 Selecting previously unselected package libngtcp2-16:arm64.
2025-Aug-18 12:13:02.320501
#9 28.27 Preparing to unpack .../152-libngtcp2-16_1.11.0-1_arm64.deb ...
2025-Aug-18 12:13:02.320501
#9 28.27 Unpacking libngtcp2-16:arm64 (1.11.0-1) ...
2025-Aug-18 12:13:02.320501
#9 28.34 Selecting previously unselected package libngtcp2-crypto-gnutls8:arm64.
2025-Aug-18 12:13:02.421999
#9 28.35 Preparing to unpack .../153-libngtcp2-crypto-gnutls8_1.11.0-1_arm64.deb ...
2025-Aug-18 12:13:02.421999
#9 28.35 Unpacking libngtcp2-crypto-gnutls8:arm64 (1.11.0-1) ...
2025-Aug-18 12:13:02.421999
#9 28.39 Selecting previously unselected package librtmp1:arm64.
2025-Aug-18 12:13:02.421999
#9 28.39 Preparing to unpack .../154-librtmp1_2.4+20151223.gitfa8646d.1-2+b5_arm64.deb ...
2025-Aug-18 12:13:02.421999
#9 28.40 Unpacking librtmp1:arm64 (2.4+20151223.gitfa8646d.1-2+b5) ...
2025-Aug-18 12:13:02.421999
#9 28.44 Selecting previously unselected package libssh2-1t64:arm64.
2025-Aug-18 12:13:02.421999
#9 28.44 Preparing to unpack .../155-libssh2-1t64_1.11.1-1_arm64.deb ...
2025-Aug-18 12:13:02.421999
#9 28.44 Unpacking libssh2-1t64:arm64 (1.11.1-1) ...
2025-Aug-18 12:13:02.545028
#9 28.50 Selecting previously unselected package libcurl3t64-gnutls:arm64.
2025-Aug-18 12:13:02.545028
#9 28.50 Preparing to unpack .../156-libcurl3t64-gnutls_8.14.1-2_arm64.deb ...
2025-Aug-18 12:13:02.545028
#9 28.50 Unpacking libcurl3t64-gnutls:arm64 (8.14.1-2) ...
2025-Aug-18 12:13:02.545028
#9 28.57 Selecting previously unselected package libdatrie1:arm64.
2025-Aug-18 12:13:02.647022
#9 28.57 Preparing to unpack .../157-libdatrie1_0.2.13-3+b1_arm64.deb ...
2025-Aug-18 12:13:02.647022
#9 28.57 Unpacking libdatrie1:arm64 (0.2.13-3+b1) ...
2025-Aug-18 12:13:02.647022
#9 28.62 Selecting previously unselected package libdeflate0:arm64.
2025-Aug-18 12:13:02.647022
#9 28.62 Preparing to unpack .../158-libdeflate0_1.23-2_arm64.deb ...
2025-Aug-18 12:13:02.658423
#9 28.62 Unpacking libdeflate0:arm64 (1.23-2) ...
2025-Aug-18 12:13:02.658423
#9 28.66 Selecting previously unselected package libdrm-common.
2025-Aug-18 12:13:02.658423
#9 28.66 Preparing to unpack .../159-libdrm-common_2.4.124-2_all.deb ...
2025-Aug-18 12:13:02.658423
#9 28.67 Unpacking libdrm-common (2.4.124-2) ...
2025-Aug-18 12:13:02.747458
#9 28.71 Selecting previously unselected package libdrm2:arm64.
2025-Aug-18 12:13:02.747458
#9 28.72 Preparing to unpack .../160-libdrm2_2.4.124-2_arm64.deb ...
2025-Aug-18 12:13:02.747458
#9 28.72 Unpacking libdrm2:arm64 (2.4.124-2) ...
2025-Aug-18 12:13:02.747458
#9 28.76 Selecting previously unselected package libdrm-amdgpu1:arm64.
2025-Aug-18 12:13:02.747458
#9 28.77 Preparing to unpack .../161-libdrm-amdgpu1_2.4.124-2_arm64.deb ...
2025-Aug-18 12:13:02.851412
#9 28.77 Unpacking libdrm-amdgpu1:arm64 (2.4.124-2) ...
2025-Aug-18 12:13:02.851412
#9 28.81 Selecting previously unselected package libedit2:arm64.
2025-Aug-18 12:13:02.851412
#9 28.82 Preparing to unpack .../162-libedit2_3.1-20250104-1_arm64.deb ...
2025-Aug-18 12:13:02.851412
#9 28.82 Unpacking libedit2:arm64 (3.1-20250104-1) ...
2025-Aug-18 12:13:02.851412
#9 28.87 Selecting previously unselected package libwayland-server0:arm64.
2025-Aug-18 12:13:02.965914
#9 28.88 Preparing to unpack .../163-libwayland-server0_1.23.1-3_arm64.deb ...
2025-Aug-18 12:13:02.965914
#9 28.88 Unpacking libwayland-server0:arm64 (1.23.1-3) ...
2025-Aug-18 12:13:02.975721
#9 28.92 Selecting previously unselected package libelf1t64:arm64.
2025-Aug-18 12:13:02.975721
#9 28.93 Preparing to unpack .../164-libelf1t64_0.192-4_arm64.deb ...
2025-Aug-18 12:13:02.975721
#9 28.93 Unpacking libelf1t64:arm64 (0.192-4) ...
2025-Aug-18 12:13:02.975721
#9 28.99 Selecting previously unselected package libxml2:arm64.
2025-Aug-18 12:13:03.067763
#9 29.00 Preparing to unpack .../165-libxml2_2.12.7+dfsg+really2.9.14-2.1_arm64.deb ...
2025-Aug-18 12:13:03.067763
#9 29.00 Unpacking libxml2:arm64 (2.12.7+dfsg+really2.9.14-2.1) ...
2025-Aug-18 12:13:03.078353
#9 29.08 Selecting previously unselected package libz3-4:arm64.
2025-Aug-18 12:13:03.078353
#9 29.08 Preparing to unpack .../166-libz3-4_4.13.3-1_arm64.deb ...
2025-Aug-18 12:13:03.078353
#9 29.09 Unpacking libz3-4:arm64 (4.13.3-1) ...
2025-Aug-18 12:13:03.946394
#9 29.97 Selecting previously unselected package libllvm19:arm64.
2025-Aug-18 12:13:04.101541
#9 29.97 Preparing to unpack .../167-libllvm19_1%3a19.1.7-3+b1_arm64.deb ...
2025-Aug-18 12:13:04.109349
#9 29.97 Unpacking libllvm19:arm64 (1:19.1.7-3+b1) ...
2025-Aug-18 12:13:05.486561
#9 31.51 Selecting previously unselected package libsensors-config.
2025-Aug-18 12:13:05.636519
#9 31.51 Preparing to unpack .../168-libsensors-config_1%3a3.6.2-2_all.deb ...
2025-Aug-18 12:13:05.636519
#9 31.52 Unpacking libsensors-config (1:3.6.2-2) ...
2025-Aug-18 12:13:05.636519
#9 31.55 Selecting previously unselected package libsensors5:arm64.
2025-Aug-18 12:13:05.636519
#9 31.55 Preparing to unpack .../169-libsensors5_1%3a3.6.2-2_arm64.deb ...
2025-Aug-18 12:13:05.636519
#9 31.55 Unpacking libsensors5:arm64 (1:3.6.2-2) ...
2025-Aug-18 12:13:05.636519
#9 31.59 Selecting previously unselected package libx11-xcb1:arm64.
2025-Aug-18 12:13:05.636519
#9 31.60 Preparing to unpack .../170-libx11-xcb1_2%3a1.8.12-1_arm64.deb ...
2025-Aug-18 12:13:05.636519
#9 31.60 Unpacking libx11-xcb1:arm64 (2:1.8.12-1) ...
2025-Aug-18 12:13:05.636519
#9 31.66 Selecting previously unselected package libxcb-dri3-0:arm64.
2025-Aug-18 12:13:05.754264
#9 31.66 Preparing to unpack .../171-libxcb-dri3-0_1.17.0-2+b1_arm64.deb ...
2025-Aug-18 12:13:05.754264
#9 31.67 Unpacking libxcb-dri3-0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:05.754264
#9 31.72 Selecting previously unselected package libxcb-present0:arm64.
2025-Aug-18 12:13:05.754264
#9 31.72 Preparing to unpack .../172-libxcb-present0_1.17.0-2+b1_arm64.deb ...
2025-Aug-18 12:13:05.754264
#9 31.73 Unpacking libxcb-present0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:05.754264
#9 31.77 Selecting previously unselected package libxcb-randr0:arm64.
2025-Aug-18 12:13:05.856828
#9 31.78 Preparing to unpack .../173-libxcb-randr0_1.17.0-2+b1_arm64.deb ...
2025-Aug-18 12:13:05.856828
#9 31.78 Unpacking libxcb-randr0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:05.856828
#9 31.82 Selecting previously unselected package libxcb-sync1:arm64.
2025-Aug-18 12:13:05.856828
#9 31.83 Preparing to unpack .../174-libxcb-sync1_1.17.0-2+b1_arm64.deb ...
2025-Aug-18 12:13:05.856828
#9 31.83 Unpacking libxcb-sync1:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:05.856828
#9 31.88 Selecting previously unselected package libxcb-xfixes0:arm64.
2025-Aug-18 12:13:05.957838
#9 31.88 Preparing to unpack .../175-libxcb-xfixes0_1.17.0-2+b1_arm64.deb ...
2025-Aug-18 12:13:05.957838
#9 31.89 Unpacking libxcb-xfixes0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:05.957838
#9 31.93 Selecting previously unselected package libxshmfence1:arm64.
2025-Aug-18 12:13:05.957838
#9 31.93 Preparing to unpack .../176-libxshmfence1_1.3.3-1_arm64.deb ...
2025-Aug-18 12:13:05.957838
#9 31.93 Unpacking libxshmfence1:arm64 (1.3.3-1) ...
2025-Aug-18 12:13:05.957838
#9 31.97 Selecting previously unselected package mesa-libgallium:arm64.
2025-Aug-18 12:13:05.957838
#9 31.97 Preparing to unpack .../177-mesa-libgallium_25.0.7-2_arm64.deb ...
2025-Aug-18 12:13:05.957838
#9 31.98 Unpacking mesa-libgallium:arm64 (25.0.7-2) ...
2025-Aug-18 12:13:06.545306
#9 32.57 Selecting previously unselected package libgbm1:arm64.
2025-Aug-18 12:13:06.555660
2025-Aug-18 12:13:06.648832
#9 32.57 Preparing to unpack .../178-libgbm1_25.0.7-2_arm64.deb ...
2025-Aug-18 12:13:06.648832
#9 32.58 Unpacking libgbm1:arm64 (25.0.7-2) ...
2025-Aug-18 12:13:06.648832
#9 32.62 Selecting previously unselected package libwayland-client0:arm64.
2025-Aug-18 12:13:06.648832
#9 32.62 Preparing to unpack .../179-libwayland-client0_1.23.1-3_arm64.deb ...
2025-Aug-18 12:13:06.648832
#9 32.62 Unpacking libwayland-client0:arm64 (1.23.1-3) ...
2025-Aug-18 12:13:06.648832
#9 32.66 Selecting previously unselected package libegl-mesa0:arm64.
2025-Aug-18 12:13:06.648832
#9 32.67 Preparing to unpack .../180-libegl-mesa0_25.0.7-2_arm64.deb ...
2025-Aug-18 12:13:06.648832
#9 32.67 Unpacking libegl-mesa0:arm64 (25.0.7-2) ...
2025-Aug-18 12:13:06.789461
#9 32.71 Selecting previously unselected package libfile-fcntllock-perl.
2025-Aug-18 12:13:06.789461
#9 32.71 Preparing to unpack .../181-libfile-fcntllock-perl_0.22-4+b4_arm64.deb ...
2025-Aug-18 12:13:06.789461
#9 32.72 Unpacking libfile-fcntllock-perl (0.22-4+b4) ...
2025-Aug-18 12:13:06.789461
#9 32.76 Selecting previously unselected package libfribidi0:arm64.
2025-Aug-18 12:13:06.789461
#9 32.77 Preparing to unpack .../182-libfribidi0_1.0.16-1_arm64.deb ...
2025-Aug-18 12:13:06.789461
#9 32.77 Unpacking libfribidi0:arm64 (1.0.16-1) ...
2025-Aug-18 12:13:06.789461
#9 32.81 Selecting previously unselected package libglib2.0-data.
2025-Aug-18 12:13:06.942802
#9 32.81 Preparing to unpack .../183-libglib2.0-data_2.84.3-1_all.deb ...
2025-Aug-18 12:13:06.942802
#9 32.81 Unpacking libglib2.0-data (2.84.3-1) ...
2025-Aug-18 12:13:06.942802
#9 32.96 Selecting previously unselected package libglvnd0:arm64.
2025-Aug-18 12:13:07.049510
#9 32.97 Preparing to unpack .../184-libglvnd0_1.7.0-1+b2_arm64.deb ...
2025-Aug-18 12:13:07.049510
#9 32.97 Unpacking libglvnd0:arm64 (1.7.0-1+b2) ...
2025-Aug-18 12:13:07.049510
#9 33.02 Selecting previously unselected package libgpg-error-l10n.
2025-Aug-18 12:13:07.049510
#9 33.02 Preparing to unpack .../185-libgpg-error-l10n_1.51-4_all.deb ...
2025-Aug-18 12:13:07.049510
#9 33.03 Unpacking libgpg-error-l10n (1.51-4) ...
2025-Aug-18 12:13:07.049510
#9 33.07 Selecting previously unselected package libgpgme11t64:arm64.
2025-Aug-18 12:13:07.157361
#9 33.07 Preparing to unpack .../186-libgpgme11t64_1.24.2-3_arm64.deb ...
2025-Aug-18 12:13:07.157361
#9 33.08 Unpacking libgpgme11t64:arm64 (1.24.2-3) ...
2025-Aug-18 12:13:07.157361
#9 33.12 Selecting previously unselected package libgpgmepp6t64:arm64.
2025-Aug-18 12:13:07.157361
#9 33.13 Preparing to unpack .../187-libgpgmepp6t64_1.24.2-3_arm64.deb ...
2025-Aug-18 12:13:07.157361
#9 33.13 Unpacking libgpgmepp6t64:arm64 (1.24.2-3) ...
2025-Aug-18 12:13:07.157361
#9 33.18 Selecting previously unselected package libgraphite2-3:arm64.
2025-Aug-18 12:13:07.280174
#9 33.18 Preparing to unpack .../188-libgraphite2-3_1.3.14-2+b1_arm64.deb ...
2025-Aug-18 12:13:07.280174
#9 33.19 Unpacking libgraphite2-3:arm64 (1.3.14-2+b1) ...
2025-Aug-18 12:13:07.280174
#9 33.23 Selecting previously unselected package libharfbuzz0b:arm64.
2025-Aug-18 12:13:07.280174
#9 33.23 Preparing to unpack .../189-libharfbuzz0b_10.2.0-1+b1_arm64.deb ...
2025-Aug-18 12:13:07.280174
#9 33.23 Unpacking libharfbuzz0b:arm64 (10.2.0-1+b1) ...
2025-Aug-18 12:13:07.280174
#9 33.30 Selecting previously unselected package libjbig0:arm64.
2025-Aug-18 12:13:07.400028
#9 33.30 Preparing to unpack .../190-libjbig0_2.1-6.1+b2_arm64.deb ...
2025-Aug-18 12:13:07.400028
#9 33.31 Unpacking libjbig0:arm64 (2.1-6.1+b2) ...
2025-Aug-18 12:13:07.400028
#9 33.35 Selecting previously unselected package libjpeg62-turbo:arm64.
2025-Aug-18 12:13:07.400028
#9 33.35 Preparing to unpack .../191-libjpeg62-turbo_1%3a2.1.5-4_arm64.deb ...
2025-Aug-18 12:13:07.400028
#9 33.36 Unpacking libjpeg62-turbo:arm64 (1:2.1.5-4) ...
2025-Aug-18 12:13:07.400028
#9 33.42 Selecting previously unselected package libkmod2:arm64.
2025-Aug-18 12:13:07.525210
#9 33.42 Preparing to unpack .../192-libkmod2_34.2-2_arm64.deb ...
2025-Aug-18 12:13:07.525210
#9 33.43 Unpacking libkmod2:arm64 (34.2-2) ...
2025-Aug-18 12:13:07.525210
#9 33.46 Selecting previously unselected package liblcms2-2:arm64.
2025-Aug-18 12:13:07.525210
#9 33.47 Preparing to unpack .../193-liblcms2-2_2.16-2_arm64.deb ...
2025-Aug-18 12:13:07.525210
#9 33.48 Unpacking liblcms2-2:arm64 (2.16-2) ...
2025-Aug-18 12:13:07.525210
#9 33.55 Selecting previously unselected package libldap-common.
2025-Aug-18 12:13:07.644843
#9 33.56 Preparing to unpack .../194-libldap-common_2.6.10+dfsg-1_all.deb ...
2025-Aug-18 12:13:07.644843
#9 33.56 Unpacking libldap-common (2.6.10+dfsg-1) ...
2025-Aug-18 12:13:07.644843
#9 33.61 Selecting previously unselected package liblerc4:arm64.
2025-Aug-18 12:13:07.644843
#9 33.62 Preparing to unpack .../195-liblerc4_4.0.0+ds-5_arm64.deb ...
2025-Aug-18 12:13:07.644843
#9 33.62 Unpacking liblerc4:arm64 (4.0.0+ds-5) ...
2025-Aug-18 12:13:07.644843
#9 33.67 Selecting previously unselected package libnspr4:arm64.
2025-Aug-18 12:13:07.844881
#9 33.67 Preparing to unpack .../196-libnspr4_2%3a4.36-1_arm64.deb ...
2025-Aug-18 12:13:07.844881
#9 33.67 Unpacking libnspr4:arm64 (2:4.36-1) ...
2025-Aug-18 12:13:07.844881
#9 33.72 Selecting previously unselected package libnss3:arm64.
2025-Aug-18 12:13:07.844881
#9 33.72 Preparing to unpack .../197-libnss3_2%3a3.110-1_arm64.deb ...
2025-Aug-18 12:13:07.844881
#9 33.73 Unpacking libnss3:arm64 (2:3.110-1) ...
2025-Aug-18 12:13:07.844881
#9 33.86 Selecting previously unselected package libopenjp2-7:arm64.
2025-Aug-18 12:13:07.968804
#9 33.87 Preparing to unpack .../198-libopenjp2-7_2.5.3-2_arm64.deb ...
2025-Aug-18 12:13:07.968804
#9 33.87 Unpacking libopenjp2-7:arm64 (2.5.3-2) ...
2025-Aug-18 12:13:07.968804
#9 33.92 Selecting previously unselected package libthai-data.
2025-Aug-18 12:13:07.968804
#9 33.92 Preparing to unpack .../199-libthai-data_0.1.29-2_all.deb ...
2025-Aug-18 12:13:07.968804
#9 33.92 Unpacking libthai-data (0.1.29-2) ...
2025-Aug-18 12:13:07.968804
#9 33.99 Selecting previously unselected package libthai0:arm64.
2025-Aug-18 12:13:08.105744
#9 34.00 Preparing to unpack .../200-libthai0_0.1.29-2+b1_arm64.deb ...
2025-Aug-18 12:13:08.105744
#9 34.00 Unpacking libthai0:arm64 (0.1.29-2+b1) ...
2025-Aug-18 12:13:08.105744
#9 34.05 Selecting previously unselected package libpango-1.0-0:arm64.
2025-Aug-18 12:13:08.105744
#9 34.05 Preparing to unpack .../201-libpango-1.0-0_1.56.3-1_arm64.deb ...
2025-Aug-18 12:13:08.105744
#9 34.05 Unpacking libpango-1.0-0:arm64 (1.56.3-1) ...
2025-Aug-18 12:13:08.105744
#9 34.13 Selecting previously unselected package libpangoft2-1.0-0:arm64.
2025-Aug-18 12:13:08.229778
#9 34.13 Preparing to unpack .../202-libpangoft2-1.0-0_1.56.3-1_arm64.deb ...
2025-Aug-18 12:13:08.229778
#9 34.14 Unpacking libpangoft2-1.0-0:arm64 (1.56.3-1) ...
2025-Aug-18 12:13:08.229778
#9 34.19 Selecting previously unselected package libpangocairo-1.0-0:arm64.
2025-Aug-18 12:13:08.229778
#9 34.20 Preparing to unpack .../203-libpangocairo-1.0-0_1.56.3-1_arm64.deb ...
2025-Aug-18 12:13:08.229778
#9 34.20 Unpacking libpangocairo-1.0-0:arm64 (1.56.3-1) ...
2025-Aug-18 12:13:08.229778
#9 34.25 Selecting previously unselected package libsharpyuv0:arm64.
2025-Aug-18 12:13:08.386194
#9 34.25 Preparing to unpack .../204-libsharpyuv0_1.5.0-0.1_arm64.deb ...
2025-Aug-18 12:13:08.386194
#9 34.26 Unpacking libsharpyuv0:arm64 (1.5.0-0.1) ...
2025-Aug-18 12:13:08.386194
#9 34.30 Selecting previously unselected package libwebp7:arm64.
2025-Aug-18 12:13:08.386194
#9 34.31 Preparing to unpack .../205-libwebp7_1.5.0-0.1_arm64.deb ...
2025-Aug-18 12:13:08.386194
#9 34.31 Unpacking libwebp7:arm64 (1.5.0-0.1) ...
2025-Aug-18 12:13:08.386194
#9 34.40 Selecting previously unselected package libtiff6:arm64.
2025-Aug-18 12:13:08.512935
#9 34.42 Preparing to unpack .../206-libtiff6_4.7.0-3_arm64.deb ...
2025-Aug-18 12:13:08.512935
#9 34.43 Unpacking libtiff6:arm64 (4.7.0-3) ...
2025-Aug-18 12:13:08.512935
#9 34.53 Selecting previously unselected package libpoppler147:arm64.
2025-Aug-18 12:13:08.677510
#9 34.54 Preparing to unpack .../207-libpoppler147_25.03.0-5_arm64.deb ...
2025-Aug-18 12:13:08.677510
#9 34.55 Unpacking libpoppler147:arm64 (25.03.0-5) ...
2025-Aug-18 12:13:08.677510
#9 34.70 Selecting previously unselected package libpoppler-cpp2:arm64.
2025-Aug-18 12:13:08.815586
#9 34.71 Preparing to unpack .../208-libpoppler-cpp2_25.03.0-5_arm64.deb ...
2025-Aug-18 12:13:08.815586
#9 34.71 Unpacking libpoppler-cpp2:arm64 (25.03.0-5) ...
2025-Aug-18 12:13:08.815586
#9 34.77 Selecting previously unselected package libpoppler-dev:arm64.
2025-Aug-18 12:13:08.815586
#9 34.78 Preparing to unpack .../209-libpoppler-dev_25.03.0-5_arm64.deb ...
2025-Aug-18 12:13:08.815586
#9 34.79 Unpacking libpoppler-dev:arm64 (25.03.0-5) ...
2025-Aug-18 12:13:08.815586
#9 34.84 Selecting previously unselected package libpoppler-cpp-dev:arm64.
2025-Aug-18 12:13:08.948746
#9 34.84 Preparing to unpack .../210-libpoppler-cpp-dev_25.03.0-5_arm64.deb ...
2025-Aug-18 12:13:08.948746
#9 34.85 Unpacking libpoppler-cpp-dev:arm64 (25.03.0-5) ...
2025-Aug-18 12:13:08.948746
#9 34.90 Selecting previously unselected package libsasl2-modules:arm64.
2025-Aug-18 12:13:08.948746
#9 34.91 Preparing to unpack .../211-libsasl2-modules_2.1.28+dfsg1-9_arm64.deb ...
2025-Aug-18 12:13:08.948746
#9 34.92 Unpacking libsasl2-modules:arm64 (2.1.28+dfsg1-9) ...
2025-Aug-18 12:13:08.948746
#9 34.97 Selecting previously unselected package libxcomposite1:arm64.
2025-Aug-18 12:13:09.063209
2025-Aug-18 12:13:09.073176
#9 34.97 Preparing to unpack .../212-libxcomposite1_1%3a0.4.6-1_arm64.deb ...
2025-Aug-18 12:13:09.073176
#9 34.98 Unpacking libxcomposite1:arm64 (1:0.4.6-1) ...
2025-Aug-18 12:13:09.073176
#9 35.03 Selecting previously unselected package libxfixes3:arm64.
2025-Aug-18 12:13:09.073176
#9 35.03 Preparing to unpack .../213-libxfixes3_1%3a6.0.0-2+b4_arm64.deb ...
2025-Aug-18 12:13:09.073176
#9 35.04 Unpacking libxfixes3:arm64 (1:6.0.0-2+b4) ...
2025-Aug-18 12:13:09.073176
#9 35.08 Selecting previously unselected package libxcursor1:arm64.
2025-Aug-18 12:13:09.168744
#9 35.09 Preparing to unpack .../214-libxcursor1_1%3a1.2.3-1_arm64.deb ...
2025-Aug-18 12:13:09.168744
#9 35.09 Unpacking libxcursor1:arm64 (1:1.2.3-1) ...
2025-Aug-18 12:13:09.168744
#9 35.14 Selecting previously unselected package libxdamage1:arm64.
2025-Aug-18 12:13:09.168744
#9 35.15 Preparing to unpack .../215-libxdamage1_1%3a1.1.6-1+b2_arm64.deb ...
2025-Aug-18 12:13:09.168744
#9 35.15 Unpacking libxdamage1:arm64 (1:1.1.6-1+b2) ...
2025-Aug-18 12:13:09.168744
#9 35.19 Selecting previously unselected package libxrandr2:arm64.
2025-Aug-18 12:13:09.394044
#9 35.19 Preparing to unpack .../216-libxrandr2_2%3a1.5.4-1+b3_arm64.deb ...
2025-Aug-18 12:13:09.394044
#9 35.20 Unpacking libxrandr2:arm64 (2:1.5.4-1+b3) ...
2025-Aug-18 12:13:09.394044
#9 35.25 Selecting previously unselected package manpages-dev.
2025-Aug-18 12:13:09.394044
#9 35.25 Preparing to unpack .../217-manpages-dev_6.9.1-1_all.deb ...
2025-Aug-18 12:13:09.394044
#9 35.26 Unpacking manpages-dev (6.9.1-1) ...
2025-Aug-18 12:13:09.526347
#9 35.55 Selecting previously unselected package poppler-utils.
2025-Aug-18 12:13:09.647982
#9 35.55 Preparing to unpack .../218-poppler-utils_25.03.0-5_arm64.deb ...
2025-Aug-18 12:13:09.647982
#9 35.56 Unpacking poppler-utils (25.03.0-5) ...
2025-Aug-18 12:13:09.647982
#9 35.67 Selecting previously unselected package psmisc.
2025-Aug-18 12:13:09.802656
#9 35.67 Preparing to unpack .../219-psmisc_23.7-2_arm64.deb ...
2025-Aug-18 12:13:09.802656
#9 35.68 Unpacking psmisc (23.7-2) ...
2025-Aug-18 12:13:09.802656
#9 35.75 Selecting previously unselected package publicsuffix.
2025-Aug-18 12:13:09.802656
#9 35.76 Preparing to unpack .../220-publicsuffix_20250328.1952-0.1_all.deb ...
2025-Aug-18 12:13:09.802656
#9 35.76 Unpacking publicsuffix (20250328.1952-0.1) ...
2025-Aug-18 12:13:09.802656
#9 35.82 Selecting previously unselected package shared-mime-info.
2025-Aug-18 12:13:09.908676
#9 35.83 Preparing to unpack .../221-shared-mime-info_2.4-5+b2_arm64.deb ...
2025-Aug-18 12:13:09.908676
#9 35.83 Unpacking shared-mime-info (2.4-5+b2) ...
2025-Aug-18 12:13:09.908676
#9 35.92 Selecting previously unselected package systemd-cryptsetup.
2025-Aug-18 12:13:09.908676
#9 35.92 Preparing to unpack .../222-systemd-cryptsetup_257.7-1_arm64.deb ...
2025-Aug-18 12:13:09.908676
#9 35.93 Unpacking systemd-cryptsetup (257.7-1) ...
2025-Aug-18 12:13:10.011602
#9 36.02 Selecting previously unselected package xdg-user-dirs.
2025-Aug-18 12:13:10.011602
#9 36.02 Preparing to unpack .../223-xdg-user-dirs_0.18-2_arm64.deb ...
2025-Aug-18 12:13:10.011602
#9 36.03 Unpacking xdg-user-dirs (0.18-2) ...
2025-Aug-18 12:13:10.111350
#9 36.08 Selecting previously unselected package gnupg-utils.
2025-Aug-18 12:13:10.111350
#9 36.08 Preparing to unpack .../224-gnupg-utils_2.4.7-21+b3_arm64.deb ...
2025-Aug-18 12:13:10.111350
#9 36.09 Unpacking gnupg-utils (2.4.7-21+b3) ...
2025-Aug-18 12:13:10.111350
#9 36.13 Selecting previously unselected package libegl1:arm64.
2025-Aug-18 12:13:10.213061
#9 36.14 Preparing to unpack .../225-libegl1_1.7.0-1+b2_arm64.deb ...
2025-Aug-18 12:13:10.213061
#9 36.14 Unpacking libegl1:arm64 (1.7.0-1+b2) ...
2025-Aug-18 12:13:10.213061
#9 36.19 Setting up libexpat1:arm64 (2.7.1-2) ...
2025-Aug-18 12:13:10.213061
#9 36.21 Setting up libgraphite2-3:arm64 (1.3.14-2+b1) ...
2025-Aug-18 12:13:10.213061
#9 36.21 Setting up liblcms2-2:arm64 (2.16-2) ...
2025-Aug-18 12:13:10.213061
#9 36.22 Setting up libpixman-1-0:arm64 (0.44.0-3) ...
2025-Aug-18 12:13:10.213061
#9 36.22 Setting up libsharpyuv0:arm64 (1.5.0-0.1) ...
2025-Aug-18 12:13:10.213061
#9 36.23 Setting up libwayland-server0:arm64 (1.23.1-3) ...
2025-Aug-18 12:13:10.314356
#9 36.24 Setting up systemd-sysv (257.7-1) ...
2025-Aug-18 12:13:10.314356
#9 36.28 Setting up libxau6:arm64 (1:1.0.11-1) ...
2025-Aug-18 12:13:10.314356
#9 36.29 Setting up libxdmcp6:arm64 (1:1.1.5-1) ...
2025-Aug-18 12:13:10.314356
#9 36.30 Setting up libnpth0t64:arm64 (1.8-3) ...
2025-Aug-18 12:13:10.314356
#9 36.31 Setting up libkeyutils1:arm64 (1.6.3-6) ...
2025-Aug-18 12:13:10.314356
#9 36.32 Setting up libxcb1:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:10.314356
#9 36.33 Setting up libxcb-xfixes0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:10.314356
#9 36.34 Setting up liblerc4:arm64 (4.0.0+ds-5) ...
2025-Aug-18 12:13:10.431245
2025-Aug-18 12:13:10.440241
#9 36.34 Setting up libgpg-error0:arm64 (1.51-4) ...
2025-Aug-18 12:13:10.440241
#9 36.36 Setting up libdatrie1:arm64 (0.2.13-3+b1) ...
2025-Aug-18 12:13:10.440241
#9 36.36 Setting up libgdbm-compat4t64:arm64 (1.24-2) ...
2025-Aug-18 12:13:10.440241
#9 36.37 Setting up xdg-user-dirs (0.18-2) ...
2025-Aug-18 12:13:10.440241
#9 36.39 Setting up psmisc (23.7-2) ...
2025-Aug-18 12:13:10.440241
#9 36.40 Setting up libxcb-render0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:10.440241
#9 36.41 Setting up manpages (6.9.1-1) ...
2025-Aug-18 12:13:10.440241
#9 36.41 Setting up libglvnd0:arm64 (1.7.0-1+b2) ...
2025-Aug-18 12:13:10.440241
#9 36.42 Setting up libbrotli1:arm64 (1.1.0-2+b7) ...
2025-Aug-18 12:13:10.440241
#9 36.43 Setting up libedit2:arm64 (3.1-20250104-1) ...
2025-Aug-18 12:13:10.440241
#9 36.43 Setting up libsasl2-modules:arm64 (2.1.28+dfsg1-9) ...
2025-Aug-18 12:13:10.440241
#9 36.45 Setting up binutils-common:arm64 (2.44-3) ...
2025-Aug-18 12:13:10.592214
#9 36.46 Setting up x11-common (1:7.7+24) ...
2025-Aug-18 12:13:10.636329
#9 36.66 debconf: unable to initialize frontend: Dialog
2025-Aug-18 12:13:10.737168
#9 36.66 debconf: (TERM is not set, so the dialog frontend is not usable.)
2025-Aug-18 12:13:10.737168
#9 36.66 debconf: falling back to frontend: Readline
2025-Aug-18 12:13:10.737168
#9 36.67 debconf: unable to initialize frontend: Readline
2025-Aug-18 12:13:10.737168
#9 36.67 debconf: (This frontend requires a controlling tty.)
2025-Aug-18 12:13:10.737168
#9 36.67 debconf: falling back to frontend: Teletype
2025-Aug-18 12:13:10.737168
#9 36.68 debconf: unable to initialize frontend: Teletype
2025-Aug-18 12:13:10.737168
#9 36.68 debconf: (This frontend requires a controlling tty.)
2025-Aug-18 12:13:10.737168
#9 36.68 debconf: falling back to frontend: Noninteractive
2025-Aug-18 12:13:10.737168
#9 36.71 invoke-rc.d: could not determine current runlevel
2025-Aug-18 12:13:10.737168
#9 36.72 invoke-rc.d: policy-rc.d denied execution of start.
2025-Aug-18 12:13:10.737168
#9 36.73 Setting up libsensors-config (1:3.6.2-2) ...
2025-Aug-18 12:13:10.737168
#9 36.74 Setting up libnghttp2-14:arm64 (1.64.0-1.1) ...
2025-Aug-18 12:13:10.737168
#9 36.75 Setting up libdeflate0:arm64 (1.23-2) ...
2025-Aug-18 12:13:10.737168
#9 36.76 Setting up linux-libc-dev (6.12.41-1) ...
2025-Aug-18 12:13:10.837702
#9 36.77 Setting up libctf-nobfd0:arm64 (2.44-3) ...
2025-Aug-18 12:13:10.837702
#9 36.78 Setting up libgcrypt20:arm64 (1.11.0-7) ...
2025-Aug-18 12:13:10.837702
#9 36.79 Setting up libnss-systemd:arm64 (257.7-1) ...
2025-Aug-18 12:13:10.837702
#9 36.81 Setting up krb5-locales (1.21.3-5) ...
2025-Aug-18 12:13:10.837702
#9 36.82 Setting up libxcb-shm0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:10.837702
#9 36.82 Setting up libcom-err2:arm64 (1.47.2-3+b3) ...
2025-Aug-18 12:13:10.837702
#9 36.83 Setting up libgomp1:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:10.837702
#9 36.84 Setting up bzip2 (1.0.8-6) ...
2025-Aug-18 12:13:10.837702
#9 36.84 Setting up libldap-common (2.6.10+dfsg-1) ...
2025-Aug-18 12:13:10.837702
#9 36.85 Setting up libjbig0:arm64 (2.1-6.1+b2) ...
2025-Aug-18 12:13:10.837702
#9 36.86 Setting up libsframe1:arm64 (2.44-3) ...
2025-Aug-18 12:13:10.943340
#9 36.87 Setting up libfakeroot:arm64 (********-1) ...
2025-Aug-18 12:13:10.943340
#9 36.88 Setting up libelf1t64:arm64 (0.192-4) ...
2025-Aug-18 12:13:10.943340
#9 36.89 Setting up libjansson4:arm64 (2.14-2+b3) ...
2025-Aug-18 12:13:10.943340
#9 36.89 Setting up poppler-data (0.4.12-1) ...
2025-Aug-18 12:13:10.943340
#9 36.92 Setting up libkrb5support0:arm64 (1.21.3-5) ...
2025-Aug-18 12:13:10.943340
#9 36.93 Setting up libsasl2-modules-db:arm64 (2.1.28+dfsg1-9) ...
2025-Aug-18 12:13:10.943340
#9 36.94 Setting up fakeroot (********-1) ...
2025-Aug-18 12:13:10.943340
#9 36.95 update-alternatives: using /usr/bin/fakeroot-sysv to provide /usr/bin/fakeroot (fakeroot) in auto mode
2025-Aug-18 12:13:10.943340
#9 36.95 update-alternatives: warning: skip creation of /usr/share/man/man1/fakeroot.1.gz because associated file /usr/share/man/man1/fakeroot-sysv.1.gz (of link group fakeroot) doesn't exist
2025-Aug-18 12:13:10.943340
#9 36.95 update-alternatives: warning: skip creation of /usr/share/man/man1/faked.1.gz because associated file /usr/share/man/man1/faked-sysv.1.gz (of link group fakeroot) doesn't exist
2025-Aug-18 12:13:10.943340
#9 36.95 update-alternatives: warning: skip creation of /usr/share/man/es/man1/fakeroot.1.gz because associated file /usr/share/man/es/man1/fakeroot-sysv.1.gz (of link group fakeroot) doesn't exist
2025-Aug-18 12:13:10.943340
#9 36.96 update-alternatives: warning: skip creation of /usr/share/man/es/man1/faked.1.gz because associated file /usr/share/man/es/man1/faked-sysv.1.gz (of link group fakeroot) doesn't exist
2025-Aug-18 12:13:10.943340
#9 36.96 update-alternatives: warning: skip creation of /usr/share/man/fr/man1/fakeroot.1.gz because associated file /usr/share/man/fr/man1/fakeroot-sysv.1.gz (of link group fakeroot) doesn't exist
2025-Aug-18 12:13:10.943340
#9 36.96 update-alternatives: warning: skip creation of /usr/share/man/fr/man1/faked.1.gz because associated file /usr/share/man/fr/man1/faked-sysv.1.gz (of link group fakeroot) doesn't exist
2025-Aug-18 12:13:10.943340
#9 36.96 update-alternatives: warning: skip creation of /usr/share/man/sv/man1/fakeroot.1.gz because associated file /usr/share/man/sv/man1/fakeroot-sysv.1.gz (of link group fakeroot) doesn't exist
2025-Aug-18 12:13:10.943340
#9 36.96 update-alternatives: warning: skip creation of /usr/share/man/sv/man1/faked.1.gz because associated file /usr/share/man/sv/man1/faked-sysv.1.gz (of link group fakeroot) doesn't exist
2025-Aug-18 12:13:10.943340
#9 36.96 Setting up libxcb-present0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:11.049198
#9 36.97 Setting up libasound2-data (1.2.14-1) ...
2025-Aug-18 12:13:11.049198
#9 36.98 Setting up libz3-4:arm64 (4.13.3-1) ...
2025-Aug-18 12:13:11.049198
#9 36.99 Setting up libglib2.0-data (2.84.3-1) ...
2025-Aug-18 12:13:11.049198
#9 37.00 Setting up rpcsvc-proto (1.4.3-1+b1) ...
2025-Aug-18 12:13:11.049198
#9 37.00 Setting up linux-sysctl-defaults (4.12) ...
2025-Aug-18 12:13:11.049198
#9 37.01 Setting up libasound2t64:arm64 (1.2.14-1) ...
2025-Aug-18 12:13:11.049198
#9 37.02 Setting up libjpeg62-turbo:arm64 (1:2.1.5-4) ...
2025-Aug-18 12:13:11.049198
#9 37.03 Setting up libx11-data (2:1.8.12-1) ...
2025-Aug-18 12:13:11.049198
#9 37.03 Setting up make (4.4.1-2) ...
2025-Aug-18 12:13:11.049198
#9 37.04 Setting up libmpfr6:arm64 (4.2.2-1) ...
2025-Aug-18 12:13:11.049198
#9 37.05 Setting up libnspr4:arm64 (2:4.36-1) ...
2025-Aug-18 12:13:11.049198
#9 37.06 Setting up gnupg-l10n (2.4.7-21) ...
2025-Aug-18 12:13:11.049198
#9 37.06 Setting up libxcb-sync1:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:11.049198
#9 37.07 Setting up libavahi-common-data:arm64 (0.8-16) ...
2025-Aug-18 12:13:11.183221
#9 37.08 Setting up libdbus-1-3:arm64 (1.16.2-2) ...
2025-Aug-18 12:13:11.183221
#9 37.09 Setting up xz-utils (5.8.1-1) ...
2025-Aug-18 12:13:11.183221
#9 37.09 update-alternatives: using /usr/bin/xz to provide /usr/bin/lzma (lzma) in auto mode
2025-Aug-18 12:13:11.183221
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzma.1.gz because associated file /usr/share/man/man1/xz.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:11.183221
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/unlzma.1.gz because associated file /usr/share/man/man1/unxz.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:11.183221
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzcat.1.gz because associated file /usr/share/man/man1/xzcat.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:11.183221
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzmore.1.gz because associated file /usr/share/man/man1/xzmore.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:11.183221
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzless.1.gz because associated file /usr/share/man/man1/xzless.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:11.183221
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzdiff.1.gz because associated file /usr/share/man/man1/xzdiff.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:11.183221
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzcmp.1.gz because associated file /usr/share/man/man1/xzcmp.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:11.183221
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzgrep.1.gz because associated file /usr/share/man/man1/xzgrep.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:11.183221
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzegrep.1.gz because associated file /usr/share/man/man1/xzegrep.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:11.183221
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzfgrep.1.gz because associated file /usr/share/man/man1/xzfgrep.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:11.183221
#9 37.10 Setting up libfribidi0:arm64 (1.0.16-1) ...
2025-Aug-18 12:13:11.183221
#9 37.11 Setting up libp11-kit0:arm64 (0.25.5-3) ...
2025-Aug-18 12:13:11.183221
#9 37.12 Setting up libproc2-0:arm64 (2:4.0.4-9) ...
2025-Aug-18 12:13:11.183221
#9 37.13 Setting up libunistring5:arm64 (1.3-2) ...
2025-Aug-18 12:13:11.183221
#9 37.13 Setting up fonts-dejavu-mono (2.37-8) ...
2025-Aug-18 12:13:11.183221
#9 37.15 Setting up libpng16-16t64:arm64 (1.6.48-1) ...
2025-Aug-18 12:13:11.183221
#9 37.16 Setting up libmpc3:arm64 (1.3.1-1+b3) ...
2025-Aug-18 12:13:11.183221
#9 37.17 Setting up systemd-timesyncd (257.7-1) ...
2025-Aug-18 12:13:11.183221
#9 37.20 Creating group 'systemd-timesync' with GID 997.
2025-Aug-18 12:13:11.334887
#9 37.20 Creating user 'systemd-timesync' (systemd Time Synchronization) with UID 997 and GID 997.
2025-Aug-18 12:13:11.483117
#9 37.50 Created symlink '/etc/systemd/system/dbus-org.freedesktop.timesync1.service' → '/usr/lib/systemd/system/systemd-timesyncd.service'.
2025-Aug-18 12:13:11.483117
#9 37.50 Created symlink '/etc/systemd/system/sysinit.target.wants/systemd-timesyncd.service' → '/usr/lib/systemd/system/systemd-timesyncd.service'.
2025-Aug-18 12:13:11.588343
#9 37.52 Setting up libatomic1:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:11.588343
#9 37.53 Setting up patch (2.8-2) ...
2025-Aug-18 12:13:11.588343
#9 37.54 Setting up fonts-dejavu-core (2.37-8) ...
2025-Aug-18 12:13:11.588343
#9 37.59 Setting up libsensors5:arm64 (1:3.6.2-2) ...
2025-Aug-18 12:13:11.588343
#9 37.59 Setting up libk5crypto3:arm64 (1.21.3-5) ...
2025-Aug-18 12:13:11.588343
#9 37.60 Setting up libsasl2-2:arm64 (2.1.28+dfsg1-9) ...
2025-Aug-18 12:13:11.588343
#9 37.61 Setting up libnghttp3-9:arm64 (1.8.0-1) ...
2025-Aug-18 12:13:11.690027
#9 37.62 Setting up libwebp7:arm64 (1.5.0-0.1) ...
2025-Aug-18 12:13:11.690027
#9 37.62 Setting up libubsan1:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:11.690027
#9 37.63 Setting up alsa-topology-conf (1.2.5.1-3) ...
2025-Aug-18 12:13:11.690027
#9 37.64 Setting up perl-modules-5.40 (5.40.1-6) ...
2025-Aug-18 12:13:11.690027
#9 37.64 Setting up libxshmfence1:arm64 (1.3.3-1) ...
2025-Aug-18 12:13:11.690027
#9 37.65 Setting up libhwasan0:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:11.690027
#9 37.66 Setting up at-spi2-common (2.56.2-1) ...
2025-Aug-18 12:13:11.690027
#9 37.67 Setting up gpgv (2.4.7-21+b3) ...
2025-Aug-18 12:13:11.690027
#9 37.67 Setting up libcrypt-dev:arm64 (1:4.4.38-1) ...
2025-Aug-18 12:13:11.690027
#9 37.69 Setting up libtiff6:arm64 (4.7.0-3) ...
2025-Aug-18 12:13:11.690027
#9 37.70 Setting up libxcb-randr0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:11.690027
#9 37.70 Setting up dbus-session-bus-common (1.16.2-2) ...
2025-Aug-18 12:13:11.690027
#9 37.71 Setting up libasan8:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:11.796584
#9 37.72 Setting up libassuan9:arm64 (3.0.2-2) ...
2025-Aug-18 12:13:11.806004
#9 37.73 Setting up procps (2:4.0.4-9) ...
2025-Aug-18 12:13:11.806004
#9 37.76 Setting up gpgconf (2.4.7-21+b3) ...
2025-Aug-18 12:13:11.806004
#9 37.77 Setting up libtasn1-6:arm64 (4.20.0-2) ...
2025-Aug-18 12:13:11.806004
#9 37.77 Setting up libopenjp2-7:arm64 (2.5.3-2) ...
2025-Aug-18 12:13:11.806004
#9 37.78 Setting up libx11-6:arm64 (2:1.8.12-1) ...
2025-Aug-18 12:13:11.806004
#9 37.79 Setting up libthai-data (0.1.29-2) ...
2025-Aug-18 12:13:11.806004
#9 37.80 Setting up libngtcp2-16:arm64 (1.11.0-1) ...
2025-Aug-18 12:13:11.806004
#9 37.81 Setting up libkrb5-3:arm64 (1.21.3-5) ...
2025-Aug-18 12:13:11.806004
#9 37.82 Setting up libssh2-1t64:arm64 (1.11.1-1) ...
2025-Aug-18 12:13:11.897036
#9 37.83 Setting up libtsan2:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:11.897036
#9 37.84 Setting up libbinutils:arm64 (2.44-3) ...
2025-Aug-18 12:13:11.897036
#9 37.85 Setting up dbus-system-bus-common (1.16.2-2) ...
2025-Aug-18 12:13:11.897036
#9 37.86 Creating group 'messagebus' with GID 996.
2025-Aug-18 12:13:11.897036
#9 37.86 Creating user 'messagebus' (System Message Bus) with UID 996 and GID 996.
2025-Aug-18 12:13:11.897036
#9 37.89 Setting up libisl23:arm64 (0.27-1) ...
2025-Aug-18 12:13:11.897036
#9 37.89 Setting up libc-dev-bin (2.41-12) ...
2025-Aug-18 12:13:11.897036
#9 37.90 Setting up libgpg-error-l10n (1.51-4) ...
2025-Aug-18 12:13:11.897036
#9 37.91 Setting up libdrm-common (2.4.124-2) ...
2025-Aug-18 12:13:11.897036
#9 37.92 Setting up libxcomposite1:arm64 (1:0.4.6-1) ...
2025-Aug-18 12:13:12.000983
#9 37.93 Setting up libjson-c5:arm64 (0.18+ds-1) ...
2025-Aug-18 12:13:12.000983
#9 37.94 Setting up publicsuffix (20250328.1952-0.1) ...
2025-Aug-18 12:13:12.000983
#9 37.95 Setting up libxml2:arm64 (2.12.7+dfsg+really2.9.14-2.1) ...
2025-Aug-18 12:13:12.000983
#9 37.96 Setting up libcc1-0:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:12.000983
#9 37.97 Setting up libldap2:arm64 (2.6.10+dfsg-1) ...
2025-Aug-18 12:13:12.000983
#9 37.98 Setting up dbus-bin (1.16.2-2) ...
2025-Aug-18 12:13:12.000983
#9 37.98 Setting up liblocale-gettext-perl (1.07-7+b1) ...
2025-Aug-18 12:13:12.000983
#9 37.99 Setting up liblsan0:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:12.000983
#9 38.00 Setting up libitm1:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:12.000983
#9 38.01 Setting up libkmod2:arm64 (34.2-2) ...
2025-Aug-18 12:13:12.000983
#9 38.01 Setting up libwayland-client0:arm64 (1.23.1-3) ...
2025-Aug-18 12:13:12.000983
#9 38.02 Setting up libctf0:arm64 (2.44-3) ...
2025-Aug-18 12:13:12.221756
#9 38.03 Setting up libksba8:arm64 (1.6.7-2+b1) ...
2025-Aug-18 12:13:12.221756
#9 38.04 Setting up pinentry-curses (1.3.1-2) ...
2025-Aug-18 12:13:12.221756
#9 38.05 Setting up libxcb-dri3-0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:12.232130
#9 38.06 Setting up libllvm19:arm64 (1:19.1.7-3+b1) ...
2025-Aug-18 12:13:12.232130
#9 38.07 Setting up manpages-dev (6.9.1-1) ...
2025-Aug-18 12:13:12.232130
#9 38.08 Setting up libx11-xcb1:arm64 (2:1.8.12-1) ...
2025-Aug-18 12:13:12.232130
#9 38.08 Setting up libxdamage1:arm64 (1:1.1.6-1+b2) ...
2025-Aug-18 12:13:12.232130
#9 38.09 Setting up gpg-agent (2.4.7-21+b3) ...
2025-Aug-18 12:13:12.247183
#9 38.27 Created symlink '/etc/systemd/user/sockets.target.wants/gpg-agent-browser.socket' → '/usr/lib/systemd/user/gpg-agent-browser.socket'.
2025-Aug-18 12:13:12.400714
#9 38.42 Created symlink '/etc/systemd/user/sockets.target.wants/gpg-agent-extra.socket' → '/usr/lib/systemd/user/gpg-agent-extra.socket'.
2025-Aug-18 12:13:12.411930
2025-Aug-18 12:13:12.540151
#9 38.56 Created symlink '/etc/systemd/user/sockets.target.wants/gpg-agent-ssh.socket' → '/usr/lib/systemd/user/gpg-agent-ssh.socket'.
2025-Aug-18 12:13:12.680819
#9 38.70 Created symlink '/etc/systemd/user/sockets.target.wants/gpg-agent.socket' → '/usr/lib/systemd/user/gpg-agent.socket'.
2025-Aug-18 12:13:12.809973
#9 38.71 Setting up libxrender1:arm64 (1:0.9.12-1) ...
2025-Aug-18 12:13:12.809973
#9 38.72 Setting up alsa-ucm-conf (1.2.14-1) ...
2025-Aug-18 12:13:12.809973
#9 38.72 Setting up fontconfig-config (2.15.0-2.3) ...
2025-Aug-18 12:13:12.809973
#9 38.83 debconf: unable to initialize frontend: Dialog
2025-Aug-18 12:13:12.809973
#9 38.83 debconf: (TERM is not set, so the dialog frontend is not usable.)
2025-Aug-18 12:13:12.908003
#9 38.83 debconf: falling back to frontend: Readline
2025-Aug-18 12:13:12.908003
#9 38.84 debconf: unable to initialize frontend: Readline
2025-Aug-18 12:13:12.908003
#9 38.84 debconf: (This frontend requires a controlling tty.)
2025-Aug-18 12:13:12.908003
#9 38.84 debconf: falling back to frontend: Teletype
2025-Aug-18 12:13:12.908003
#9 38.85 debconf: unable to initialize frontend: Teletype
2025-Aug-18 12:13:12.908003
#9 38.85 debconf: (This frontend requires a controlling tty.)
2025-Aug-18 12:13:12.908003
#9 38.85 debconf: falling back to frontend: Noninteractive
2025-Aug-18 12:13:12.908003
#9 38.92 Setting up gpgsm (2.4.7-21+b3) ...
2025-Aug-18 12:13:12.908003
#9 38.93 Setting up libavahi-common3:arm64 (0.8-16) ...
2025-Aug-18 12:13:13.029667
#9 38.94 Setting up libxext6:arm64 (2:1.3.4-1+b3) ...
2025-Aug-18 12:13:13.029667
#9 38.95 Setting up binutils-aarch64-linux-gnu (2.44-3) ...
2025-Aug-18 12:13:13.029667
#9 38.96 Setting up libidn2-0:arm64 (2.3.8-2) ...
2025-Aug-18 12:13:13.029667
#9 38.96 Setting up libnss3:arm64 (2:3.110-1) ...
2025-Aug-18 12:13:13.029667
#9 38.97 Setting up dbus-daemon (1.16.2-2) ...
2025-Aug-18 12:13:13.029667
#9 38.99 Setting up libperl5.40:arm64 (5.40.1-6) ...
2025-Aug-18 12:13:13.029667
#9 39.00 Setting up libthai0:arm64 (0.1.29-2+b1) ...
2025-Aug-18 12:13:13.029667
#9 39.01 Setting up perl (5.40.1-6) ...
2025-Aug-18 12:13:13.029667
#9 39.03 Setting up libglib2.0-0t64:arm64 (2.84.3-1) ...
2025-Aug-18 12:13:13.029667
#9 39.05 Setting up libgprofng0:arm64 (2.44-3) ...
2025-Aug-18 12:13:13.263748
#9 39.07 Setting up libfreetype6:arm64 (2.13.3+dfsg-1) ...
2025-Aug-18 12:13:13.263748
#9 39.07 Setting up libxfixes3:arm64 (1:6.0.0-2+b4) ...
2025-Aug-18 12:13:13.263748
#9 39.08 Setting up dbus (1.16.2-2) ...
2025-Aug-18 12:13:13.263748
#9 39.12 invoke-rc.d: could not determine current runlevel
2025-Aug-18 12:13:13.263748
#9 39.13 invoke-rc.d: policy-rc.d denied execution of start.
2025-Aug-18 12:13:13.263748
#9 39.13 Setting up shared-mime-info (2.4-5+b2) ...
2025-Aug-18 12:13:14.874668
#9 40.90 Setting up libgssapi-krb5-2:arm64 (1.21.3-5) ...
2025-Aug-18 12:13:15.020324
#9 40.91 Setting up libxrandr2:arm64 (2:1.5.4-1+b3) ...
2025-Aug-18 12:13:15.033110
#9 40.91 Setting up libdpkg-perl (1.22.21) ...
2025-Aug-18 12:13:15.033110
#9 40.92 Setting up libdrm2:arm64 (2.4.124-2) ...
2025-Aug-18 12:13:15.033110
#9 40.93 Setting up cpp-14-aarch64-linux-gnu (14.2.0-19) ...
2025-Aug-18 12:13:15.033110
#9 40.94 Setting up libpam-systemd:arm64 (257.7-1) ...
2025-Aug-18 12:13:15.033110
#9 41.04 debconf: unable to initialize frontend: Dialog
2025-Aug-18 12:13:15.161805
#9 41.04 debconf: (TERM is not set, so the dialog frontend is not usable.)
2025-Aug-18 12:13:15.161805
#9 41.04 debconf: falling back to frontend: Readline
2025-Aug-18 12:13:15.161805
#9 41.07 debconf: unable to initialize frontend: Readline
2025-Aug-18 12:13:15.161805
#9 41.07 debconf: (This frontend requires a controlling tty.)
2025-Aug-18 12:13:15.161805
#9 41.07 debconf: falling back to frontend: Teletype
2025-Aug-18 12:13:15.161805
#9 41.07 debconf: unable to initialize frontend: Teletype
2025-Aug-18 12:13:15.161805
#9 41.08 debconf: (This frontend requires a controlling tty.)
2025-Aug-18 12:13:15.161805
#9 41.08 debconf: falling back to frontend: Noninteractive
2025-Aug-18 12:13:15.161805
#9 41.18 Setting up libc6-dev:arm64 (2.41-12) ...
2025-Aug-18 12:13:15.346939
#9 41.19 Setting up libharfbuzz0b:arm64 (10.2.0-1+b1) ...
2025-Aug-18 12:13:15.346939
#9 41.20 Setting up libfontconfig1:arm64 (2.15.0-2.3) ...
2025-Aug-18 12:13:15.346939
#9 41.21 Setting up libgcc-14-dev:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:15.346939
#9 41.22 Setting up libstdc++-14-dev:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:15.346939
#9 41.22 Setting up libavahi-client3:arm64 (0.8-16) ...
2025-Aug-18 12:13:15.346939
#9 41.23 Setting up gpg (2.4.7-21+b3) ...
2025-Aug-18 12:13:15.346939
#9 41.37 Created symlink '/etc/systemd/user/sockets.target.wants/keyboxd.socket' → '/usr/lib/systemd/user/keyboxd.socket'.
2025-Aug-18 12:13:15.544265
#9 41.38 Setting up gnupg-utils (2.4.7-21+b3) ...
2025-Aug-18 12:13:15.544265
#9 41.38 Setting up libdrm-amdgpu1:arm64 (2.4.124-2) ...
2025-Aug-18 12:13:15.544265
#9 41.39 Setting up libgnutls30t64:arm64 (3.8.9-3) ...
2025-Aug-18 12:13:15.544265
#9 41.39 Setting up mesa-libgallium:arm64 (25.0.7-2) ...
2025-Aug-18 12:13:15.544265
#9 41.40 Setting up fontconfig (2.15.0-2.3) ...
2025-Aug-18 12:13:15.544265
#9 41.41 Regenerating fonts cache...
2025-Aug-18 12:13:17.452037
done.
2025-Aug-18 12:13:17.556989
#9 43.48 Setting up libatk1.0-0t64:arm64 (2.56.2-1) ...
2025-Aug-18 12:13:17.556989
#9 43.49 Setting up libxi6:arm64 (2:1.8.2-1) ...
2025-Aug-18 12:13:17.556989
#9 43.49 Setting up libgbm1:arm64 (25.0.7-2) ...
2025-Aug-18 12:13:17.556989
#9 43.50 Setting up libfile-fcntllock-perl (0.22-4+b4) ...
2025-Aug-18 12:13:17.556989
#9 43.51 Setting up libalgorithm-diff-perl (1.201-1) ...
2025-Aug-18 12:13:17.556989
#9 43.51 Setting up libxtst6:arm64 (2:1.2.5-1) ...
2025-Aug-18 12:13:17.556989
#9 43.52 Setting up libxcursor1:arm64 (1:1.2.3-1) ...
2025-Aug-18 12:13:17.556989
#9 43.53 Setting up libpango-1.0-0:arm64 (1.56.3-1) ...
2025-Aug-18 12:13:17.556989
#9 43.54 Setting up libpsl5t64:arm64 (0.21.2-1.1+b1) ...
2025-Aug-18 12:13:17.566245
#9 43.55 Setting up binutils (2.44-3) ...
2025-Aug-18 12:13:17.566245
#9 43.55 Setting up libcairo2:arm64 (1.18.4-1+b1) ...
2025-Aug-18 12:13:17.566245
#9 43.56 Setting up dpkg-dev (1.22.21) ...
2025-Aug-18 12:13:17.566245
#9 43.57 Setting up libdconf1:arm64 (0.40.0-5) ...
2025-Aug-18 12:13:17.566245
#9 43.58 Setting up dirmngr (2.4.7-21+b3) ...
2025-Aug-18 12:13:17.707969
#9 43.73 Created symlink '/etc/systemd/user/sockets.target.wants/dirmngr.socket' → '/usr/lib/systemd/user/dirmngr.socket'.
2025-Aug-18 12:13:17.811996
#9 43.75 Setting up dbus-user-session (1.16.2-2) ...
2025-Aug-18 12:13:17.811996
#9 43.76 Setting up cpp-aarch64-linux-gnu (4:14.2.0-1) ...
2025-Aug-18 12:13:17.820399
#9 43.77 Setting up libegl-mesa0:arm64 (25.0.7-2) ...
2025-Aug-18 12:13:17.820399
#9 43.78 Setting up librtmp1:arm64 (2.4+20151223.gitfa8646d.1-2+b5) ...
2025-Aug-18 12:13:17.820399
#9 43.79 Setting up libatspi2.0-0t64:arm64 (2.56.2-1) ...
2025-Aug-18 12:13:17.820399
#9 43.80 Setting up cpp-14 (14.2.0-19) ...
2025-Aug-18 12:13:17.820399
#9 43.81 Setting up cpp (4:14.2.0-1) ...
2025-Aug-18 12:13:17.820399
#9 43.83 Setting up libegl1:arm64 (1.7.0-1+b2) ...
2025-Aug-18 12:13:17.915781
#9 43.85 Setting up gnupg (2.4.7-21) ...
2025-Aug-18 12:13:17.915781
#9 43.85 Setting up libgpgme11t64:arm64 (1.24.2-3) ...
2025-Aug-18 12:13:17.915781
#9 43.86 Setting up gcc-14-aarch64-linux-gnu (14.2.0-19) ...
2025-Aug-18 12:13:17.915781
#9 43.87 Setting up libpangoft2-1.0-0:arm64 (1.56.3-1) ...
2025-Aug-18 12:13:17.915781
#9 43.88 Setting up libalgorithm-diff-xs-perl (0.04-9) ...
2025-Aug-18 12:13:17.915781
#9 43.88 Setting up libcups2t64:arm64 (2.4.10-3) ...
2025-Aug-18 12:13:17.915781
#9 43.89 Setting up libngtcp2-crypto-gnutls8:arm64 (1.11.0-1) ...
2025-Aug-18 12:13:17.915781
#9 43.90 Setting up libpangocairo-1.0-0:arm64 (1.56.3-1) ...
2025-Aug-18 12:13:17.915781
#9 43.91 Setting up libalgorithm-merge-perl (0.08-5) ...
2025-Aug-18 12:13:17.915781
#9 43.92 Setting up libatk-bridge2.0-0t64:arm64 (2.56.2-1) ...
2025-Aug-18 12:13:17.915781
#9 43.93 Setting up gcc-aarch64-linux-gnu (4:14.2.0-1) ...
2025-Aug-18 12:13:17.915781
#9 43.94 Setting up libgpgmepp6t64:arm64 (1.24.2-3) ...
2025-Aug-18 12:13:18.031005
#9 43.95 Setting up g++-14-aarch64-linux-gnu (14.2.0-19) ...
2025-Aug-18 12:13:18.031005
#9 43.96 Setting up wget (1.25.0-2) ...
2025-Aug-18 12:13:18.031005
#9 43.97 Setting up gpg-wks-client (2.4.7-21+b3) ...
2025-Aug-18 12:13:18.031005
#9 43.98 Setting up libcurl3t64-gnutls:arm64 (8.14.1-2) ...
2025-Aug-18 12:13:18.031005
#9 43.98 Setting up dconf-service (0.40.0-5) ...
2025-Aug-18 12:13:18.031005
#9 43.99 Setting up gcc-14 (14.2.0-19) ...
2025-Aug-18 12:13:18.031005
#9 44.00 Setting up g++-aarch64-linux-gnu (4:14.2.0-1) ...
2025-Aug-18 12:13:18.031005
#9 44.01 Setting up g++-14 (14.2.0-19) ...
2025-Aug-18 12:13:18.031005
#9 44.02 Setting up libpoppler147:arm64 (25.03.0-5) ...
2025-Aug-18 12:13:18.031005
#9 44.03 Setting up dconf-gsettings-backend:arm64 (0.40.0-5) ...
2025-Aug-18 12:13:18.031005
#9 44.03 Setting up gcc (4:14.2.0-1) ...
2025-Aug-18 12:13:18.031005
#9 44.05 Setting up libpoppler-cpp2:arm64 (25.03.0-5) ...
2025-Aug-18 12:13:18.135966
#9 44.07 Setting up g++ (4:14.2.0-1) ...
2025-Aug-18 12:13:18.135966
#9 44.08 update-alternatives: using /usr/bin/g++ to provide /usr/bin/c++ (c++) in auto mode
2025-Aug-18 12:13:18.135966
#9 44.08 Setting up build-essential (12.12) ...
2025-Aug-18 12:13:18.135966
#9 44.09 Setting up poppler-utils (25.03.0-5) ...
2025-Aug-18 12:13:18.135966
#9 44.09 Setting up libpoppler-dev:arm64 (25.03.0-5) ...
2025-Aug-18 12:13:18.135966
#9 44.11 Setting up gsettings-desktop-schemas (48.0-1) ...
2025-Aug-18 12:13:18.135966
#9 44.12 Setting up libpoppler-cpp-dev:arm64 (25.03.0-5) ...
2025-Aug-18 12:13:18.135966
#9 44.13 Setting up at-spi2-core (2.56.2-1) ...
2025-Aug-18 12:13:18.135966
#9 44.16 Setting up dmsetup (2:1.02.205-2) ...
2025-Aug-18 12:13:18.281919
#9 44.17 Setting up libdevmapper1.02.1:arm64 (2:1.02.205-2) ...
2025-Aug-18 12:13:18.281919
#9 44.18 Setting up libcryptsetup12:arm64 (2:2.7.5-2) ...
2025-Aug-18 12:13:18.281919
#9 44.19 Setting up systemd-cryptsetup (257.7-1) ...
2025-Aug-18 12:13:18.281919
#9 44.20 Processing triggers for libc-bin (2.41-12) ...
2025-Aug-18 12:13:18.281919
#9 44.30 Processing triggers for systemd (257.7-1) ...
2025-Aug-18 12:13:18.464857
#9 DONE 44.5s
2025-Aug-18 12:13:18.577020
#10 [stage-0 4/9] WORKDIR /app
2025-Aug-18 12:13:18.577020
#10 DONE 0.0s
2025-Aug-18 12:13:18.577020
2025-Aug-18 12:13:18.577020
#11 [stage-0 5/9] COPY pyproject.toml uv.lock* ./
2025-Aug-18 12:13:18.577020
#11 DONE 0.1s
2025-Aug-18 12:13:18.731137
#12 [stage-0 6/9] RUN uv sync --frozen --no-cache
2025-Aug-18 12:13:19.040325
#12 0.460 Using CPython 3.12.11 interpreter at: /usr/local/bin/python3
2025-Aug-18 12:13:19.149970
#12 0.461 Creating virtual environment at: .venv
2025-Aug-18 12:13:19.149970
#12 0.513    Building telekomrbudownloader @ file:///app
2025-Aug-18 12:13:19.149970
#12 0.570 Downloading
2025-Aug-18 12:13:19.330974
pillow (4.3MiB)
2025-Aug-18 12:13:19.330974
#12 0.586 Downloading cryptography (4.0MiB)
2025-Aug-18 12:13:19.330974
#12 0.589 Downloading pdfminer-six (5.4MiB)
2025-Aug-18 12:13:19.330974
#12 0.593 Downloading playwright (42.5MiB)
2025-Aug-18 12:13:19.330974
#12 0.596 Downloading numpy (13.4MiB)
2025-Aug-18 12:13:19.330974
#12 0.597 Downloading pandas (10.7MiB)
2025-Aug-18 12:13:19.330974
#12 0.598 Downloading pypdfium2 (2.7MiB)
2025-Aug-18 12:13:19.330974
#12 0.599 Downloading uvloop (4.4MiB)
2025-Aug-18 12:13:19.330974
#12 0.600 Downloading pydantic-core (1.8MiB)
2025-Aug-18 12:13:19.814496
#12 1.231  Downloading pydantic-core
2025-Aug-18 12:13:20.004905
#12 1.425  Downloading pypdfium2
2025-Aug-18 12:13:20.405578
#12 1.825  Downloading pdfminer-six
2025-Aug-18 12:13:20.520817
#12 1.859  Downloading pillow
2025-Aug-18 12:13:20.520817
#12 1.876  Downloading uvloop
2025-Aug-18 12:13:20.520817
#12 1.939
2025-Aug-18 12:13:20.674153
Downloading cryptography
2025-Aug-18 12:13:22.747227
#12 4.166  Downloading playwright
2025-Aug-18 12:13:23.145169
#12 4.564
2025-Aug-18 12:13:23.296328
Downloading numpy
2025-Aug-18 12:13:23.380671
#12 4.801  Downloading
2025-Aug-18 12:13:23.532245
pandas
2025-Aug-18 12:13:23.540745
2025-Aug-18 12:13:24.234667
#12 5.654
2025-Aug-18 12:13:24.386320
Built telekomrbudownloader @ file:///app
2025-Aug-18 12:13:24.386320
#12 5.661 Prepared 45 packages in 5.16s
2025-Aug-18 12:13:24.386320
#12 5.806 Installed
2025-Aug-18 12:13:24.545096
45 packages in 146ms
2025-Aug-18 12:13:24.545096
#12 5.807  + annotated-types==0.7.0
2025-Aug-18 12:13:24.545096
#12 5.808  + anyio==4.9.0
2025-Aug-18 12:13:24.545096
#12 5.808  + certifi==2025.4.26
2025-Aug-18 12:13:24.545096
#12 5.810  + cffi==1.17.1
2025-Aug-18 12:13:24.545096
#12 5.810  + charset-normalizer==3.4.2
2025-Aug-18 12:13:24.545096
#12 5.810  + click==8.2.1
2025-Aug-18 12:13:24.545096
#12 5.810  + cryptography==45.0.4
2025-Aug-18 12:13:24.545096
#12 5.810  + et-xmlfile==2.0.0
2025-Aug-18 12:13:24.545096
#12 5.810  + fastapi==0.115.12
2025-Aug-18 12:13:24.545096
#12 5.810  + greenlet==3.2.3
2025-Aug-18 12:13:24.545096
#12 5.810  + h11==0.16.0
2025-Aug-18 12:13:24.545096
#12 5.810  + httptools==0.6.4
2025-Aug-18 12:13:24.545096
#12 5.810  + idna==3.10
2025-Aug-18 12:13:24.545096
#12 5.810  + numpy==2.2.6
2025-Aug-18 12:13:24.545096
#12 5.810  + openpyxl==3.1.5
2025-Aug-18 12:13:24.545096
#12 5.810  + pandas==2.3.0
2025-Aug-18 12:13:24.545096
#12 5.810  + pdfminer-six==20250327
2025-Aug-18 12:13:24.545096
#12 5.810  + pdfplumber==0.11.6
2025-Aug-18 12:13:24.545096
#12 5.810  + pillow==11.2.1
2025-Aug-18 12:13:24.545096
#12 5.810  + playwright==1.52.0
2025-Aug-18 12:13:24.545096
#12 5.810  + psutil==7.0.0
2025-Aug-18 12:13:24.545096
#12 5.810  + pycparser==2.22
2025-Aug-18 12:13:24.545096
#12 5.810  + pydantic==2.11.5
2025-Aug-18 12:13:24.545096
#12 5.810  + pydantic-core==2.33.2
2025-Aug-18 12:13:24.545096
#12 5.810  + pydantic-settings==2.9.1
2025-Aug-18 12:13:24.545096
#12 5.810  + pyee==13.0.0
2025-Aug-18 12:13:24.545096
#12 5.810  + pypdfium2==4.30.1
2025-Aug-18 12:13:24.545096
#12 5.810  + python-dateutil==2.9.0.post0
2025-Aug-18 12:13:24.545096
#12 5.810  + python-dotenv==1.1.0
2025-Aug-18 12:13:24.545096
#12 5.810  + python-multipart==0.0.20
2025-Aug-18 12:13:24.545096
#12 5.810  + pytz==2025.2
2025-Aug-18 12:13:24.545096
#12 5.810  + pyyaml==6.0.2
2025-Aug-18 12:13:24.545096
#12 5.810  + requests==2.32.4
2025-Aug-18 12:13:24.545096
#12 5.810  + six==1.17.0
2025-Aug-18 12:13:24.545096
#12 5.810  + sniffio==1.3.1
2025-Aug-18 12:13:24.545096
#12 5.810  + starlette==0.46.2
2025-Aug-18 12:13:24.545096
#12 5.810  + telekomrbudownloader==0.1.0 (from file:///app)
2025-Aug-18 12:13:24.545096
#12 5.810  + typing-extensions==4.14.0
2025-Aug-18 12:13:24.545096
#12 5.810  + typing-inspection==0.4.1
2025-Aug-18 12:13:24.545096
#12 5.810  + tzdata==2025.2
2025-Aug-18 12:13:24.545096
#12 5.810  + urllib3==2.4.0
2025-Aug-18 12:13:24.545096
#12 5.810  + uvicorn==0.34.3
2025-Aug-18 12:13:24.545096
#12 5.810  + uvloop==0.21.0
2025-Aug-18 12:13:24.545096
#12 5.810  + watchfiles==1.0.5
2025-Aug-18 12:13:24.545096
#12 5.810  + websockets==15.0.1
2025-Aug-18 12:13:24.616972
#12 DONE 6.0s
2025-Aug-18 12:13:24.770759
#13 [stage-0 7/9] RUN uv run playwright install --with-deps
2025-Aug-18 12:13:25.372507
#13 0.753 BEWARE: your OS is not officially supported by Playwright; installing dependencies for ubuntu20.04-arm64 as a fallback.
2025-Aug-18 12:13:25.484253
#13 0.754 Installing dependencies...
2025-Aug-18 12:13:25.484253
#13 0.841 Get:1 http://deb.debian.org/debian trixie InRelease [138 kB]
2025-Aug-18 12:13:25.484253
#13 0.865 Get:2 http://deb.debian.org/debian trixie-updates InRelease [47.1 kB]
2025-Aug-18 12:13:25.613696
#13 0.867 Get:3 http://deb.debian.org/debian-security trixie-security InRelease [43.4 kB]
2025-Aug-18 12:13:25.613696
#13 0.905 Get:4 http://deb.debian.org/debian trixie/main arm64 Packages [9604 kB]
2025-Aug-18 12:13:25.613696
#13 0.995 Get:5 http://deb.debian.org/debian trixie-updates/main arm64 Packages [2432 B]
2025-Aug-18 12:13:25.769238
#13 1.000 Get:6 http://deb.debian.org/debian-security trixie-security/main arm64 Packages [9464 B]
2025-Aug-18 12:13:26.633699
#13 2.015 Fetched 9844 kB in 1s (8288 kB/s)
2025-Aug-18 12:13:26.643254
#13 2.015 Reading package lists...
2025-Aug-18 12:13:27.348848
2025-Aug-18 12:13:27.534343
#13 2.765 Reading package lists...
2025-Aug-18 12:13:28.118347
2025-Aug-18 12:13:28.286594
#13 3.517 Building dependency tree...
2025-Aug-18 12:13:28.415133
#13 3.796 Reading state information...
2025-Aug-18 12:13:28.557226
#13 3.843 Package ttf-ubuntu-font-family is not available, but is referred to by another package.
2025-Aug-18 12:13:28.557226
#13 3.843 This may mean that the package is missing, has been obsoleted, or
2025-Aug-18 12:13:28.557226
#13 3.843 is only available from another source
2025-Aug-18 12:13:28.557226
#13 3.843
2025-Aug-18 12:13:28.557226
#13 3.843 Package libgdk-pixbuf2.0-0 is not available, but is referred to by another package.
2025-Aug-18 12:13:28.557226
#13 3.843 This may mean that the package is missing, has been obsoleted, or
2025-Aug-18 12:13:28.557226
#13 3.843 is only available from another source
2025-Aug-18 12:13:28.557226
#13 3.843 However the following packages replace it:
2025-Aug-18 12:13:28.557226
#13 3.843   libgdk-pixbuf-xlib-2.0-0
2025-Aug-18 12:13:28.557226
#13 3.843
2025-Aug-18 12:13:28.557226
#13 3.843 Package libjpeg-turbo8 is not available, but is referred to by another package.
2025-Aug-18 12:13:28.557226
#13 3.843 This may mean that the package is missing, has been obsoleted, or
2025-Aug-18 12:13:28.557226
#13 3.843 is only available from another source
2025-Aug-18 12:13:28.557226
#13 3.843
2025-Aug-18 12:13:28.557226
#13 3.843 Package ttf-unifont is not available, but is referred to by another package.
2025-Aug-18 12:13:28.557226
#13 3.843 This may mean that the package is missing, has been obsoleted, or
2025-Aug-18 12:13:28.557226
#13 3.843 is only available from another source
2025-Aug-18 12:13:28.557226
#13 3.843 However the following packages replace it:
2025-Aug-18 12:13:28.557226
#13 3.843   fonts-unifont
2025-Aug-18 12:13:28.557226
#13 3.843
2025-Aug-18 12:13:28.557226
#13 3.847 E: Package 'libgdk-pixbuf2.0-0' has no installation candidate
2025-Aug-18 12:13:28.557226
#13 3.847 E: Unable to locate package libx264-155
2025-Aug-18 12:13:28.557226
#13 3.847 E: Unable to locate package libenchant1c2a
2025-Aug-18 12:13:28.557226
#13 3.847 E: Unable to locate package libicu66
2025-Aug-18 12:13:28.557226
#13 3.847 E: Package 'libjpeg-turbo8' has no installation candidate
2025-Aug-18 12:13:28.557226
#13 3.847 E: Unable to locate package libvpx6
2025-Aug-18 12:13:28.557226
#13 3.847 E: Unable to locate package libwebp6
2025-Aug-18 12:13:28.557226
#13 3.847 E: Package 'ttf-unifont' has no installation candidate
2025-Aug-18 12:13:28.557226
#13 3.847 E: Package 'ttf-ubuntu-font-family' has no installation candidate
2025-Aug-18 12:13:28.557226
#13 3.851 Failed to install browsers
2025-Aug-18 12:13:28.557226
#13 3.851 Error: Installation process exited with code: 100
2025-Aug-18 12:13:28.557226
#13 ERROR: process "/bin/sh -c uv run playwright install --with-deps" did not complete successfully: exit code: 1
2025-Aug-18 12:13:28.600347
------
2025-Aug-18 12:13:28.600347
> [stage-0 7/9] RUN uv run playwright install --with-deps:
2025-Aug-18 12:13:28.600347
3.847 E: Unable to locate package libx264-155
2025-Aug-18 12:13:28.600347
3.847 E: Unable to locate package libenchant1c2a
2025-Aug-18 12:13:28.600347
3.847 E: Unable to locate package libicu66
2025-Aug-18 12:13:28.600347
3.847 E: Package 'libjpeg-turbo8' has no installation candidate
2025-Aug-18 12:13:28.600347
3.847 E: Unable to locate package libvpx6
2025-Aug-18 12:13:28.600347
3.847 E: Unable to locate package libwebp6
2025-Aug-18 12:13:28.600347
3.847 E: Package 'ttf-unifont' has no installation candidate
2025-Aug-18 12:13:28.600347
3.847 E: Package 'ttf-ubuntu-font-family' has no installation candidate
2025-Aug-18 12:13:28.600347
3.851 Failed to install browsers
2025-Aug-18 12:13:28.600347
3.851 Error: Installation process exited with code: 100
2025-Aug-18 12:13:28.600347
------
2025-Aug-18 12:13:28.613757
Dockerfile:30
2025-Aug-18 12:13:28.613757
--------------------
2025-Aug-18 12:13:28.613757
28 |     # Install dependencies with uv
2025-Aug-18 12:13:28.613757
29 |     RUN uv sync --frozen --no-cache
2025-Aug-18 12:13:28.613757
30 | >>> RUN uv run playwright install --with-deps
2025-Aug-18 12:13:28.613757
31 |
2025-Aug-18 12:13:28.613757
32 |     # Create data directory for PDFs
2025-Aug-18 12:13:28.613757
--------------------
2025-Aug-18 12:13:28.613757
ERROR: failed to solve: process "/bin/sh -c uv run playwright install --with-deps" did not complete successfully: exit code: 1
2025-Aug-18 12:13:28.613757
exit status 1
2025-Aug-18 12:13:28.699098
Oops something is not okay, are you okay? 😢
2025-Aug-18 12:13:28.718197
#0 building with "default" instance using docker driver
2025-Aug-18 12:13:28.718197
2025-Aug-18 12:13:28.718197
#1 [internal] load build definition from Dockerfile
2025-Aug-18 12:13:28.718197
#1 transferring dockerfile: 1.29kB done
2025-Aug-18 12:13:28.718197
#1 DONE 0.0s
2025-Aug-18 12:13:28.718197
2025-Aug-18 12:13:28.718197
#2 [internal] load metadata for docker.io/library/python:3.12-slim
2025-Aug-18 12:13:28.718197
#2 DONE 1.4s
2025-Aug-18 12:13:28.718197
2025-Aug-18 12:13:28.718197
#3 [internal] load metadata for ghcr.io/astral-sh/uv:latest
2025-Aug-18 12:13:28.718197
#3 DONE 1.5s
2025-Aug-18 12:13:28.718197
2025-Aug-18 12:13:28.718197
#4 [internal] load .dockerignore
2025-Aug-18 12:13:28.718197
#4 transferring context: 2B done
2025-Aug-18 12:13:28.718197
#4 DONE 0.0s
2025-Aug-18 12:13:28.718197
2025-Aug-18 12:13:28.718197
#5 [stage-0 1/9] FROM docker.io/library/python:3.12-slim@sha256:d67a7b66b989ad6b6d6b10d428dcc5e0bfc3e5f88906e67d490c4d3daac57047
2025-Aug-18 12:13:28.718197
#5 resolve docker.io/library/python:3.12-slim@sha256:d67a7b66b989ad6b6d6b10d428dcc5e0bfc3e5f88906e67d490c4d3daac57047 0.0s done
2025-Aug-18 12:13:28.718197
#5 ...
2025-Aug-18 12:13:28.718197
2025-Aug-18 12:13:28.718197
#6 [internal] load build context
2025-Aug-18 12:13:28.718197
#6 transferring context: 350.10kB 0.0s done
2025-Aug-18 12:13:28.718197
#6 DONE 0.1s
2025-Aug-18 12:13:28.718197
2025-Aug-18 12:13:28.718197
#5 [stage-0 1/9] FROM docker.io/library/python:3.12-slim@sha256:d67a7b66b989ad6b6d6b10d428dcc5e0bfc3e5f88906e67d490c4d3daac57047
2025-Aug-18 12:13:28.718197
#5 sha256:960281d68bf0f3705eb82c5694af05c40f74e6336b9e9ae4643f134de19f1b9c 5.59kB / 5.59kB done
2025-Aug-18 12:13:28.718197
#5 sha256:9a6263cdeaa5d408640880103ee36920ef814974ca8e2674412ad6460e8968c9 0B / 30.14MB 0.2s
2025-Aug-18 12:13:28.718197
#5 sha256:d67a7b66b989ad6b6d6b10d428dcc5e0bfc3e5f88906e67d490c4d3daac57047 10.37kB / 10.37kB done
2025-Aug-18 12:13:28.718197
#5 sha256:8f5e6d99738f82eed0a35db3adf5e20358c324b62a5f25af486928867a028e66 1.75kB / 1.75kB done
2025-Aug-18 12:13:28.718197
#5 sha256:3cd5add3ba3330f959ed1b27096bfb25e2f29a4cf327009d69855a903036f3af 0B / 1.27MB 0.3s
2025-Aug-18 12:13:28.718197
#5 sha256:7b5dfa58994b71320b90f4e4e2ced96f1ae3f6468038e81cf1e27a0e5d7b3c8d 0B / 12.04MB 0.3s
2025-Aug-18 12:13:28.718197
#5 sha256:9a6263cdeaa5d408640880103ee36920ef814974ca8e2674412ad6460e8968c9 5.24MB / 30.14MB 0.4s
2025-Aug-18 12:13:28.718197
#5 sha256:9a6263cdeaa5d408640880103ee36920ef814974ca8e2674412ad6460e8968c9 12.58MB / 30.14MB 0.5s
2025-Aug-18 12:13:28.718197
#5 sha256:9a6263cdeaa5d408640880103ee36920ef814974ca8e2674412ad6460e8968c9 18.87MB / 30.14MB 0.6s
2025-Aug-18 12:13:28.718197
#5 sha256:9a6263cdeaa5d408640880103ee36920ef814974ca8e2674412ad6460e8968c9 25.48MB / 30.14MB 0.7s
2025-Aug-18 12:13:28.718197
#5 sha256:3cd5add3ba3330f959ed1b27096bfb25e2f29a4cf327009d69855a903036f3af 1.27MB / 1.27MB 0.6s done
2025-Aug-18 12:13:28.718197
#5 sha256:36d983c178a06f2be8270a47a21f02ba9618771919e20e0ba847592826b79c2a 0B / 249B 0.7s
2025-Aug-18 12:13:28.718197
#5 sha256:9a6263cdeaa5d408640880103ee36920ef814974ca8e2674412ad6460e8968c9 30.14MB / 30.14MB 0.7s done
2025-Aug-18 12:13:28.718197
#5 sha256:7b5dfa58994b71320b90f4e4e2ced96f1ae3f6468038e81cf1e27a0e5d7b3c8d 12.04MB / 12.04MB 0.9s
2025-Aug-18 12:13:28.718197
#5 extracting sha256:9a6263cdeaa5d408640880103ee36920ef814974ca8e2674412ad6460e8968c9
2025-Aug-18 12:13:28.718197
#5 sha256:7b5dfa58994b71320b90f4e4e2ced96f1ae3f6468038e81cf1e27a0e5d7b3c8d 12.04MB / 12.04MB 0.9s done
2025-Aug-18 12:13:28.718197
#5 sha256:36d983c178a06f2be8270a47a21f02ba9618771919e20e0ba847592826b79c2a 249B / 249B 0.9s done
2025-Aug-18 12:13:28.718197
#5 ...
2025-Aug-18 12:13:28.718197
2025-Aug-18 12:13:28.718197
#7 FROM ghcr.io/astral-sh/uv:latest@sha256:8101ad825250a114e7bef89eefaa73c31e34e10ffbe5aff01562740bac97553c
2025-Aug-18 12:13:28.718197
#7 resolve ghcr.io/astral-sh/uv:latest@sha256:8101ad825250a114e7bef89eefaa73c31e34e10ffbe5aff01562740bac97553c 0.0s done
2025-Aug-18 12:13:28.718197
#7 sha256:8101ad825250a114e7bef89eefaa73c31e34e10ffbe5aff01562740bac97553c 2.19kB / 2.19kB done
2025-Aug-18 12:13:28.718197
#7 sha256:b06eb096b84c24d6461013a8419653c7c4d2319ac654d5eb3a4e86f543d8990b 669B / 669B done
2025-Aug-18 12:13:28.718197
#7 sha256:9d1ca2305d96406febc1337cc23300de9e212d4ae55e49898f94b11ec0aa4958 1.30kB / 1.30kB done
2025-Aug-18 12:13:28.718197
#7 sha256:7b33eb96cdab840115fb426ed199fefd757a2264dc88570221d62228c9fc8dfc 18.45MB / 18.45MB 0.2s done
2025-Aug-18 12:13:28.718197
#7 sha256:6d3d194a07a996d9c394e9be87c64752b7e89fbc3e4674b6c64e2828c959be42 98B / 98B 0.2s done
2025-Aug-18 12:13:28.718197
#7 extracting sha256:7b33eb96cdab840115fb426ed199fefd757a2264dc88570221d62228c9fc8dfc 1.2s done
2025-Aug-18 12:13:28.718197
#7 extracting sha256:6d3d194a07a996d9c394e9be87c64752b7e89fbc3e4674b6c64e2828c959be42 done
2025-Aug-18 12:13:28.718197
#7 DONE 1.6s
2025-Aug-18 12:13:28.718197
2025-Aug-18 12:13:28.718197
#5 [stage-0 1/9] FROM docker.io/library/python:3.12-slim@sha256:d67a7b66b989ad6b6d6b10d428dcc5e0bfc3e5f88906e67d490c4d3daac57047
2025-Aug-18 12:13:28.718197
#5 extracting sha256:9a6263cdeaa5d408640880103ee36920ef814974ca8e2674412ad6460e8968c9 2.2s done
2025-Aug-18 12:13:28.718197
#5 extracting sha256:3cd5add3ba3330f959ed1b27096bfb25e2f29a4cf327009d69855a903036f3af 0.1s
2025-Aug-18 12:13:28.718197
#5 extracting sha256:3cd5add3ba3330f959ed1b27096bfb25e2f29a4cf327009d69855a903036f3af 0.2s done
2025-Aug-18 12:13:28.718197
#5 extracting sha256:7b5dfa58994b71320b90f4e4e2ced96f1ae3f6468038e81cf1e27a0e5d7b3c8d 0.1s
2025-Aug-18 12:13:28.718197
#5 extracting sha256:7b5dfa58994b71320b90f4e4e2ced96f1ae3f6468038e81cf1e27a0e5d7b3c8d 0.9s done
2025-Aug-18 12:13:28.718197
#5 extracting sha256:36d983c178a06f2be8270a47a21f02ba9618771919e20e0ba847592826b79c2a done
2025-Aug-18 12:13:28.718197
#5 DONE 4.4s
2025-Aug-18 12:13:28.718197
2025-Aug-18 12:13:28.718197
#8 [stage-0 2/9] COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /usr/local/bin/
2025-Aug-18 12:13:28.718197
#8 DONE 0.2s
2025-Aug-18 12:13:28.718197
2025-Aug-18 12:13:28.718197
#9 [stage-0 3/9] RUN apt-get update && apt-get install -y     wget gnupg libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1     libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2     libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0     libwayland-server0 libxshmfence1 libegl1     libpoppler-cpp-dev libpoppler-dev poppler-utils     build-essential  && rm -rf /var/lib/apt/lists/*
2025-Aug-18 12:13:28.718197
#9 0.148 Hit:1 http://deb.debian.org/debian trixie InRelease
2025-Aug-18 12:13:28.718197
#9 0.150 Get:2 http://deb.debian.org/debian trixie-updates InRelease [47.1 kB]
2025-Aug-18 12:13:28.718197
#9 0.152 Get:3 http://deb.debian.org/debian-security trixie-security InRelease [43.4 kB]
2025-Aug-18 12:13:28.718197
#9 0.179 Get:4 http://deb.debian.org/debian trixie/main arm64 Packages [9604 kB]
2025-Aug-18 12:13:28.718197
#9 0.278 Get:5 http://deb.debian.org/debian trixie-updates/main arm64 Packages [2432 B]
2025-Aug-18 12:13:28.718197
#9 0.279 Get:6 http://deb.debian.org/debian-security trixie-security/main arm64 Packages [9464 B]
2025-Aug-18 12:13:28.718197
#9 1.266 Fetched 9706 kB in 1s (8449 kB/s)
2025-Aug-18 12:13:28.718197
#9 1.266 Reading package lists...
2025-Aug-18 12:13:28.718197
#9 2.050 Reading package lists...
2025-Aug-18 12:13:28.718197
#9 2.836 Building dependency tree...
2025-Aug-18 12:13:28.718197
#9 3.094 Reading state information...
2025-Aug-18 12:13:28.718197
#9 3.739 The following additional packages will be installed:
2025-Aug-18 12:13:28.718197
#9 3.739   alsa-topology-conf alsa-ucm-conf at-spi2-common at-spi2-core binutils
2025-Aug-18 12:13:28.718197
#9 3.739   binutils-aarch64-linux-gnu binutils-common bzip2 cpp cpp-14
2025-Aug-18 12:13:28.718197
#9 3.739   cpp-14-aarch64-linux-gnu cpp-aarch64-linux-gnu dbus dbus-bin dbus-daemon
2025-Aug-18 12:13:28.718197
#9 3.739   dbus-session-bus-common dbus-system-bus-common dbus-user-session
2025-Aug-18 12:13:28.718197
#9 3.740   dconf-gsettings-backend dconf-service dirmngr dmsetup dpkg-dev fakeroot
2025-Aug-18 12:13:28.718197
#9 3.740   fontconfig fontconfig-config fonts-dejavu-core fonts-dejavu-mono g++ g++-14
2025-Aug-18 12:13:28.718197
#9 3.740   g++-14-aarch64-linux-gnu g++-aarch64-linux-gnu gcc gcc-14
2025-Aug-18 12:13:28.718197
#9 3.740   gcc-14-aarch64-linux-gnu gcc-aarch64-linux-gnu gnupg-l10n gnupg-utils gpg
2025-Aug-18 12:13:28.718197
#9 3.740   gpg-agent gpg-wks-client gpgconf gpgsm gpgv gsettings-desktop-schemas
2025-Aug-18 12:13:28.718197
#9 3.740   krb5-locales libalgorithm-diff-perl libalgorithm-diff-xs-perl
2025-Aug-18 12:13:28.718197
#9 3.740   libalgorithm-merge-perl libapparmor1 libasan8 libasound2-data libassuan9
2025-Aug-18 12:13:28.718197
#9 3.740   libatomic1 libatspi2.0-0t64 libavahi-client3 libavahi-common-data
2025-Aug-18 12:13:28.718197
#9 3.741   libavahi-common3 libbinutils libbrotli1 libc-dev-bin libc6-dev libcairo2
2025-Aug-18 12:13:28.718197
#9 3.741   libcc1-0 libcom-err2 libcrypt-dev libcryptsetup12 libctf-nobfd0 libctf0
2025-Aug-18 12:13:28.718197
#9 3.741   libcurl3t64-gnutls libdatrie1 libdbus-1-3 libdconf1 libdeflate0
2025-Aug-18 12:13:28.718197
#9 3.741   libdevmapper1.02.1 libdpkg-perl libdrm-amdgpu1 libdrm-common libedit2
2025-Aug-18 12:13:28.718197
#9 3.741   libegl-mesa0 libelf1t64 libexpat1 libfakeroot libfile-fcntllock-perl
2025-Aug-18 12:13:28.718197
#9 3.741   libfontconfig1 libfreetype6 libfribidi0 libgcc-14-dev libgcrypt20
2025-Aug-18 12:13:28.718197
#9 3.741   libgdbm-compat4t64 libglib2.0-0t64 libglib2.0-data libglvnd0 libgnutls30t64
2025-Aug-18 12:13:28.718197
#9 3.741   libgomp1 libgpg-error-l10n libgpg-error0 libgpgme11t64 libgpgmepp6t64
2025-Aug-18 12:13:28.718197
#9 3.741   libgprofng0 libgraphite2-3 libgssapi-krb5-2 libharfbuzz0b libhwasan0
2025-Aug-18 12:13:28.718197
#9 3.741   libidn2-0 libisl23 libitm1 libjansson4 libjbig0 libjpeg62-turbo libjson-c5
2025-Aug-18 12:13:28.718197
#9 3.741   libk5crypto3 libkeyutils1 libkmod2 libkrb5-3 libkrb5support0 libksba8
2025-Aug-18 12:13:28.718197
#9 3.741   liblcms2-2 libldap-common libldap2 liblerc4 libllvm19 liblocale-gettext-perl
2025-Aug-18 12:13:28.718197
#9 3.741   liblsan0 libmpc3 libmpfr6 libnghttp2-14 libnghttp3-9 libngtcp2-16
2025-Aug-18 12:13:28.718197
#9 3.741   libngtcp2-crypto-gnutls8 libnpth0t64 libnspr4 libnss-systemd libopenjp2-7
2025-Aug-18 12:13:28.718197
#9 3.741   libp11-kit0 libpam-systemd libpangoft2-1.0-0 libperl5.40 libpixman-1-0
2025-Aug-18 12:13:28.718197
#9 3.744   libpng16-16t64 libpoppler-cpp2 libpoppler147 libproc2-0 libpsl5t64 librtmp1
2025-Aug-18 12:13:28.718197
#9 3.744   libsasl2-2 libsasl2-modules libsasl2-modules-db libsensors-config
2025-Aug-18 12:13:28.718197
#9 3.744   libsensors5 libsframe1 libsharpyuv0 libssh2-1t64 libstdc++-14-dev
2025-Aug-18 12:13:28.718197
#9 3.744   libsystemd-shared libtasn1-6 libthai-data libthai0 libtiff6 libtsan2
2025-Aug-18 12:13:28.718197
#9 3.748   libubsan1 libunistring5 libwebp7 libx11-6 libx11-data libxau6 libxcb-dri3-0
2025-Aug-18 12:13:28.718197
#9 3.748   libxcb-present0 libxcb-randr0 libxcb-render0 libxcb-shm0 libxcb-sync1
2025-Aug-18 12:13:28.718197
#9 3.748   libxcb-xfixes0 libxcb1 libxdmcp6 libxext6 libxfixes3 libxi6 libxml2
2025-Aug-18 12:13:28.718197
#9 3.748   libxrender1 libxtst6 libz3-4 linux-libc-dev linux-sysctl-defaults make
2025-Aug-18 12:13:28.718197
#9 3.748   manpages manpages-dev mesa-libgallium patch perl perl-modules-5.40
2025-Aug-18 12:13:28.718197
#9 3.748   pinentry-curses poppler-data procps psmisc publicsuffix rpcsvc-proto
2025-Aug-18 12:13:28.718197
#9 3.748   shared-mime-info systemd systemd-cryptsetup systemd-sysv systemd-timesyncd
2025-Aug-18 12:13:28.718197
#9 3.748   x11-common xdg-user-dirs xz-utils
2025-Aug-18 12:13:28.718197
#9 3.748 Suggested packages:
2025-Aug-18 12:13:28.718197
#9 3.748   binutils-doc gprofng-gui binutils-gold bzip2-doc cpp-doc gcc-14-locales
2025-Aug-18 12:13:28.718197
#9 3.748   cpp-14-doc pinentry-gnome3 tor debian-keyring debian-tag2upload-keyring
2025-Aug-18 12:13:28.718197
#9 3.748   gcc-14-doc gcc-multilib autoconf automake libtool flex bison gdb gcc-doc
2025-Aug-18 12:13:28.718197
#9 3.748   gdb-aarch64-linux-gnu gpg-wks-server parcimonie xloadimage scdaemon
2025-Aug-18 12:13:28.718197
#9 3.748   tpm2daemon alsa-utils libasound2-plugins libc-devtools glibc-doc cups-common
2025-Aug-18 12:13:28.718197
#9 3.748   sensible-utils git bzr rng-tools low-memory-monitor gnutls-bin krb5-doc
2025-Aug-18 12:13:28.718197
#9 3.748   krb5-user liblcms2-utils libtss2-rc0t64 libsasl2-modules-gssapi-mit
2025-Aug-18 12:13:28.718197
#9 3.748   | libsasl2-modules-gssapi-heimdal libsasl2-modules-ldap libsasl2-modules-otp
2025-Aug-18 12:13:28.718197
#9 3.748   libsasl2-modules-sql lm-sensors libstdc++-14-doc libarchive13t64 libbpf1
2025-Aug-18 12:13:28.718197
#9 3.748   libdw1t64 libfido2-1 libip4tc2 libpwquality1 libqrencode4 make-doc
2025-Aug-18 12:13:28.718197
#9 3.748   man-browser ed diffutils-doc perl-doc libterm-readline-gnu-perl
2025-Aug-18 12:13:28.718197
#9 3.748   | libterm-readline-perl-perl libtap-harness-archive-perl pinentry-doc
2025-Aug-18 12:13:28.718197
#9 3.748   ghostscript fonts-japanese-mincho | fonts-ipafont-mincho
2025-Aug-18 12:13:28.718197
#9 3.748   fonts-japanese-gothic | fonts-ipafont-gothic fonts-arphic-ukai
2025-Aug-18 12:13:28.718197
#9 3.748   fonts-arphic-uming fonts-nanum systemd-container systemd-homed
2025-Aug-18 12:13:28.718197
#9 3.748   systemd-userdbd systemd-boot systemd-resolved systemd-repart
2025-Aug-18 12:13:28.718197
#9 3.748   libtss2-tcti-device0 polkitd
2025-Aug-18 12:13:28.718197
#9 4.854 The following NEW packages will be installed:
2025-Aug-18 12:13:28.718197
#9 4.855   alsa-topology-conf alsa-ucm-conf at-spi2-common at-spi2-core binutils
2025-Aug-18 12:13:28.718197
#9 4.855   binutils-aarch64-linux-gnu binutils-common build-essential bzip2 cpp cpp-14
2025-Aug-18 12:13:28.718197
#9 4.855   cpp-14-aarch64-linux-gnu cpp-aarch64-linux-gnu dbus dbus-bin dbus-daemon
2025-Aug-18 12:13:28.718197
#9 4.855   dbus-session-bus-common dbus-system-bus-common dbus-user-session
2025-Aug-18 12:13:28.718197
#9 4.855   dconf-gsettings-backend dconf-service dirmngr dmsetup dpkg-dev fakeroot
2025-Aug-18 12:13:28.718197
#9 4.855   fontconfig fontconfig-config fonts-dejavu-core fonts-dejavu-mono g++ g++-14
2025-Aug-18 12:13:28.718197
#9 4.855   g++-14-aarch64-linux-gnu g++-aarch64-linux-gnu gcc gcc-14
2025-Aug-18 12:13:28.718197
#9 4.855   gcc-14-aarch64-linux-gnu gcc-aarch64-linux-gnu gnupg gnupg-l10n gnupg-utils
2025-Aug-18 12:13:28.718197
#9 4.855   gpg gpg-agent gpg-wks-client gpgconf gpgsm gpgv gsettings-desktop-schemas
2025-Aug-18 12:13:28.718197
#9 4.855   krb5-locales libalgorithm-diff-perl libalgorithm-diff-xs-perl
2025-Aug-18 12:13:28.718197
#9 4.855   libalgorithm-merge-perl libapparmor1 libasan8 libasound2-data libasound2t64
2025-Aug-18 12:13:28.718197
#9 4.855   libassuan9 libatk-bridge2.0-0t64 libatk1.0-0t64 libatomic1 libatspi2.0-0t64
2025-Aug-18 12:13:28.718197
#9 4.855   libavahi-client3 libavahi-common-data libavahi-common3 libbinutils
2025-Aug-18 12:13:28.718197
#9 4.855   libbrotli1 libc-dev-bin libc6-dev libcairo2 libcc1-0 libcom-err2
2025-Aug-18 12:13:28.718197
#9 4.855   libcrypt-dev libcryptsetup12 libctf-nobfd0 libctf0 libcups2t64
2025-Aug-18 12:13:28.718197
#9 4.855   libcurl3t64-gnutls libdatrie1 libdbus-1-3 libdconf1 libdeflate0
2025-Aug-18 12:13:28.718197
#9 4.855   libdevmapper1.02.1 libdpkg-perl libdrm-amdgpu1 libdrm-common libdrm2
2025-Aug-18 12:13:28.718197
#9 4.855   libedit2 libegl-mesa0 libegl1 libelf1t64 libexpat1 libfakeroot
2025-Aug-18 12:13:28.718197
#9 4.855   libfile-fcntllock-perl libfontconfig1 libfreetype6 libfribidi0 libgbm1
2025-Aug-18 12:13:28.718197
#9 4.855   libgcc-14-dev libgcrypt20 libgdbm-compat4t64 libglib2.0-0t64 libglib2.0-data
2025-Aug-18 12:13:28.718197
#9 4.855   libglvnd0 libgnutls30t64 libgomp1 libgpg-error-l10n libgpg-error0
2025-Aug-18 12:13:28.718197
#9 4.855   libgpgme11t64 libgpgmepp6t64 libgprofng0 libgraphite2-3 libgssapi-krb5-2
2025-Aug-18 12:13:28.718197
#9 4.855   libharfbuzz0b libhwasan0 libidn2-0 libisl23 libitm1 libjansson4 libjbig0
2025-Aug-18 12:13:28.718197
#9 4.855   libjpeg62-turbo libjson-c5 libk5crypto3 libkeyutils1 libkmod2 libkrb5-3
2025-Aug-18 12:13:28.718197
#9 4.855   libkrb5support0 libksba8 liblcms2-2 libldap-common libldap2 liblerc4
2025-Aug-18 12:13:28.718197
#9 4.857   libllvm19 liblocale-gettext-perl liblsan0 libmpc3 libmpfr6 libnghttp2-14
2025-Aug-18 12:13:28.718197
#9 4.857   libnghttp3-9 libngtcp2-16 libngtcp2-crypto-gnutls8 libnpth0t64 libnspr4
2025-Aug-18 12:13:28.718197
#9 4.857   libnss-systemd libnss3 libopenjp2-7 libp11-kit0 libpam-systemd
2025-Aug-18 12:13:28.718197
#9 4.857   libpango-1.0-0 libpangocairo-1.0-0 libpangoft2-1.0-0 libperl5.40
2025-Aug-18 12:13:28.718197
#9 4.857   libpixman-1-0 libpng16-16t64 libpoppler-cpp-dev libpoppler-cpp2
2025-Aug-18 12:13:28.718197
#9 4.857   libpoppler-dev libpoppler147 libproc2-0 libpsl5t64 librtmp1 libsasl2-2
2025-Aug-18 12:13:28.718197
#9 4.857   libsasl2-modules libsasl2-modules-db libsensors-config libsensors5
2025-Aug-18 12:13:28.718197
#9 4.857   libsframe1 libsharpyuv0 libssh2-1t64 libstdc++-14-dev libsystemd-shared
2025-Aug-18 12:13:28.718197
#9 4.857   libtasn1-6 libthai-data libthai0 libtiff6 libtsan2 libubsan1 libunistring5
2025-Aug-18 12:13:28.718197
#9 4.857   libwayland-client0 libwayland-server0 libwebp7 libx11-6 libx11-data
2025-Aug-18 12:13:28.718197
#9 4.857   libx11-xcb1 libxau6 libxcb-dri3-0 libxcb-present0 libxcb-randr0
2025-Aug-18 12:13:28.718197
#9 4.857   libxcb-render0 libxcb-shm0 libxcb-sync1 libxcb-xfixes0 libxcb1
2025-Aug-18 12:13:28.718197
#9 4.857   libxcomposite1 libxcursor1 libxdamage1 libxdmcp6 libxext6 libxfixes3 libxi6
2025-Aug-18 12:13:28.718197
#9 4.857   libxml2 libxrandr2 libxrender1 libxshmfence1 libxtst6 libz3-4 linux-libc-dev
2025-Aug-18 12:13:28.718197
#9 4.857   linux-sysctl-defaults make manpages manpages-dev mesa-libgallium patch perl
2025-Aug-18 12:13:28.718197
#9 4.857   perl-modules-5.40 pinentry-curses poppler-data poppler-utils procps psmisc
2025-Aug-18 12:13:28.718197
#9 4.857   publicsuffix rpcsvc-proto shared-mime-info systemd systemd-cryptsetup
2025-Aug-18 12:13:28.718197
#9 4.858   systemd-sysv systemd-timesyncd wget x11-common xdg-user-dirs xz-utils
2025-Aug-18 12:13:28.718197
#9 4.950 0 upgraded, 229 newly installed, 0 to remove and 0 not upgraded.
2025-Aug-18 12:13:28.718197
#9 4.950 Need to get 156 MB of archives.
2025-Aug-18 12:13:28.718197
#9 4.950 After this operation, 675 MB of additional disk space will be used.
2025-Aug-18 12:13:28.718197
#9 4.950 Get:1 http://deb.debian.org/debian trixie/main arm64 libsystemd-shared arm64 257.7-1 [1916 kB]
2025-Aug-18 12:13:28.718197
#9 4.986 Get:2 http://deb.debian.org/debian trixie/main arm64 libapparmor1 arm64 4.1.0-1 [42.9 kB]
2025-Aug-18 12:13:28.718197
#9 4.990 Get:3 http://deb.debian.org/debian trixie/main arm64 systemd arm64 257.7-1 [2930 kB]
2025-Aug-18 12:13:28.718197
#9 5.013 Get:4 http://deb.debian.org/debian trixie/main arm64 systemd-sysv arm64 257.7-1 [64.2 kB]
2025-Aug-18 12:13:28.718197
#9 5.013 Get:5 http://deb.debian.org/debian trixie/main arm64 libdbus-1-3 arm64 1.16.2-2 [169 kB]
2025-Aug-18 12:13:28.718197
#9 5.016 Get:6 http://deb.debian.org/debian trixie/main arm64 dbus-bin arm64 1.16.2-2 [78.8 kB]
2025-Aug-18 12:13:28.718197
#9 5.016 Get:7 http://deb.debian.org/debian trixie/main arm64 dbus-session-bus-common all 1.16.2-2 [52.3 kB]
2025-Aug-18 12:13:28.718197
#9 5.018 Get:8 http://deb.debian.org/debian trixie/main arm64 libexpat1 arm64 2.7.1-2 [93.3 kB]
2025-Aug-18 12:13:28.718197
#9 5.019 Get:9 http://deb.debian.org/debian trixie/main arm64 dbus-daemon arm64 1.16.2-2 [152 kB]
2025-Aug-18 12:13:28.718197
#9 5.020 Get:10 http://deb.debian.org/debian trixie/main arm64 dbus-system-bus-common all 1.16.2-2 [53.5 kB]
2025-Aug-18 12:13:28.718197
#9 5.021 Get:11 http://deb.debian.org/debian trixie/main arm64 dbus arm64 1.16.2-2 [70.7 kB]
2025-Aug-18 12:13:28.718197
#9 5.023 Get:12 http://deb.debian.org/debian trixie/main arm64 liblocale-gettext-perl arm64 1.07-7+b1 [15.2 kB]
2025-Aug-18 12:13:28.718197
#9 5.024 Get:13 http://deb.debian.org/debian trixie/main arm64 poppler-data all 0.4.12-1 [1601 kB]
2025-Aug-18 12:13:28.718197
#9 5.038 Get:14 http://deb.debian.org/debian trixie/main arm64 linux-sysctl-defaults all 4.12 [5624 B]
2025-Aug-18 12:13:28.718197
#9 5.038 Get:15 http://deb.debian.org/debian trixie/main arm64 libproc2-0 arm64 2:4.0.4-9 [62.8 kB]
2025-Aug-18 12:13:28.718197
#9 5.039 Get:16 http://deb.debian.org/debian trixie/main arm64 procps arm64 2:4.0.4-9 [871 kB]
2025-Aug-18 12:13:28.718197
#9 5.043 Get:17 http://deb.debian.org/debian trixie/main arm64 bzip2 arm64 1.0.8-6 [39.5 kB]
2025-Aug-18 12:13:28.718197
#9 5.044 Get:18 http://deb.debian.org/debian trixie/main arm64 krb5-locales all 1.21.3-5 [101 kB]
2025-Aug-18 12:13:28.718197
#9 5.045 Get:19 http://deb.debian.org/debian trixie/main arm64 libnss-systemd arm64 257.7-1 [202 kB]
2025-Aug-18 12:13:28.718197
#9 5.047 Get:20 http://deb.debian.org/debian trixie/main arm64 libpam-systemd arm64 257.7-1 [275 kB]
2025-Aug-18 12:13:28.718197
#9 5.051 Get:21 http://deb.debian.org/debian trixie/main arm64 manpages all 6.9.1-1 [1393 kB]
2025-Aug-18 12:13:28.718197
#9 5.062 Get:22 http://deb.debian.org/debian trixie/main arm64 perl-modules-5.40 all 5.40.1-6 [3019 kB]
2025-Aug-18 12:13:28.718197
#9 5.076 Get:23 http://deb.debian.org/debian trixie/main arm64 libgdbm-compat4t64 arm64 1.24-2 [50.3 kB]
2025-Aug-18 12:13:28.718197
#9 5.076 Get:24 http://deb.debian.org/debian trixie/main arm64 libperl5.40 arm64 5.40.1-6 [4142 kB]
2025-Aug-18 12:13:28.718197
#9 5.106 Get:25 http://deb.debian.org/debian trixie/main arm64 perl arm64 5.40.1-6 [267 kB]
2025-Aug-18 12:13:28.718197
#9 5.107 Get:26 http://deb.debian.org/debian trixie/main arm64 systemd-timesyncd arm64 257.7-1 [90.8 kB]
2025-Aug-18 12:13:28.718197
#9 5.107 Get:27 http://deb.debian.org/debian trixie/main arm64 libunistring5 arm64 1.3-2 [453 kB]
2025-Aug-18 12:13:28.718197
#9 5.113 Get:28 http://deb.debian.org/debian trixie/main arm64 libidn2-0 arm64 2.3.8-2 [107 kB]
2025-Aug-18 12:13:28.718197
#9 5.113 Get:29 http://deb.debian.org/debian trixie/main arm64 libp11-kit0 arm64 0.25.5-3 [409 kB]
2025-Aug-18 12:13:28.718197
#9 5.116 Get:30 http://deb.debian.org/debian trixie/main arm64 libtasn1-6 arm64 4.20.0-2 [47.3 kB]
2025-Aug-18 12:13:28.718197
#9 5.116 Get:31 http://deb.debian.org/debian trixie/main arm64 libgnutls30t64 arm64 3.8.9-3 [1375 kB]
2025-Aug-18 12:13:28.718197
#9 5.124 Get:32 http://deb.debian.org/debian trixie/main arm64 libpsl5t64 arm64 0.21.2-1.1+b1 [57.1 kB]
2025-Aug-18 12:13:28.718197
#9 5.124 Get:33 http://deb.debian.org/debian trixie/main arm64 wget arm64 1.25.0-2 [970 kB]
2025-Aug-18 12:13:28.718197
#9 5.129 Get:34 http://deb.debian.org/debian trixie/main arm64 xz-utils arm64 5.8.1-1 [657 kB]
2025-Aug-18 12:13:28.718197
#9 5.133 Get:35 http://deb.debian.org/debian trixie/main arm64 alsa-topology-conf all 1.2.5.1-3 [15.3 kB]
2025-Aug-18 12:13:28.718197
#9 5.133 Get:36 http://deb.debian.org/debian trixie/main arm64 libasound2-data all 1.2.14-1 [21.1 kB]
2025-Aug-18 12:13:28.718197
#9 5.135 Get:37 http://deb.debian.org/debian trixie/main arm64 libasound2t64 arm64 1.2.14-1 [342 kB]
2025-Aug-18 12:13:28.718197
#9 5.137 Get:38 http://deb.debian.org/debian trixie/main arm64 alsa-ucm-conf all 1.2.14-1 [92.5 kB]
2025-Aug-18 12:13:28.718197
#9 5.139 Get:39 http://deb.debian.org/debian trixie/main arm64 at-spi2-common all 2.56.2-1 [171 kB]
2025-Aug-18 12:13:28.718197
#9 5.140 Get:40 http://deb.debian.org/debian trixie/main arm64 libatomic1 arm64 14.2.0-19 [10.1 kB]
2025-Aug-18 12:13:28.718197
#9 5.141 Get:41 http://deb.debian.org/debian trixie/main arm64 libglib2.0-0t64 arm64 2.84.3-1 [1426 kB]
2025-Aug-18 12:13:28.718197
#9 5.147 Get:42 http://deb.debian.org/debian trixie/main arm64 libxau6 arm64 1:1.0.11-1 [20.6 kB]
2025-Aug-18 12:13:28.718197
#9 5.147 Get:43 http://deb.debian.org/debian trixie/main arm64 libxdmcp6 arm64 1:1.1.5-1 [27.8 kB]
2025-Aug-18 12:13:28.718197
#9 5.147 Get:44 http://deb.debian.org/debian trixie/main arm64 libxcb1 arm64 1.17.0-2+b1 [143 kB]
2025-Aug-18 12:13:28.718197
#9 5.149 Get:45 http://deb.debian.org/debian trixie/main arm64 libx11-data all 2:1.8.12-1 [343 kB]
2025-Aug-18 12:13:28.718197
#9 5.152 Get:46 http://deb.debian.org/debian trixie/main arm64 libx11-6 arm64 2:1.8.12-1 [795 kB]
2025-Aug-18 12:13:28.718197
#9 5.156 Get:47 http://deb.debian.org/debian trixie/main arm64 libxext6 arm64 2:1.3.4-1+b3 [49.2 kB]
2025-Aug-18 12:13:28.718197
#9 5.156 Get:48 http://deb.debian.org/debian trixie/main arm64 libxi6 arm64 2:1.8.2-1 [77.8 kB]
2025-Aug-18 12:13:28.718197
#9 5.159 Get:49 http://deb.debian.org/debian trixie/main arm64 libatspi2.0-0t64 arm64 2.56.2-1 [76.5 kB]
2025-Aug-18 12:13:28.718197
#9 5.160 Get:50 http://deb.debian.org/debian trixie/main arm64 x11-common all 1:7.7+24 [217 kB]
2025-Aug-18 12:13:28.718197
#9 5.165 Get:51 http://deb.debian.org/debian trixie/main arm64 libxtst6 arm64 2:1.2.5-1 [25.7 kB]
2025-Aug-18 12:13:28.718197
#9 5.170 Get:52 http://deb.debian.org/debian trixie/main arm64 dbus-user-session arm64 1.16.2-2 [52.1 kB]
2025-Aug-18 12:13:28.718197
#9 5.172 Get:53 http://deb.debian.org/debian trixie/main arm64 libdconf1 arm64 0.40.0-5 [40.4 kB]
2025-Aug-18 12:13:28.718197
#9 5.174 Get:54 http://deb.debian.org/debian trixie/main arm64 dconf-service arm64 0.40.0-5 [30.9 kB]
2025-Aug-18 12:13:28.718197
#9 5.177 Get:55 http://deb.debian.org/debian trixie/main arm64 dconf-gsettings-backend arm64 0.40.0-5 [27.3 kB]
2025-Aug-18 12:13:28.718197
#9 5.184 Get:56 http://deb.debian.org/debian trixie/main arm64 gsettings-desktop-schemas all 48.0-1 [700 kB]
2025-Aug-18 12:13:28.718197
#9 5.190 Get:57 http://deb.debian.org/debian trixie/main arm64 at-spi2-core arm64 2.56.2-1 [59.7 kB]
2025-Aug-18 12:13:28.718197
#9 5.190 Get:58 http://deb.debian.org/debian trixie/main arm64 libsframe1 arm64 2.44-3 [77.8 kB]
2025-Aug-18 12:13:28.718197
#9 5.193 Get:59 http://deb.debian.org/debian trixie/main arm64 binutils-common arm64 2.44-3 [2509 kB]
2025-Aug-18 12:13:28.718197
#9 5.204 Get:60 http://deb.debian.org/debian trixie/main arm64 libbinutils arm64 2.44-3 [660 kB]
2025-Aug-18 12:13:28.718197
#9 5.209 Get:61 http://deb.debian.org/debian trixie/main arm64 libgprofng0 arm64 2.44-3 [668 kB]
2025-Aug-18 12:13:28.718197
#9 5.216 Get:62 http://deb.debian.org/debian trixie/main arm64 libctf-nobfd0 arm64 2.44-3 [152 kB]
2025-Aug-18 12:13:28.718197
#9 5.218 Get:63 http://deb.debian.org/debian trixie/main arm64 libctf0 arm64 2.44-3 [84.2 kB]
2025-Aug-18 12:13:28.718197
#9 5.221 Get:64 http://deb.debian.org/debian trixie/main arm64 libjansson4 arm64 2.14-2+b3 [39.2 kB]
2025-Aug-18 12:13:28.718197
#9 5.221 Get:65 http://deb.debian.org/debian trixie/main arm64 binutils-aarch64-linux-gnu arm64 2.44-3 [820 kB]
2025-Aug-18 12:13:28.718197
#9 5.226 Get:66 http://deb.debian.org/debian trixie/main arm64 binutils arm64 2.44-3 [262 kB]
2025-Aug-18 12:13:28.718197
#9 5.228 Get:67 http://deb.debian.org/debian trixie/main arm64 libc-dev-bin arm64 2.41-12 [57.4 kB]
2025-Aug-18 12:13:28.718197
#9 5.229 Get:68 http://deb.debian.org/debian-security trixie-security/main arm64 linux-libc-dev all 6.12.41-1 [2637 kB]
2025-Aug-18 12:13:28.718197
#9 5.244 Get:69 http://deb.debian.org/debian trixie/main arm64 libcrypt-dev arm64 1:4.4.38-1 [123 kB]
2025-Aug-18 12:13:28.718197
#9 5.245 Get:70 http://deb.debian.org/debian trixie/main arm64 rpcsvc-proto arm64 1.4.3-1+b1 [60.5 kB]
2025-Aug-18 12:13:28.718197
#9 5.247 Get:71 http://deb.debian.org/debian trixie/main arm64 libc6-dev arm64 2.41-12 [1621 kB]
2025-Aug-18 12:13:28.718197
#9 5.255 Get:72 http://deb.debian.org/debian trixie/main arm64 libisl23 arm64 0.27-1 [601 kB]
2025-Aug-18 12:13:28.718197
#9 5.260 Get:73 http://deb.debian.org/debian trixie/main arm64 libmpfr6 arm64 4.2.2-1 [685 kB]
2025-Aug-18 12:13:28.718197
#9 5.263 Get:74 http://deb.debian.org/debian trixie/main arm64 libmpc3 arm64 1.3.1-1+b3 [50.5 kB]
2025-Aug-18 12:13:28.718197
#9 5.263 Get:75 http://deb.debian.org/debian trixie/main arm64 cpp-14-aarch64-linux-gnu arm64 14.2.0-19 [9169 kB]
2025-Aug-18 12:13:28.718197
#9 5.301 Get:76 http://deb.debian.org/debian trixie/main arm64 cpp-14 arm64 14.2.0-19 [1276 B]
2025-Aug-18 12:13:28.718197
#9 5.302 Get:77 http://deb.debian.org/debian trixie/main arm64 cpp-aarch64-linux-gnu arm64 4:14.2.0-1 [4832 B]
2025-Aug-18 12:13:28.718197
#9 5.303 Get:78 http://deb.debian.org/debian trixie/main arm64 cpp arm64 4:14.2.0-1 [1568 B]
2025-Aug-18 12:13:28.718197
#9 5.304 Get:79 http://deb.debian.org/debian trixie/main arm64 libcc1-0 arm64 14.2.0-19 [42.2 kB]
2025-Aug-18 12:13:28.718197
#9 5.305 Get:80 http://deb.debian.org/debian trixie/main arm64 libgomp1 arm64 14.2.0-19 [124 kB]
2025-Aug-18 12:13:28.718197
#9 5.306 Get:81 http://deb.debian.org/debian trixie/main arm64 libitm1 arm64 14.2.0-19 [24.2 kB]
2025-Aug-18 12:13:28.718197
#9 5.307 Get:82 http://deb.debian.org/debian trixie/main arm64 libasan8 arm64 14.2.0-19 [2578 kB]
2025-Aug-18 12:13:28.718197
#9 5.320 Get:83 http://deb.debian.org/debian trixie/main arm64 liblsan0 arm64 14.2.0-19 [1161 kB]
2025-Aug-18 12:13:28.718197
#9 5.327 Get:84 http://deb.debian.org/debian trixie/main arm64 libtsan2 arm64 14.2.0-19 [2383 kB]
2025-Aug-18 12:13:28.718197
#9 5.336 Get:85 http://deb.debian.org/debian trixie/main arm64 libubsan1 arm64 14.2.0-19 [1039 kB]
2025-Aug-18 12:13:28.718197
#9 5.341 Get:86 http://deb.debian.org/debian trixie/main arm64 libhwasan0 arm64 14.2.0-19 [1442 kB]
2025-Aug-18 12:13:28.718197
#9 5.349 Get:87 http://deb.debian.org/debian trixie/main arm64 libgcc-14-dev arm64 14.2.0-19 [2359 kB]
2025-Aug-18 12:13:28.718197
#9 5.359 Get:88 http://deb.debian.org/debian trixie/main arm64 gcc-14-aarch64-linux-gnu arm64 14.2.0-19 [17.7 MB]
2025-Aug-18 12:13:28.718197
#9 5.435 Get:89 http://deb.debian.org/debian trixie/main arm64 gcc-14 arm64 14.2.0-19 [529 kB]
2025-Aug-18 12:13:28.718197
#9 5.441 Get:90 http://deb.debian.org/debian trixie/main arm64 gcc-aarch64-linux-gnu arm64 4:14.2.0-1 [1440 B]
2025-Aug-18 12:13:28.718197
#9 5.442 Get:91 http://deb.debian.org/debian trixie/main arm64 gcc arm64 4:14.2.0-1 [5136 B]
2025-Aug-18 12:13:28.718197
#9 5.446 Get:92 http://deb.debian.org/debian trixie/main arm64 libstdc++-14-dev arm64 14.2.0-19 [2295 kB]
2025-Aug-18 12:13:28.718197
#9 5.451 Get:93 http://deb.debian.org/debian trixie/main arm64 g++-14-aarch64-linux-gnu arm64 14.2.0-19 [10.1 MB]
2025-Aug-18 12:13:28.718197
#9 5.498 Get:94 http://deb.debian.org/debian trixie/main arm64 g++-14 arm64 14.2.0-19 [22.5 kB]
2025-Aug-18 12:13:28.718197
#9 5.498 Get:95 http://deb.debian.org/debian trixie/main arm64 g++-aarch64-linux-gnu arm64 4:14.2.0-1 [1200 B]
2025-Aug-18 12:13:28.718197
#9 5.498 Get:96 http://deb.debian.org/debian trixie/main arm64 g++ arm64 4:14.2.0-1 [1332 B]
2025-Aug-18 12:13:28.718197
#9 5.498 Get:97 http://deb.debian.org/debian trixie/main arm64 make arm64 4.4.1-2 [452 kB]
2025-Aug-18 12:13:28.718197
#9 5.498 Get:98 http://deb.debian.org/debian trixie/main arm64 libdpkg-perl all 1.22.21 [650 kB]
2025-Aug-18 12:13:28.718197
#9 5.500 Get:99 http://deb.debian.org/debian trixie/main arm64 patch arm64 2.8-2 [128 kB]
2025-Aug-18 12:13:28.718197
#9 5.520 Get:100 http://deb.debian.org/debian trixie/main arm64 dpkg-dev all 1.22.21 [1338 kB]
2025-Aug-18 12:13:28.718197
#9 5.520 Get:101 http://deb.debian.org/debian trixie/main arm64 build-essential arm64 12.12 [4624 B]
2025-Aug-18 12:13:28.718197
#9 5.520 Get:102 http://deb.debian.org/debian trixie/main arm64 libgpg-error0 arm64 1.51-4 [78.5 kB]
2025-Aug-18 12:13:28.718197
#9 5.520 Get:103 http://deb.debian.org/debian trixie/main arm64 libassuan9 arm64 3.0.2-2 [59.1 kB]
2025-Aug-18 12:13:28.718197
#9 5.520 Get:104 http://deb.debian.org/debian trixie/main arm64 libgcrypt20 arm64 1.11.0-7 [742 kB]
2025-Aug-18 12:13:28.718197
#9 5.520 Get:105 http://deb.debian.org/debian trixie/main arm64 gpgconf arm64 2.4.7-21+b3 [121 kB]
2025-Aug-18 12:13:28.718197
#9 5.520 Get:106 http://deb.debian.org/debian trixie/main arm64 libksba8 arm64 1.6.7-2+b1 [125 kB]
2025-Aug-18 12:13:28.718197
#9 5.520 Get:107 http://deb.debian.org/debian trixie/main arm64 libsasl2-modules-db arm64 2.1.28+dfsg1-9 [20.1 kB]
2025-Aug-18 12:13:28.718197
#9 5.520 Get:108 http://deb.debian.org/debian trixie/main arm64 libsasl2-2 arm64 2.1.28+dfsg1-9 [55.6 kB]
2025-Aug-18 12:13:28.718197
#9 5.520 Get:109 http://deb.debian.org/debian trixie/main arm64 libldap2 arm64 2.6.10+dfsg-1 [179 kB]
2025-Aug-18 12:13:28.718197
#9 5.520 Get:110 http://deb.debian.org/debian trixie/main arm64 libnpth0t64 arm64 1.8-3 [22.9 kB]
2025-Aug-18 12:13:28.718197
#9 5.520 Get:111 http://deb.debian.org/debian trixie/main arm64 dirmngr arm64 2.4.7-21+b3 [359 kB]
2025-Aug-18 12:13:28.718197
#9 5.520 Get:112 http://deb.debian.org/debian trixie/main arm64 libdevmapper1.02.1 arm64 2:1.02.205-2 [118 kB]
2025-Aug-18 12:13:28.718197
#9 5.530 Get:113 http://deb.debian.org/debian trixie/main arm64 dmsetup arm64 2:1.02.205-2 [76.1 kB]
2025-Aug-18 12:13:28.718197
#9 5.530 Get:114 http://deb.debian.org/debian trixie/main arm64 libfakeroot arm64 ********-1 [29.6 kB]
2025-Aug-18 12:13:28.718197
#9 5.532 Get:115 http://deb.debian.org/debian trixie/main arm64 fakeroot arm64 ********-1 [75.4 kB]
2025-Aug-18 12:13:28.718197
#9 5.535 Get:116 http://deb.debian.org/debian trixie/main arm64 libbrotli1 arm64 1.1.0-2+b7 [308 kB]
2025-Aug-18 12:13:28.718197
#9 5.545 Get:117 http://deb.debian.org/debian trixie/main arm64 libpng16-16t64 arm64 1.6.48-1 [274 kB]
2025-Aug-18 12:13:28.718197
#9 5.550 Get:118 http://deb.debian.org/debian trixie/main arm64 libfreetype6 arm64 2.13.3+dfsg-1 [422 kB]
2025-Aug-18 12:13:28.718197
#9 5.558 Get:119 http://deb.debian.org/debian trixie/main arm64 fonts-dejavu-mono all 2.37-8 [489 kB]
2025-Aug-18 12:13:28.718197
#9 5.564 Get:120 http://deb.debian.org/debian trixie/main arm64 fonts-dejavu-core all 2.37-8 [840 kB]
2025-Aug-18 12:13:28.718197
#9 5.574 Get:121 http://deb.debian.org/debian trixie/main arm64 fontconfig-config arm64 2.15.0-2.3 [318 kB]
2025-Aug-18 12:13:28.718197
#9 5.578 Get:122 http://deb.debian.org/debian trixie/main arm64 libfontconfig1 arm64 2.15.0-2.3 [387 kB]
2025-Aug-18 12:13:28.718197
#9 5.580 Get:123 http://deb.debian.org/debian trixie/main arm64 fontconfig arm64 2.15.0-2.3 [463 kB]
2025-Aug-18 12:13:28.718197
#9 5.586 Get:124 http://deb.debian.org/debian trixie/main arm64 gnupg-l10n all 2.4.7-21 [747 kB]
2025-Aug-18 12:13:28.718197
#9 5.590 Get:125 http://deb.debian.org/debian trixie/main arm64 gpg arm64 2.4.7-21+b3 [578 kB]
2025-Aug-18 12:13:28.718197
#9 5.598 Get:126 http://deb.debian.org/debian trixie/main arm64 pinentry-curses arm64 1.3.1-2 [83.5 kB]
2025-Aug-18 12:13:28.718197
#9 5.598 Get:127 http://deb.debian.org/debian trixie/main arm64 gpg-agent arm64 2.4.7-21+b3 [249 kB]
2025-Aug-18 12:13:28.718197
#9 5.598 Get:128 http://deb.debian.org/debian trixie/main arm64 gpgsm arm64 2.4.7-21+b3 [251 kB]
2025-Aug-18 12:13:28.718197
#9 5.599 Get:129 http://deb.debian.org/debian trixie/main arm64 gnupg all 2.4.7-21 [417 kB]
2025-Aug-18 12:13:28.718197
#9 5.603 Get:130 http://deb.debian.org/debian trixie/main arm64 gpg-wks-client arm64 2.4.7-21+b3 [101 kB]
2025-Aug-18 12:13:28.718197
#9 5.605 Get:131 http://deb.debian.org/debian trixie/main arm64 gpgv arm64 2.4.7-21+b3 [220 kB]
2025-Aug-18 12:13:28.718197
#9 5.609 Get:132 http://deb.debian.org/debian trixie/main arm64 libalgorithm-diff-perl all 1.201-1 [43.3 kB]
2025-Aug-18 12:13:28.718197
#9 5.612 Get:133 http://deb.debian.org/debian trixie/main arm64 libalgorithm-diff-xs-perl arm64 0.04-9 [10.9 kB]
2025-Aug-18 12:13:28.718197
#9 5.612 Get:134 http://deb.debian.org/debian trixie/main arm64 libalgorithm-merge-perl all 0.08-5 [11.8 kB]
2025-Aug-18 12:13:28.718197
#9 5.612 Get:135 http://deb.debian.org/debian trixie/main arm64 libatk1.0-0t64 arm64 2.56.2-1 [50.1 kB]
2025-Aug-18 12:13:28.718197
#9 5.619 Get:136 http://deb.debian.org/debian trixie/main arm64 libatk-bridge2.0-0t64 arm64 2.56.2-1 [64.8 kB]
2025-Aug-18 12:13:28.718197
#9 5.619 Get:137 http://deb.debian.org/debian trixie/main arm64 libavahi-common-data arm64 0.8-16 [112 kB]
2025-Aug-18 12:13:28.718197
#9 5.619 Get:138 http://deb.debian.org/debian trixie/main arm64 libavahi-common3 arm64 0.8-16 [43.3 kB]
2025-Aug-18 12:13:28.718197
#9 5.619 Get:139 http://deb.debian.org/debian trixie/main arm64 libavahi-client3 arm64 0.8-16 [46.7 kB]
2025-Aug-18 12:13:28.718197
#9 5.622 Get:140 http://deb.debian.org/debian trixie/main arm64 libpixman-1-0 arm64 0.44.0-3 [168 kB]
2025-Aug-18 12:13:28.718197
#9 5.629 Get:141 http://deb.debian.org/debian trixie/main arm64 libxcb-render0 arm64 1.17.0-2+b1 [115 kB]
2025-Aug-18 12:13:28.718197
#9 5.633 Get:142 http://deb.debian.org/debian trixie/main arm64 libxcb-shm0 arm64 1.17.0-2+b1 [105 kB]
2025-Aug-18 12:13:28.718197
#9 5.637 Get:143 http://deb.debian.org/debian trixie/main arm64 libxrender1 arm64 1:0.9.12-1 [27.0 kB]
2025-Aug-18 12:13:28.718197
#9 5.638 Get:144 http://deb.debian.org/debian trixie/main arm64 libcairo2 arm64 1.18.4-1+b1 [483 kB]
2025-Aug-18 12:13:28.718197
#9 5.642 Get:145 http://deb.debian.org/debian trixie/main arm64 libcom-err2 arm64 1.47.2-3+b3 [24.9 kB]
2025-Aug-18 12:13:28.718197
#9 5.643 Get:146 http://deb.debian.org/debian trixie/main arm64 libjson-c5 arm64 0.18+ds-1 [45.2 kB]
2025-Aug-18 12:13:28.718197
#9 5.645 Get:147 http://deb.debian.org/debian trixie/main arm64 libcryptsetup12 arm64 2:2.7.5-2 [232 kB]
2025-Aug-18 12:13:28.718197
#9 5.652 Get:148 http://deb.debian.org/debian trixie/main arm64 libkrb5support0 arm64 1.21.3-5 [32.4 kB]
2025-Aug-18 12:13:28.718197
#9 5.659 Get:149 http://deb.debian.org/debian trixie/main arm64 libk5crypto3 arm64 1.21.3-5 [81.2 kB]
2025-Aug-18 12:13:28.718197
#9 5.659 Get:150 http://deb.debian.org/debian trixie/main arm64 libkeyutils1 arm64 1.6.3-6 [9716 B]
2025-Aug-18 12:13:28.718197
#9 5.659 Get:151 http://deb.debian.org/debian trixie/main arm64 libkrb5-3 arm64 1.21.3-5 [308 kB]
2025-Aug-18 12:13:28.718197
#9 5.660 Get:152 http://deb.debian.org/debian trixie/main arm64 libgssapi-krb5-2 arm64 1.21.3-5 [127 kB]
2025-Aug-18 12:13:28.718197
#9 5.666 Get:153 http://deb.debian.org/debian trixie/main arm64 libcups2t64 arm64 2.4.10-3 [235 kB]
2025-Aug-18 12:13:28.718197
#9 5.668 Get:154 http://deb.debian.org/debian trixie/main arm64 libnghttp2-14 arm64 1.64.0-1.1 [71.4 kB]
2025-Aug-18 12:13:28.718197
#9 5.673 Get:155 http://deb.debian.org/debian trixie/main arm64 libnghttp3-9 arm64 1.8.0-1 [63.2 kB]
2025-Aug-18 12:13:28.718197
#9 5.673 Get:156 http://deb.debian.org/debian trixie/main arm64 libngtcp2-16 arm64 1.11.0-1 [121 kB]
2025-Aug-18 12:13:28.718197
#9 5.673 Get:157 http://deb.debian.org/debian trixie/main arm64 libngtcp2-crypto-gnutls8 arm64 1.11.0-1 [28.2 kB]
2025-Aug-18 12:13:28.718197
#9 5.673 Get:158 http://deb.debian.org/debian trixie/main arm64 librtmp1 arm64 2.4+20151223.gitfa8646d.1-2+b5 [56.8 kB]
2025-Aug-18 12:13:28.718197
#9 5.673 Get:159 http://deb.debian.org/debian trixie/main arm64 libssh2-1t64 arm64 1.11.1-1 [235 kB]
2025-Aug-18 12:13:28.718197
#9 5.673 Get:160 http://deb.debian.org/debian trixie/main arm64 libcurl3t64-gnutls arm64 8.14.1-2 [353 kB]
2025-Aug-18 12:13:28.718197
#9 5.682 Get:161 http://deb.debian.org/debian trixie/main arm64 libdatrie1 arm64 0.2.13-3+b1 [37.6 kB]
2025-Aug-18 12:13:28.718197
#9 5.687 Get:162 http://deb.debian.org/debian trixie/main arm64 libdeflate0 arm64 1.23-2 [42.4 kB]
2025-Aug-18 12:13:28.718197
#9 5.688 Get:163 http://deb.debian.org/debian trixie/main arm64 libdrm-common all 2.4.124-2 [8288 B]
2025-Aug-18 12:13:28.718197
#9 5.688 Get:164 http://deb.debian.org/debian trixie/main arm64 libdrm2 arm64 2.4.124-2 [38.3 kB]
2025-Aug-18 12:13:28.718197
#9 5.690 Get:165 http://deb.debian.org/debian trixie/main arm64 libdrm-amdgpu1 arm64 2.4.124-2 [21.8 kB]
2025-Aug-18 12:13:28.718197
#9 5.691 Get:166 http://deb.debian.org/debian trixie/main arm64 libedit2 arm64 3.1-20250104-1 [89.3 kB]
2025-Aug-18 12:13:28.718197
#9 5.692 Get:167 http://deb.debian.org/debian trixie/main arm64 libwayland-server0 arm64 1.23.1-3 [33.7 kB]
2025-Aug-18 12:13:28.718197
#9 5.698 Get:168 http://deb.debian.org/debian trixie/main arm64 libelf1t64 arm64 0.192-4 [189 kB]
2025-Aug-18 12:13:28.718197
#9 5.702 Get:169 http://deb.debian.org/debian trixie/main arm64 libxml2 arm64 2.12.7+dfsg+really2.9.14-2.1 [630 kB]
2025-Aug-18 12:13:28.718197
#9 5.705 Get:170 http://deb.debian.org/debian trixie/main arm64 libz3-4 arm64 4.13.3-1 [7507 kB]
2025-Aug-18 12:13:28.718197
#9 5.742 Get:171 http://deb.debian.org/debian trixie/main arm64 libllvm19 arm64 1:19.1.7-3+b1 [23.3 MB]
2025-Aug-18 12:13:28.718197
#9 5.870 Get:172 http://deb.debian.org/debian trixie/main arm64 libsensors-config all 1:3.6.2-2 [16.2 kB]
2025-Aug-18 12:13:28.718197
#9 5.871 Get:173 http://deb.debian.org/debian trixie/main arm64 libsensors5 arm64 1:3.6.2-2 [36.4 kB]
2025-Aug-18 12:13:28.718197
#9 5.871 Get:174 http://deb.debian.org/debian trixie/main arm64 libx11-xcb1 arm64 2:1.8.12-1 [247 kB]
2025-Aug-18 12:13:28.718197
#9 5.874 Get:175 http://deb.debian.org/debian trixie/main arm64 libxcb-dri3-0 arm64 1.17.0-2+b1 [107 kB]
2025-Aug-18 12:13:28.718197
#9 5.878 Get:176 http://deb.debian.org/debian trixie/main arm64 libxcb-present0 arm64 1.17.0-2+b1 [106 kB]
2025-Aug-18 12:13:28.718197
#9 5.881 Get:177 http://deb.debian.org/debian trixie/main arm64 libxcb-randr0 arm64 1.17.0-2+b1 [117 kB]
2025-Aug-18 12:13:28.718197
#9 5.881 Get:178 http://deb.debian.org/debian trixie/main arm64 libxcb-sync1 arm64 1.17.0-2+b1 [109 kB]
2025-Aug-18 12:13:28.718197
#9 5.881 Get:179 http://deb.debian.org/debian trixie/main arm64 libxcb-xfixes0 arm64 1.17.0-2+b1 [110 kB]
2025-Aug-18 12:13:28.718197
#9 5.882 Get:180 http://deb.debian.org/debian trixie/main arm64 libxshmfence1 arm64 1.3.3-1 [11.1 kB]
2025-Aug-18 12:13:28.718197
#9 5.884 Get:181 http://deb.debian.org/debian trixie/main arm64 mesa-libgallium arm64 25.0.7-2 [8032 kB]
2025-Aug-18 12:13:28.718197
#9 5.932 Get:182 http://deb.debian.org/debian trixie/main arm64 libgbm1 arm64 25.0.7-2 [43.9 kB]
2025-Aug-18 12:13:28.718197
#9 5.933 Get:183 http://deb.debian.org/debian trixie/main arm64 libwayland-client0 arm64 1.23.1-3 [26.1 kB]
2025-Aug-18 12:13:28.718197
#9 5.933 Get:184 http://deb.debian.org/debian trixie/main arm64 libegl-mesa0 arm64 25.0.7-2 [121 kB]
2025-Aug-18 12:13:28.718197
#9 5.935 Get:185 http://deb.debian.org/debian trixie/main arm64 libfile-fcntllock-perl arm64 0.22-4+b4 [34.6 kB]
2025-Aug-18 12:13:28.718197
#9 5.937 Get:186 http://deb.debian.org/debian trixie/main arm64 libfribidi0 arm64 1.0.16-1 [26.5 kB]
2025-Aug-18 12:13:28.718197
#9 5.938 Get:187 http://deb.debian.org/debian trixie/main arm64 libglib2.0-data all 2.84.3-1 [1285 kB]
2025-Aug-18 12:13:28.718197
#9 5.948 Get:188 http://deb.debian.org/debian trixie/main arm64 libglvnd0 arm64 1.7.0-1+b2 [41.6 kB]
2025-Aug-18 12:13:28.718197
#9 5.949 Get:189 http://deb.debian.org/debian trixie/main arm64 libgpg-error-l10n all 1.51-4 [114 kB]
2025-Aug-18 12:13:28.718197
#9 5.951 Get:190 http://deb.debian.org/debian trixie/main arm64 libgpgme11t64 arm64 1.24.2-3 [329 kB]
2025-Aug-18 12:13:28.718197
#9 5.954 Get:191 http://deb.debian.org/debian trixie/main arm64 libgpgmepp6t64 arm64 1.24.2-3 [328 kB]
2025-Aug-18 12:13:28.718197
#9 5.956 Get:192 http://deb.debian.org/debian trixie/main arm64 libgraphite2-3 arm64 1.3.14-2+b1 [70.4 kB]
2025-Aug-18 12:13:28.718197
#9 5.958 Get:193 http://deb.debian.org/debian trixie/main arm64 libharfbuzz0b arm64 10.2.0-1+b1 [442 kB]
2025-Aug-18 12:13:28.718197
#9 5.962 Get:194 http://deb.debian.org/debian trixie/main arm64 libjbig0 arm64 2.1-6.1+b2 [30.4 kB]
2025-Aug-18 12:13:28.718197
#9 5.964 Get:195 http://deb.debian.org/debian trixie/main arm64 libjpeg62-turbo arm64 1:2.1.5-4 [173 kB]
2025-Aug-18 12:13:28.718197
#9 5.967 Get:196 http://deb.debian.org/debian trixie/main arm64 libkmod2 arm64 34.2-2 [59.7 kB]
2025-Aug-18 12:13:28.718197
#9 5.968 Get:197 http://deb.debian.org/debian trixie/main arm64 liblcms2-2 arm64 2.16-2 [151 kB]
2025-Aug-18 12:13:28.718197
#9 5.972 Get:198 http://deb.debian.org/debian trixie/main arm64 libldap-common all 2.6.10+dfsg-1 [35.1 kB]
2025-Aug-18 12:13:28.718197
#9 5.976 Get:199 http://deb.debian.org/debian trixie/main arm64 liblerc4 arm64 4.0.0+ds-5 [146 kB]
2025-Aug-18 12:13:28.718197
#9 5.982 Get:200 http://deb.debian.org/debian trixie/main arm64 libnspr4 arm64 2:4.36-1 [102 kB]
2025-Aug-18 12:13:28.718197
#9 5.983 Get:201 http://deb.debian.org/debian trixie/main arm64 libnss3 arm64 2:3.110-1 [1292 kB]
2025-Aug-18 12:13:28.718197
#9 5.991 Get:202 http://deb.debian.org/debian trixie/main arm64 libopenjp2-7 arm64 2.5.3-2 [190 kB]
2025-Aug-18 12:13:28.718197
#9 5.995 Get:203 http://deb.debian.org/debian trixie/main arm64 libthai-data all 0.1.29-2 [168 kB]
2025-Aug-18 12:13:28.718197
#9 5.996 Get:204 http://deb.debian.org/debian trixie/main arm64 libthai0 arm64 0.1.29-2+b1 [48.4 kB]
2025-Aug-18 12:13:28.718197
#9 5.997 Get:205 http://deb.debian.org/debian trixie/main arm64 libpango-1.0-0 arm64 1.56.3-1 [213 kB]
2025-Aug-18 12:13:28.718197
#9 6.000 Get:206 http://deb.debian.org/debian trixie/main arm64 libpangoft2-1.0-0 arm64 1.56.3-1 [52.9 kB]
2025-Aug-18 12:13:28.718197
#9 6.003 Get:207 http://deb.debian.org/debian trixie/main arm64 libpangocairo-1.0-0 arm64 1.56.3-1 [33.7 kB]
2025-Aug-18 12:13:28.718197
#9 6.007 Get:208 http://deb.debian.org/debian trixie/main arm64 libsharpyuv0 arm64 1.5.0-0.1 [114 kB]
2025-Aug-18 12:13:28.718197
#9 6.008 Get:209 http://deb.debian.org/debian trixie/main arm64 libwebp7 arm64 1.5.0-0.1 [271 kB]
2025-Aug-18 12:13:28.718197
#9 6.011 Get:210 http://deb.debian.org/debian trixie/main arm64 libtiff6 arm64 4.7.0-3 [325 kB]
2025-Aug-18 12:13:28.718197
#9 6.015 Get:211 http://deb.debian.org/debian trixie/main arm64 libpoppler147 arm64 25.03.0-5 [1912 kB]
2025-Aug-18 12:13:28.718197
#9 6.030 Get:212 http://deb.debian.org/debian trixie/main arm64 libpoppler-cpp2 arm64 25.03.0-5 [41.1 kB]
2025-Aug-18 12:13:28.718197
#9 6.032 Get:213 http://deb.debian.org/debian trixie/main arm64 libpoppler-dev arm64 25.03.0-5 [9308 B]
2025-Aug-18 12:13:28.718197
#9 6.033 Get:214 http://deb.debian.org/debian trixie/main arm64 libpoppler-cpp-dev arm64 25.03.0-5 [16.0 kB]
2025-Aug-18 12:13:28.718197
#9 6.034 Get:215 http://deb.debian.org/debian trixie/main arm64 libsasl2-modules arm64 2.1.28+dfsg1-9 [62.9 kB]
2025-Aug-18 12:13:28.718197
#9 6.036 Get:216 http://deb.debian.org/debian trixie/main arm64 libxcomposite1 arm64 1:0.4.6-1 [16.4 kB]
2025-Aug-18 12:13:28.718197
#9 6.037 Get:217 http://deb.debian.org/debian trixie/main arm64 libxfixes3 arm64 1:6.0.0-2+b4 [20.5 kB]
2025-Aug-18 12:13:28.718197
#9 6.038 Get:218 http://deb.debian.org/debian trixie/main arm64 libxcursor1 arm64 1:1.2.3-1 [39.3 kB]
2025-Aug-18 12:13:28.718197
#9 6.040 Get:219 http://deb.debian.org/debian trixie/main arm64 libxdamage1 arm64 1:1.1.6-1+b2 [15.6 kB]
2025-Aug-18 12:13:28.718197
#9 6.043 Get:220 http://deb.debian.org/debian trixie/main arm64 libxrandr2 arm64 2:1.5.4-1+b3 [35.9 kB]
2025-Aug-18 12:13:28.718197
#9 6.043 Get:221 http://deb.debian.org/debian trixie/main arm64 manpages-dev all 6.9.1-1 [2122 kB]
2025-Aug-18 12:13:28.718197
#9 6.058 Get:222 http://deb.debian.org/debian trixie/main arm64 poppler-utils arm64 25.03.0-5 [193 kB]
2025-Aug-18 12:13:28.718197
#9 6.060 Get:223 http://deb.debian.org/debian trixie/main arm64 psmisc arm64 23.7-2 [265 kB]
2025-Aug-18 12:13:28.718197
#9 6.063 Get:224 http://deb.debian.org/debian trixie/main arm64 publicsuffix all 20250328.1952-0.1 [296 kB]
2025-Aug-18 12:13:28.718197
#9 6.065 Get:225 http://deb.debian.org/debian trixie/main arm64 shared-mime-info arm64 2.4-5+b2 [756 kB]
2025-Aug-18 12:13:28.718197
#9 6.069 Get:226 http://deb.debian.org/debian trixie/main arm64 systemd-cryptsetup arm64 257.7-1 [164 kB]
2025-Aug-18 12:13:28.718197
#9 6.071 Get:227 http://deb.debian.org/debian trixie/main arm64 xdg-user-dirs arm64 0.18-2 [53.2 kB]
2025-Aug-18 12:13:28.718197
#9 6.074 Get:228 http://deb.debian.org/debian trixie/main arm64 gnupg-utils arm64 2.4.7-21+b3 [181 kB]
2025-Aug-18 12:13:28.718197
#9 6.074 Get:229 http://deb.debian.org/debian trixie/main arm64 libegl1 arm64 1.7.0-1+b2 [34.0 kB]
2025-Aug-18 12:13:28.718197
#9 6.357 debconf: unable to initialize frontend: Dialog
2025-Aug-18 12:13:28.718197
#9 6.357 debconf: (TERM is not set, so the dialog frontend is not usable.)
2025-Aug-18 12:13:28.718197
#9 6.357 debconf: falling back to frontend: Readline
2025-Aug-18 12:13:28.718197
#9 6.358 debconf: unable to initialize frontend: Readline
2025-Aug-18 12:13:28.718197
#9 6.358 debconf: (Can't locate Term/ReadLine.pm in @INC (you may need to install the Term::ReadLine module) (@INC entries checked: /etc/perl /usr/local/lib/aarch64-linux-gnu/perl/5.40.1 /usr/local/share/perl/5.40.1 /usr/lib/aarch64-linux-gnu/perl5/5.40 /usr/share/perl5 /usr/lib/aarch64-linux-gnu/perl-base /usr/lib/aarch64-linux-gnu/perl/5.40 /usr/share/perl/5.40 /usr/local/lib/site_perl) at /usr/share/perl5/Debconf/FrontEnd/Readline.pm line 8, <STDIN> line 229.)
2025-Aug-18 12:13:28.718197
#9 6.358 debconf: falling back to frontend: Teletype
2025-Aug-18 12:13:28.718197
#9 6.366 debconf: unable to initialize frontend: Teletype
2025-Aug-18 12:13:28.718197
#9 6.366 debconf: (This frontend requires a controlling tty.)
2025-Aug-18 12:13:28.718197
#9 6.366 debconf: falling back to frontend: Noninteractive
2025-Aug-18 12:13:28.718197
#9 12.40 Preconfiguring packages ...
2025-Aug-18 12:13:28.718197
#9 12.51 Fetched 156 MB in 1s (133 MB/s)
2025-Aug-18 12:13:28.718197
#9 12.55 Selecting previously unselected package libsystemd-shared:arm64.
2025-Aug-18 12:13:28.718197
#9 12.55 (Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 5641 files and directories currently installed.)
2025-Aug-18 12:13:28.718197
#9 12.56 Preparing to unpack .../libsystemd-shared_257.7-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 12.56 Unpacking libsystemd-shared:arm64 (257.7-1) ...
2025-Aug-18 12:13:28.718197
#9 12.75 Selecting previously unselected package libapparmor1:arm64.
2025-Aug-18 12:13:28.718197
#9 12.75 Preparing to unpack .../libapparmor1_4.1.0-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 12.76 Unpacking libapparmor1:arm64 (4.1.0-1) ...
2025-Aug-18 12:13:28.718197
#9 12.79 Setting up libsystemd-shared:arm64 (257.7-1) ...
2025-Aug-18 12:13:28.718197
#9 12.84 Selecting previously unselected package systemd.
2025-Aug-18 12:13:28.718197
#9 12.84 (Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 5654 files and directories currently installed.)
2025-Aug-18 12:13:28.718197
#9 12.85 Preparing to unpack .../systemd_257.7-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 12.86 Unpacking systemd (257.7-1) ...
2025-Aug-18 12:13:28.718197
#9 13.11 Setting up libapparmor1:arm64 (4.1.0-1) ...
2025-Aug-18 12:13:28.718197
#9 13.12 Setting up systemd (257.7-1) ...
2025-Aug-18 12:13:28.718197
#9 13.17 Created symlink '/etc/systemd/system/getty.target.wants/<EMAIL>' → '/usr/lib/systemd/system/getty@.service'.
2025-Aug-18 12:13:28.718197
#9 13.18 Created symlink '/etc/systemd/system/multi-user.target.wants/remote-fs.target' → '/usr/lib/systemd/system/remote-fs.target'.
2025-Aug-18 12:13:28.718197
#9 13.19 Created symlink '/etc/systemd/system/sysinit.target.wants/systemd-pstore.service' → '/usr/lib/systemd/system/systemd-pstore.service'.
2025-Aug-18 12:13:28.718197
#9 13.19 Initializing machine ID from random generator.
2025-Aug-18 12:13:28.718197
#9 13.22 Creating group 'systemd-journal' with GID 999.
2025-Aug-18 12:13:28.718197
#9 13.22 Creating group 'systemd-network' with GID 998.
2025-Aug-18 12:13:28.718197
#9 13.22 Creating user 'systemd-network' (systemd Network Management) with UID 998 and GID 998.
2025-Aug-18 12:13:28.718197
#9 13.25 /usr/lib/tmpfiles.d/legacy.conf:14: Duplicate line for path "/run/lock", ignoring.
2025-Aug-18 12:13:28.718197
#9 13.31 Selecting previously unselected package systemd-sysv.
2025-Aug-18 12:13:28.718197
#9 13.31 (Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 6592 files and directories currently installed.)
2025-Aug-18 12:13:28.718197
#9 13.32 Preparing to unpack .../000-systemd-sysv_257.7-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 13.32 Unpacking systemd-sysv (257.7-1) ...
2025-Aug-18 12:13:28.718197
#9 13.36 Selecting previously unselected package libdbus-1-3:arm64.
2025-Aug-18 12:13:28.718197
#9 13.36 Preparing to unpack .../001-libdbus-1-3_1.16.2-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 13.37 Unpacking libdbus-1-3:arm64 (1.16.2-2) ...
2025-Aug-18 12:13:28.718197
#9 13.40 Selecting previously unselected package dbus-bin.
2025-Aug-18 12:13:28.718197
#9 13.41 Preparing to unpack .../002-dbus-bin_1.16.2-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 13.41 Unpacking dbus-bin (1.16.2-2) ...
2025-Aug-18 12:13:28.718197
#9 13.45 Selecting previously unselected package dbus-session-bus-common.
2025-Aug-18 12:13:28.718197
#9 13.45 Preparing to unpack .../003-dbus-session-bus-common_1.16.2-2_all.deb ...
2025-Aug-18 12:13:28.718197
#9 13.45 Unpacking dbus-session-bus-common (1.16.2-2) ...
2025-Aug-18 12:13:28.718197
#9 13.49 Selecting previously unselected package libexpat1:arm64.
2025-Aug-18 12:13:28.718197
#9 13.49 Preparing to unpack .../004-libexpat1_2.7.1-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 13.49 Unpacking libexpat1:arm64 (2.7.1-2) ...
2025-Aug-18 12:13:28.718197
#9 13.54 Selecting previously unselected package dbus-daemon.
2025-Aug-18 12:13:28.718197
#9 13.54 Preparing to unpack .../005-dbus-daemon_1.16.2-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 13.54 Unpacking dbus-daemon (1.16.2-2) ...
2025-Aug-18 12:13:28.718197
#9 13.58 Selecting previously unselected package dbus-system-bus-common.
2025-Aug-18 12:13:28.718197
#9 13.59 Preparing to unpack .../006-dbus-system-bus-common_1.16.2-2_all.deb ...
2025-Aug-18 12:13:28.718197
#9 13.59 Unpacking dbus-system-bus-common (1.16.2-2) ...
2025-Aug-18 12:13:28.718197
#9 13.64 Selecting previously unselected package dbus.
2025-Aug-18 12:13:28.718197
#9 13.65 Preparing to unpack .../007-dbus_1.16.2-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 13.65 Unpacking dbus (1.16.2-2) ...
2025-Aug-18 12:13:28.718197
#9 13.69 Selecting previously unselected package liblocale-gettext-perl.
2025-Aug-18 12:13:28.718197
#9 13.69 Preparing to unpack .../008-liblocale-gettext-perl_1.07-7+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 13.70 Unpacking liblocale-gettext-perl (1.07-7+b1) ...
2025-Aug-18 12:13:28.718197
#9 13.73 Selecting previously unselected package poppler-data.
2025-Aug-18 12:13:28.718197
#9 13.73 Preparing to unpack .../009-poppler-data_0.4.12-1_all.deb ...
2025-Aug-18 12:13:28.718197
#9 13.74 Unpacking poppler-data (0.4.12-1) ...
2025-Aug-18 12:13:28.718197
#9 14.00 Selecting previously unselected package linux-sysctl-defaults.
2025-Aug-18 12:13:28.718197
#9 14.01 Preparing to unpack .../010-linux-sysctl-defaults_4.12_all.deb ...
2025-Aug-18 12:13:28.718197
#9 14.01 Unpacking linux-sysctl-defaults (4.12) ...
2025-Aug-18 12:13:28.718197
#9 14.05 Selecting previously unselected package libproc2-0:arm64.
2025-Aug-18 12:13:28.718197
#9 14.05 Preparing to unpack .../011-libproc2-0_2%3a4.0.4-9_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 14.05 Unpacking libproc2-0:arm64 (2:4.0.4-9) ...
2025-Aug-18 12:13:28.718197
#9 14.09 Selecting previously unselected package procps.
2025-Aug-18 12:13:28.718197
#9 14.09 Preparing to unpack .../012-procps_2%3a4.0.4-9_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 14.10 Unpacking procps (2:4.0.4-9) ...
2025-Aug-18 12:13:28.718197
#9 14.22 Selecting previously unselected package bzip2.
2025-Aug-18 12:13:28.718197
#9 14.23 Preparing to unpack .../013-bzip2_1.0.8-6_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 14.23 Unpacking bzip2 (1.0.8-6) ...
2025-Aug-18 12:13:28.718197
#9 14.27 Selecting previously unselected package krb5-locales.
2025-Aug-18 12:13:28.718197
#9 14.27 Preparing to unpack .../014-krb5-locales_1.21.3-5_all.deb ...
2025-Aug-18 12:13:28.718197
#9 14.28 Unpacking krb5-locales (1.21.3-5) ...
2025-Aug-18 12:13:28.718197
#9 14.32 Selecting previously unselected package libnss-systemd:arm64.
2025-Aug-18 12:13:28.718197
#9 14.32 Preparing to unpack .../015-libnss-systemd_257.7-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 14.33 Unpacking libnss-systemd:arm64 (257.7-1) ...
2025-Aug-18 12:13:28.718197
#9 14.39 Selecting previously unselected package libpam-systemd:arm64.
2025-Aug-18 12:13:28.718197
#9 14.39 Preparing to unpack .../016-libpam-systemd_257.7-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 14.39 Unpacking libpam-systemd:arm64 (257.7-1) ...
2025-Aug-18 12:13:28.718197
#9 14.45 Selecting previously unselected package manpages.
2025-Aug-18 12:13:28.718197
#9 14.45 Preparing to unpack .../017-manpages_6.9.1-1_all.deb ...
2025-Aug-18 12:13:28.718197
#9 14.45 Unpacking manpages (6.9.1-1) ...
2025-Aug-18 12:13:28.718197
#9 14.55 Selecting previously unselected package perl-modules-5.40.
2025-Aug-18 12:13:28.718197
#9 14.56 Preparing to unpack .../018-perl-modules-5.40_5.40.1-6_all.deb ...
2025-Aug-18 12:13:28.718197
#9 14.56 Unpacking perl-modules-5.40 (5.40.1-6) ...
2025-Aug-18 12:13:28.718197
#9 14.96 Selecting previously unselected package libgdbm-compat4t64:arm64.
2025-Aug-18 12:13:28.718197
#9 14.97 Preparing to unpack .../019-libgdbm-compat4t64_1.24-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 14.97 Unpacking libgdbm-compat4t64:arm64 (1.24-2) ...
2025-Aug-18 12:13:28.718197
#9 15.02 Selecting previously unselected package libperl5.40:arm64.
2025-Aug-18 12:13:28.718197
#9 15.02 Preparing to unpack .../020-libperl5.40_5.40.1-6_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 15.02 Unpacking libperl5.40:arm64 (5.40.1-6) ...
2025-Aug-18 12:13:28.718197
#9 15.42 Selecting previously unselected package perl.
2025-Aug-18 12:13:28.718197
#9 15.43 Preparing to unpack .../021-perl_5.40.1-6_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 15.43 Unpacking perl (5.40.1-6) ...
2025-Aug-18 12:13:28.718197
#9 15.48 Selecting previously unselected package systemd-timesyncd.
2025-Aug-18 12:13:28.718197
#9 15.49 Preparing to unpack .../022-systemd-timesyncd_257.7-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 15.49 Unpacking systemd-timesyncd (257.7-1) ...
2025-Aug-18 12:13:28.718197
#9 15.53 Selecting previously unselected package libunistring5:arm64.
2025-Aug-18 12:13:28.718197
#9 15.54 Preparing to unpack .../023-libunistring5_1.3-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 15.54 Unpacking libunistring5:arm64 (1.3-2) ...
2025-Aug-18 12:13:28.718197
#9 15.62 Selecting previously unselected package libidn2-0:arm64.
2025-Aug-18 12:13:28.718197
#9 15.63 Preparing to unpack .../024-libidn2-0_2.3.8-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 15.63 Unpacking libidn2-0:arm64 (2.3.8-2) ...
2025-Aug-18 12:13:28.718197
#9 15.69 Selecting previously unselected package libp11-kit0:arm64.
2025-Aug-18 12:13:28.718197
#9 15.70 Preparing to unpack .../025-libp11-kit0_0.25.5-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 15.70 Unpacking libp11-kit0:arm64 (0.25.5-3) ...
2025-Aug-18 12:13:28.718197
#9 15.81 Selecting previously unselected package libtasn1-6:arm64.
2025-Aug-18 12:13:28.718197
#9 15.82 Preparing to unpack .../026-libtasn1-6_4.20.0-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 15.83 Unpacking libtasn1-6:arm64 (4.20.0-2) ...
2025-Aug-18 12:13:28.718197
#9 15.89 Selecting previously unselected package libgnutls30t64:arm64.
2025-Aug-18 12:13:28.718197
#9 15.90 Preparing to unpack .../027-libgnutls30t64_3.8.9-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 15.90 Unpacking libgnutls30t64:arm64 (3.8.9-3) ...
2025-Aug-18 12:13:28.718197
#9 16.02 Selecting previously unselected package libpsl5t64:arm64.
2025-Aug-18 12:13:28.718197
#9 16.03 Preparing to unpack .../028-libpsl5t64_0.21.2-1.1+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 16.03 Unpacking libpsl5t64:arm64 (0.21.2-1.1+b1) ...
2025-Aug-18 12:13:28.718197
#9 16.07 Selecting previously unselected package wget.
2025-Aug-18 12:13:28.718197
#9 16.07 Preparing to unpack .../029-wget_1.25.0-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 16.07 Unpacking wget (1.25.0-2) ...
2025-Aug-18 12:13:28.718197
#9 16.16 Selecting previously unselected package xz-utils.
2025-Aug-18 12:13:28.718197
#9 16.16 Preparing to unpack .../030-xz-utils_5.8.1-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 16.17 Unpacking xz-utils (5.8.1-1) ...
2025-Aug-18 12:13:28.718197
#9 16.24 Selecting previously unselected package alsa-topology-conf.
2025-Aug-18 12:13:28.718197
#9 16.24 Preparing to unpack .../031-alsa-topology-conf_1.2.5.1-3_all.deb ...
2025-Aug-18 12:13:28.718197
#9 16.24 Unpacking alsa-topology-conf (1.2.5.1-3) ...
2025-Aug-18 12:13:28.718197
#9 16.29 Selecting previously unselected package libasound2-data.
2025-Aug-18 12:13:28.718197
#9 16.29 Preparing to unpack .../032-libasound2-data_1.2.14-1_all.deb ...
2025-Aug-18 12:13:28.718197
#9 16.29 Unpacking libasound2-data (1.2.14-1) ...
2025-Aug-18 12:13:28.718197
#9 16.35 Selecting previously unselected package libasound2t64:arm64.
2025-Aug-18 12:13:28.718197
#9 16.35 Preparing to unpack .../033-libasound2t64_1.2.14-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 16.35 Unpacking libasound2t64:arm64 (1.2.14-1) ...
2025-Aug-18 12:13:28.718197
#9 16.41 Selecting previously unselected package alsa-ucm-conf.
2025-Aug-18 12:13:28.718197
#9 16.42 Preparing to unpack .../034-alsa-ucm-conf_1.2.14-1_all.deb ...
2025-Aug-18 12:13:28.718197
#9 16.42 Unpacking alsa-ucm-conf (1.2.14-1) ...
2025-Aug-18 12:13:28.718197
#9 16.58 Selecting previously unselected package at-spi2-common.
2025-Aug-18 12:13:28.718197
#9 16.59 Preparing to unpack .../035-at-spi2-common_2.56.2-1_all.deb ...
2025-Aug-18 12:13:28.718197
#9 16.59 Unpacking at-spi2-common (2.56.2-1) ...
2025-Aug-18 12:13:28.718197
#9 16.64 Selecting previously unselected package libatomic1:arm64.
2025-Aug-18 12:13:28.718197
#9 16.64 Preparing to unpack .../036-libatomic1_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 16.64 Unpacking libatomic1:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 16.69 Selecting previously unselected package libglib2.0-0t64:arm64.
2025-Aug-18 12:13:28.718197
#9 16.70 Preparing to unpack .../037-libglib2.0-0t64_2.84.3-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 16.71 Unpacking libglib2.0-0t64:arm64 (2.84.3-1) ...
2025-Aug-18 12:13:28.718197
#9 16.88 Selecting previously unselected package libxau6:arm64.
2025-Aug-18 12:13:28.718197
#9 16.88 Preparing to unpack .../038-libxau6_1%3a1.0.11-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 16.89 Unpacking libxau6:arm64 (1:1.0.11-1) ...
2025-Aug-18 12:13:28.718197
#9 16.94 Selecting previously unselected package libxdmcp6:arm64.
2025-Aug-18 12:13:28.718197
#9 16.95 Preparing to unpack .../039-libxdmcp6_1%3a1.1.5-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 16.95 Unpacking libxdmcp6:arm64 (1:1.1.5-1) ...
2025-Aug-18 12:13:28.718197
#9 17.00 Selecting previously unselected package libxcb1:arm64.
2025-Aug-18 12:13:28.718197
#9 17.01 Preparing to unpack .../040-libxcb1_1.17.0-2+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 17.01 Unpacking libxcb1:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 17.06 Selecting previously unselected package libx11-data.
2025-Aug-18 12:13:28.718197
#9 17.07 Preparing to unpack .../041-libx11-data_2%3a1.8.12-1_all.deb ...
2025-Aug-18 12:13:28.718197
#9 17.07 Unpacking libx11-data (2:1.8.12-1) ...
2025-Aug-18 12:13:28.718197
#9 17.20 Selecting previously unselected package libx11-6:arm64.
2025-Aug-18 12:13:28.718197
#9 17.20 Preparing to unpack .../042-libx11-6_2%3a1.8.12-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 17.21 Unpacking libx11-6:arm64 (2:1.8.12-1) ...
2025-Aug-18 12:13:28.718197
#9 17.36 Selecting previously unselected package libxext6:arm64.
2025-Aug-18 12:13:28.718197
#9 17.36 Preparing to unpack .../043-libxext6_2%3a1.3.4-1+b3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 17.36 Unpacking libxext6:arm64 (2:1.3.4-1+b3) ...
2025-Aug-18 12:13:28.718197
#9 17.42 Selecting previously unselected package libxi6:arm64.
2025-Aug-18 12:13:28.718197
#9 17.42 Preparing to unpack .../044-libxi6_2%3a1.8.2-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 17.43 Unpacking libxi6:arm64 (2:1.8.2-1) ...
2025-Aug-18 12:13:28.718197
#9 17.49 Selecting previously unselected package libatspi2.0-0t64:arm64.
2025-Aug-18 12:13:28.718197
#9 17.50 Preparing to unpack .../045-libatspi2.0-0t64_2.56.2-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 17.50 Unpacking libatspi2.0-0t64:arm64 (2.56.2-1) ...
2025-Aug-18 12:13:28.718197
#9 17.59 Selecting previously unselected package x11-common.
2025-Aug-18 12:13:28.718197
#9 17.59 Preparing to unpack .../046-x11-common_1%3a7.7+24_all.deb ...
2025-Aug-18 12:13:28.718197
#9 17.60 Unpacking x11-common (1:7.7+24) ...
2025-Aug-18 12:13:28.718197
#9 17.67 Selecting previously unselected package libxtst6:arm64.
2025-Aug-18 12:13:28.718197
#9 17.68 Preparing to unpack .../047-libxtst6_2%3a1.2.5-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 17.68 Unpacking libxtst6:arm64 (2:1.2.5-1) ...
2025-Aug-18 12:13:28.718197
#9 17.74 Selecting previously unselected package dbus-user-session.
2025-Aug-18 12:13:28.718197
#9 17.74 Preparing to unpack .../048-dbus-user-session_1.16.2-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 17.74 Unpacking dbus-user-session (1.16.2-2) ...
2025-Aug-18 12:13:28.718197
#9 17.80 Selecting previously unselected package libdconf1:arm64.
2025-Aug-18 12:13:28.718197
#9 17.81 Preparing to unpack .../049-libdconf1_0.40.0-5_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 17.81 Unpacking libdconf1:arm64 (0.40.0-5) ...
2025-Aug-18 12:13:28.718197
#9 17.86 Selecting previously unselected package dconf-service.
2025-Aug-18 12:13:28.718197
#9 17.87 Preparing to unpack .../050-dconf-service_0.40.0-5_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 17.87 Unpacking dconf-service (0.40.0-5) ...
2025-Aug-18 12:13:28.718197
#9 17.92 Selecting previously unselected package dconf-gsettings-backend:arm64.
2025-Aug-18 12:13:28.718197
#9 17.92 Preparing to unpack .../051-dconf-gsettings-backend_0.40.0-5_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 17.92 Unpacking dconf-gsettings-backend:arm64 (0.40.0-5) ...
2025-Aug-18 12:13:28.718197
#9 17.97 Selecting previously unselected package gsettings-desktop-schemas.
2025-Aug-18 12:13:28.718197
#9 17.97 Preparing to unpack .../052-gsettings-desktop-schemas_48.0-1_all.deb ...
2025-Aug-18 12:13:28.718197
#9 17.98 Unpacking gsettings-desktop-schemas (48.0-1) ...
2025-Aug-18 12:13:28.718197
#9 18.09 Selecting previously unselected package at-spi2-core.
2025-Aug-18 12:13:28.718197
#9 18.10 Preparing to unpack .../053-at-spi2-core_2.56.2-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 18.10 Unpacking at-spi2-core (2.56.2-1) ...
2025-Aug-18 12:13:28.718197
#9 18.14 Selecting previously unselected package libsframe1:arm64.
2025-Aug-18 12:13:28.718197
#9 18.15 Preparing to unpack .../054-libsframe1_2.44-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 18.15 Unpacking libsframe1:arm64 (2.44-3) ...
2025-Aug-18 12:13:28.718197
#9 18.20 Selecting previously unselected package binutils-common:arm64.
2025-Aug-18 12:13:28.718197
#9 18.21 Preparing to unpack .../055-binutils-common_2.44-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 18.21 Unpacking binutils-common:arm64 (2.44-3) ...
2025-Aug-18 12:13:28.718197
#9 18.46 Selecting previously unselected package libbinutils:arm64.
2025-Aug-18 12:13:28.718197
#9 18.46 Preparing to unpack .../056-libbinutils_2.44-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 18.47 Unpacking libbinutils:arm64 (2.44-3) ...
2025-Aug-18 12:13:28.718197
#9 18.57 Selecting previously unselected package libgprofng0:arm64.
2025-Aug-18 12:13:28.718197
#9 18.58 Preparing to unpack .../057-libgprofng0_2.44-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 18.58 Unpacking libgprofng0:arm64 (2.44-3) ...
2025-Aug-18 12:13:28.718197
#9 18.66 Selecting previously unselected package libctf-nobfd0:arm64.
2025-Aug-18 12:13:28.718197
#9 18.67 Preparing to unpack .../058-libctf-nobfd0_2.44-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 18.67 Unpacking libctf-nobfd0:arm64 (2.44-3) ...
2025-Aug-18 12:13:28.718197
#9 18.71 Selecting previously unselected package libctf0:arm64.
2025-Aug-18 12:13:28.718197
#9 18.72 Preparing to unpack .../059-libctf0_2.44-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 18.72 Unpacking libctf0:arm64 (2.44-3) ...
2025-Aug-18 12:13:28.718197
#9 18.77 Selecting previously unselected package libjansson4:arm64.
2025-Aug-18 12:13:28.718197
#9 18.77 Preparing to unpack .../060-libjansson4_2.14-2+b3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 18.77 Unpacking libjansson4:arm64 (2.14-2+b3) ...
2025-Aug-18 12:13:28.718197
#9 18.82 Selecting previously unselected package binutils-aarch64-linux-gnu.
2025-Aug-18 12:13:28.718197
#9 18.83 Preparing to unpack .../061-binutils-aarch64-linux-gnu_2.44-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 18.83 Unpacking binutils-aarch64-linux-gnu (2.44-3) ...
2025-Aug-18 12:13:28.718197
#9 19.10 Selecting previously unselected package binutils.
2025-Aug-18 12:13:28.718197
#9 19.11 Preparing to unpack .../062-binutils_2.44-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 19.11 Unpacking binutils (2.44-3) ...
2025-Aug-18 12:13:28.718197
#9 19.22 Selecting previously unselected package libc-dev-bin.
2025-Aug-18 12:13:28.718197
#9 19.22 Preparing to unpack .../063-libc-dev-bin_2.41-12_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 19.23 Unpacking libc-dev-bin (2.41-12) ...
2025-Aug-18 12:13:28.718197
#9 19.26 Selecting previously unselected package linux-libc-dev.
2025-Aug-18 12:13:28.718197
#9 19.27 Preparing to unpack .../064-linux-libc-dev_6.12.41-1_all.deb ...
2025-Aug-18 12:13:28.718197
#9 19.27 Unpacking linux-libc-dev (6.12.41-1) ...
2025-Aug-18 12:13:28.718197
#9 19.68 Selecting previously unselected package libcrypt-dev:arm64.
2025-Aug-18 12:13:28.718197
#9 19.69 Preparing to unpack .../065-libcrypt-dev_1%3a4.4.38-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 19.70 Unpacking libcrypt-dev:arm64 (1:4.4.38-1) ...
2025-Aug-18 12:13:28.718197
#9 19.75 Selecting previously unselected package rpcsvc-proto.
2025-Aug-18 12:13:28.718197
#9 19.75 Preparing to unpack .../066-rpcsvc-proto_1.4.3-1+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 19.76 Unpacking rpcsvc-proto (1.4.3-1+b1) ...
2025-Aug-18 12:13:28.718197
#9 19.81 Selecting previously unselected package libc6-dev:arm64.
2025-Aug-18 12:13:28.718197
#9 19.81 Preparing to unpack .../067-libc6-dev_2.41-12_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 19.82 Unpacking libc6-dev:arm64 (2.41-12) ...
2025-Aug-18 12:13:28.718197
#9 20.08 Selecting previously unselected package libisl23:arm64.
2025-Aug-18 12:13:28.718197
#9 20.09 Preparing to unpack .../068-libisl23_0.27-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 20.09 Unpacking libisl23:arm64 (0.27-1) ...
2025-Aug-18 12:13:28.718197
#9 20.20 Selecting previously unselected package libmpfr6:arm64.
2025-Aug-18 12:13:28.718197
#9 20.20 Preparing to unpack .../069-libmpfr6_4.2.2-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 20.20 Unpacking libmpfr6:arm64 (4.2.2-1) ...
2025-Aug-18 12:13:28.718197
#9 20.27 Selecting previously unselected package libmpc3:arm64.
2025-Aug-18 12:13:28.718197
#9 20.27 Preparing to unpack .../070-libmpc3_1.3.1-1+b3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 20.28 Unpacking libmpc3:arm64 (1.3.1-1+b3) ...
2025-Aug-18 12:13:28.718197
#9 20.31 Selecting previously unselected package cpp-14-aarch64-linux-gnu.
2025-Aug-18 12:13:28.718197
#9 20.31 Preparing to unpack .../071-cpp-14-aarch64-linux-gnu_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 20.32 Unpacking cpp-14-aarch64-linux-gnu (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 20.94 Selecting previously unselected package cpp-14.
2025-Aug-18 12:13:28.718197
#9 20.95 Preparing to unpack .../072-cpp-14_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 20.96 Unpacking cpp-14 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 20.98 Selecting previously unselected package cpp-aarch64-linux-gnu.
2025-Aug-18 12:13:28.718197
#9 20.98 Preparing to unpack .../073-cpp-aarch64-linux-gnu_4%3a14.2.0-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 20.99 Unpacking cpp-aarch64-linux-gnu (4:14.2.0-1) ...
2025-Aug-18 12:13:28.718197
#9 21.02 Selecting previously unselected package cpp.
2025-Aug-18 12:13:28.718197
#9 21.02 Preparing to unpack .../074-cpp_4%3a14.2.0-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 21.03 Unpacking cpp (4:14.2.0-1) ...
2025-Aug-18 12:13:28.718197
#9 21.07 Selecting previously unselected package libcc1-0:arm64.
2025-Aug-18 12:13:28.718197
#9 21.07 Preparing to unpack .../075-libcc1-0_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 21.08 Unpacking libcc1-0:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 21.12 Selecting previously unselected package libgomp1:arm64.
2025-Aug-18 12:13:28.718197
#9 21.13 Preparing to unpack .../076-libgomp1_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 21.14 Unpacking libgomp1:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 21.18 Selecting previously unselected package libitm1:arm64.
2025-Aug-18 12:13:28.718197
#9 21.18 Preparing to unpack .../077-libitm1_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 21.19 Unpacking libitm1:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 21.23 Selecting previously unselected package libasan8:arm64.
2025-Aug-18 12:13:28.718197
#9 21.23 Preparing to unpack .../078-libasan8_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 21.24 Unpacking libasan8:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 21.46 Selecting previously unselected package liblsan0:arm64.
2025-Aug-18 12:13:28.718197
#9 21.47 Preparing to unpack .../079-liblsan0_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 21.47 Unpacking liblsan0:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 21.59 Selecting previously unselected package libtsan2:arm64.
2025-Aug-18 12:13:28.718197
#9 21.59 Preparing to unpack .../080-libtsan2_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 21.59 Unpacking libtsan2:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 21.78 Selecting previously unselected package libubsan1:arm64.
2025-Aug-18 12:13:28.718197
#9 21.79 Preparing to unpack .../081-libubsan1_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 21.79 Unpacking libubsan1:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 21.89 Selecting previously unselected package libhwasan0:arm64.
2025-Aug-18 12:13:28.718197
#9 21.89 Preparing to unpack .../082-libhwasan0_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 21.89 Unpacking libhwasan0:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 22.02 Selecting previously unselected package libgcc-14-dev:arm64.
2025-Aug-18 12:13:28.718197
#9 22.02 Preparing to unpack .../083-libgcc-14-dev_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 22.04 Unpacking libgcc-14-dev:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 22.23 Selecting previously unselected package gcc-14-aarch64-linux-gnu.
2025-Aug-18 12:13:28.718197
#9 22.24 Preparing to unpack .../084-gcc-14-aarch64-linux-gnu_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 22.24 Unpacking gcc-14-aarch64-linux-gnu (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 23.20 Selecting previously unselected package gcc-14.
2025-Aug-18 12:13:28.718197
#9 23.21 Preparing to unpack .../085-gcc-14_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 23.21 Unpacking gcc-14 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 23.26 Selecting previously unselected package gcc-aarch64-linux-gnu.
2025-Aug-18 12:13:28.718197
#9 23.26 Preparing to unpack .../086-gcc-aarch64-linux-gnu_4%3a14.2.0-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 23.26 Unpacking gcc-aarch64-linux-gnu (4:14.2.0-1) ...
2025-Aug-18 12:13:28.718197
#9 23.30 Selecting previously unselected package gcc.
2025-Aug-18 12:13:28.718197
#9 23.30 Preparing to unpack .../087-gcc_4%3a14.2.0-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 23.31 Unpacking gcc (4:14.2.0-1) ...
2025-Aug-18 12:13:28.718197
#9 23.34 Selecting previously unselected package libstdc++-14-dev:arm64.
2025-Aug-18 12:13:28.718197
#9 23.35 Preparing to unpack .../088-libstdc++-14-dev_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 23.35 Unpacking libstdc++-14-dev:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 23.59 Selecting previously unselected package g++-14-aarch64-linux-gnu.
2025-Aug-18 12:13:28.718197
#9 23.59 Preparing to unpack .../089-g++-14-aarch64-linux-gnu_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 23.60 Unpacking g++-14-aarch64-linux-gnu (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 24.31 Selecting previously unselected package g++-14.
2025-Aug-18 12:13:28.718197
#9 24.31 Preparing to unpack .../090-g++-14_14.2.0-19_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 24.31 Unpacking g++-14 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 24.35 Selecting previously unselected package g++-aarch64-linux-gnu.
2025-Aug-18 12:13:28.718197
#9 24.35 Preparing to unpack .../091-g++-aarch64-linux-gnu_4%3a14.2.0-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 24.35 Unpacking g++-aarch64-linux-gnu (4:14.2.0-1) ...
2025-Aug-18 12:13:28.718197
#9 24.38 Selecting previously unselected package g++.
2025-Aug-18 12:13:28.718197
#9 24.39 Preparing to unpack .../092-g++_4%3a14.2.0-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 24.39 Unpacking g++ (4:14.2.0-1) ...
2025-Aug-18 12:13:28.718197
#9 24.43 Selecting previously unselected package make.
2025-Aug-18 12:13:28.718197
#9 24.43 Preparing to unpack .../093-make_4.4.1-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 24.44 Unpacking make (4.4.1-2) ...
2025-Aug-18 12:13:28.718197
#9 24.49 Selecting previously unselected package libdpkg-perl.
2025-Aug-18 12:13:28.718197
#9 24.50 Preparing to unpack .../094-libdpkg-perl_1.22.21_all.deb ...
2025-Aug-18 12:13:28.718197
#9 24.50 Unpacking libdpkg-perl (1.22.21) ...
2025-Aug-18 12:13:28.718197
#9 24.59 Selecting previously unselected package patch.
2025-Aug-18 12:13:28.718197
#9 24.59 Preparing to unpack .../095-patch_2.8-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 24.60 Unpacking patch (2.8-2) ...
2025-Aug-18 12:13:28.718197
#9 24.64 Selecting previously unselected package dpkg-dev.
2025-Aug-18 12:13:28.718197
#9 24.65 Preparing to unpack .../096-dpkg-dev_1.22.21_all.deb ...
2025-Aug-18 12:13:28.718197
#9 24.65 Unpacking dpkg-dev (1.22.21) ...
2025-Aug-18 12:13:28.718197
#9 24.75 Selecting previously unselected package build-essential.
2025-Aug-18 12:13:28.718197
#9 24.76 Preparing to unpack .../097-build-essential_12.12_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 24.76 Unpacking build-essential (12.12) ...
2025-Aug-18 12:13:28.718197
#9 24.80 Selecting previously unselected package libgpg-error0:arm64.
2025-Aug-18 12:13:28.718197
#9 24.80 Preparing to unpack .../098-libgpg-error0_1.51-4_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 24.80 Unpacking libgpg-error0:arm64 (1.51-4) ...
2025-Aug-18 12:13:28.718197
#9 24.84 Selecting previously unselected package libassuan9:arm64.
2025-Aug-18 12:13:28.718197
#9 24.85 Preparing to unpack .../099-libassuan9_3.0.2-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 24.85 Unpacking libassuan9:arm64 (3.0.2-2) ...
2025-Aug-18 12:13:28.718197
#9 24.89 Selecting previously unselected package libgcrypt20:arm64.
2025-Aug-18 12:13:28.718197
#9 24.90 Preparing to unpack .../100-libgcrypt20_1.11.0-7_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 24.90 Unpacking libgcrypt20:arm64 (1.11.0-7) ...
2025-Aug-18 12:13:28.718197
#9 24.97 Selecting previously unselected package gpgconf.
2025-Aug-18 12:13:28.718197
#9 24.97 Preparing to unpack .../101-gpgconf_2.4.7-21+b3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 24.97 Unpacking gpgconf (2.4.7-21+b3) ...
2025-Aug-18 12:13:28.718197
#9 25.02 Selecting previously unselected package libksba8:arm64.
2025-Aug-18 12:13:28.718197
#9 25.03 Preparing to unpack .../102-libksba8_1.6.7-2+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 25.04 Unpacking libksba8:arm64 (1.6.7-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 25.08 Selecting previously unselected package libsasl2-modules-db:arm64.
2025-Aug-18 12:13:28.718197
#9 25.09 Preparing to unpack .../103-libsasl2-modules-db_2.1.28+dfsg1-9_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 25.09 Unpacking libsasl2-modules-db:arm64 (2.1.28+dfsg1-9) ...
2025-Aug-18 12:13:28.718197
#9 25.14 Selecting previously unselected package libsasl2-2:arm64.
2025-Aug-18 12:13:28.718197
#9 25.14 Preparing to unpack .../104-libsasl2-2_2.1.28+dfsg1-9_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 25.14 Unpacking libsasl2-2:arm64 (2.1.28+dfsg1-9) ...
2025-Aug-18 12:13:28.718197
#9 25.18 Selecting previously unselected package libldap2:arm64.
2025-Aug-18 12:13:28.718197
#9 25.18 Preparing to unpack .../105-libldap2_2.6.10+dfsg-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 25.19 Unpacking libldap2:arm64 (2.6.10+dfsg-1) ...
2025-Aug-18 12:13:28.718197
#9 25.24 Selecting previously unselected package libnpth0t64:arm64.
2025-Aug-18 12:13:28.718197
#9 25.24 Preparing to unpack .../106-libnpth0t64_1.8-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 25.24 Unpacking libnpth0t64:arm64 (1.8-3) ...
2025-Aug-18 12:13:28.718197
#9 25.28 Selecting previously unselected package dirmngr.
2025-Aug-18 12:13:28.718197
#9 25.28 Preparing to unpack .../107-dirmngr_2.4.7-21+b3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 25.30 Unpacking dirmngr (2.4.7-21+b3) ...
2025-Aug-18 12:13:28.718197
#9 25.36 Selecting previously unselected package libdevmapper1.02.1:arm64.
2025-Aug-18 12:13:28.718197
#9 25.36 Preparing to unpack .../108-libdevmapper1.02.1_2%3a1.02.205-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 25.37 Unpacking libdevmapper1.02.1:arm64 (2:1.02.205-2) ...
2025-Aug-18 12:13:28.718197
#9 25.42 Selecting previously unselected package dmsetup.
2025-Aug-18 12:13:28.718197
#9 25.42 Preparing to unpack .../109-dmsetup_2%3a1.02.205-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 25.42 Unpacking dmsetup (2:1.02.205-2) ...
2025-Aug-18 12:13:28.718197
#9 25.47 Selecting previously unselected package libfakeroot:arm64.
2025-Aug-18 12:13:28.718197
#9 25.47 Preparing to unpack .../110-libfakeroot_********-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 25.48 Unpacking libfakeroot:arm64 (********-1) ...
2025-Aug-18 12:13:28.718197
#9 25.52 Selecting previously unselected package fakeroot.
2025-Aug-18 12:13:28.718197
#9 25.52 Preparing to unpack .../111-fakeroot_********-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 25.53 Unpacking fakeroot (********-1) ...
2025-Aug-18 12:13:28.718197
#9 25.58 Selecting previously unselected package libbrotli1:arm64.
2025-Aug-18 12:13:28.718197
#9 25.58 Preparing to unpack .../112-libbrotli1_1.1.0-2+b7_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 25.59 Unpacking libbrotli1:arm64 (1.1.0-2+b7) ...
2025-Aug-18 12:13:28.718197
#9 25.65 Selecting previously unselected package libpng16-16t64:arm64.
2025-Aug-18 12:13:28.718197
#9 25.65 Preparing to unpack .../113-libpng16-16t64_1.6.48-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 25.66 Unpacking libpng16-16t64:arm64 (1.6.48-1) ...
2025-Aug-18 12:13:28.718197
#9 25.72 Selecting previously unselected package libfreetype6:arm64.
2025-Aug-18 12:13:28.718197
#9 25.72 Preparing to unpack .../114-libfreetype6_2.13.3+dfsg-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 25.73 Unpacking libfreetype6:arm64 (2.13.3+dfsg-1) ...
2025-Aug-18 12:13:28.718197
#9 25.79 Selecting previously unselected package fonts-dejavu-mono.
2025-Aug-18 12:13:28.718197
#9 25.80 Preparing to unpack .../115-fonts-dejavu-mono_2.37-8_all.deb ...
2025-Aug-18 12:13:28.718197
#9 25.80 Unpacking fonts-dejavu-mono (2.37-8) ...
2025-Aug-18 12:13:28.718197
#9 25.89 Selecting previously unselected package fonts-dejavu-core.
2025-Aug-18 12:13:28.718197
#9 25.89 Preparing to unpack .../116-fonts-dejavu-core_2.37-8_all.deb ...
2025-Aug-18 12:13:28.718197
#9 25.93 Unpacking fonts-dejavu-core (2.37-8) ...
2025-Aug-18 12:13:28.718197
#9 26.02 Selecting previously unselected package fontconfig-config.
2025-Aug-18 12:13:28.718197
#9 26.02 Preparing to unpack .../117-fontconfig-config_2.15.0-2.3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 26.03 Unpacking fontconfig-config (2.15.0-2.3) ...
2025-Aug-18 12:13:28.718197
#9 26.08 Selecting previously unselected package libfontconfig1:arm64.
2025-Aug-18 12:13:28.718197
#9 26.09 Preparing to unpack .../118-libfontconfig1_2.15.0-2.3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 26.09 Unpacking libfontconfig1:arm64 (2.15.0-2.3) ...
2025-Aug-18 12:13:28.718197
#9 26.14 Selecting previously unselected package fontconfig.
2025-Aug-18 12:13:28.718197
#9 26.15 Preparing to unpack .../119-fontconfig_2.15.0-2.3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 26.15 Unpacking fontconfig (2.15.0-2.3) ...
2025-Aug-18 12:13:28.718197
#9 26.23 Selecting previously unselected package gnupg-l10n.
2025-Aug-18 12:13:28.718197
#9 26.23 Preparing to unpack .../120-gnupg-l10n_2.4.7-21_all.deb ...
2025-Aug-18 12:13:28.718197
#9 26.24 Unpacking gnupg-l10n (2.4.7-21) ...
2025-Aug-18 12:13:28.718197
#9 26.34 Selecting previously unselected package gpg.
2025-Aug-18 12:13:28.718197
#9 26.35 Preparing to unpack .../121-gpg_2.4.7-21+b3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 26.36 Unpacking gpg (2.4.7-21+b3) ...
2025-Aug-18 12:13:28.718197
#9 26.46 Selecting previously unselected package pinentry-curses.
2025-Aug-18 12:13:28.718197
#9 26.47 Preparing to unpack .../122-pinentry-curses_1.3.1-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 26.48 Unpacking pinentry-curses (1.3.1-2) ...
2025-Aug-18 12:13:28.718197
#9 26.56 Selecting previously unselected package gpg-agent.
2025-Aug-18 12:13:28.718197
#9 26.57 Preparing to unpack .../123-gpg-agent_2.4.7-21+b3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 26.57 Unpacking gpg-agent (2.4.7-21+b3) ...
2025-Aug-18 12:13:28.718197
#9 26.68 Selecting previously unselected package gpgsm.
2025-Aug-18 12:13:28.718197
#9 26.68 Preparing to unpack .../124-gpgsm_2.4.7-21+b3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 26.69 Unpacking gpgsm (2.4.7-21+b3) ...
2025-Aug-18 12:13:28.718197
#9 26.76 Selecting previously unselected package gnupg.
2025-Aug-18 12:13:28.718197
#9 26.76 Preparing to unpack .../125-gnupg_2.4.7-21_all.deb ...
2025-Aug-18 12:13:28.718197
#9 26.76 Unpacking gnupg (2.4.7-21) ...
2025-Aug-18 12:13:28.718197
#9 26.83 Selecting previously unselected package gpg-wks-client.
2025-Aug-18 12:13:28.718197
#9 26.83 Preparing to unpack .../126-gpg-wks-client_2.4.7-21+b3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 26.84 Unpacking gpg-wks-client (2.4.7-21+b3) ...
2025-Aug-18 12:13:28.718197
#9 26.89 Selecting previously unselected package gpgv.
2025-Aug-18 12:13:28.718197
#9 26.89 Preparing to unpack .../127-gpgv_2.4.7-21+b3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 26.89 Unpacking gpgv (2.4.7-21+b3) ...
2025-Aug-18 12:13:28.718197
#9 26.94 Selecting previously unselected package libalgorithm-diff-perl.
2025-Aug-18 12:13:28.718197
#9 26.95 Preparing to unpack .../128-libalgorithm-diff-perl_1.201-1_all.deb ...
2025-Aug-18 12:13:28.718197
#9 26.95 Unpacking libalgorithm-diff-perl (1.201-1) ...
2025-Aug-18 12:13:28.718197
#9 26.99 Selecting previously unselected package libalgorithm-diff-xs-perl.
2025-Aug-18 12:13:28.718197
#9 26.99 Preparing to unpack .../129-libalgorithm-diff-xs-perl_0.04-9_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 26.99 Unpacking libalgorithm-diff-xs-perl (0.04-9) ...
2025-Aug-18 12:13:28.718197
#9 27.03 Selecting previously unselected package libalgorithm-merge-perl.
2025-Aug-18 12:13:28.718197
#9 27.03 Preparing to unpack .../130-libalgorithm-merge-perl_0.08-5_all.deb ...
2025-Aug-18 12:13:28.718197
#9 27.03 Unpacking libalgorithm-merge-perl (0.08-5) ...
2025-Aug-18 12:13:28.718197
#9 27.07 Selecting previously unselected package libatk1.0-0t64:arm64.
2025-Aug-18 12:13:28.718197
#9 27.08 Preparing to unpack .../131-libatk1.0-0t64_2.56.2-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.08 Unpacking libatk1.0-0t64:arm64 (2.56.2-1) ...
2025-Aug-18 12:13:28.718197
#9 27.13 Selecting previously unselected package libatk-bridge2.0-0t64:arm64.
2025-Aug-18 12:13:28.718197
#9 27.14 Preparing to unpack .../132-libatk-bridge2.0-0t64_2.56.2-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.14 Unpacking libatk-bridge2.0-0t64:arm64 (2.56.2-1) ...
2025-Aug-18 12:13:28.718197
#9 27.18 Selecting previously unselected package libavahi-common-data:arm64.
2025-Aug-18 12:13:28.718197
#9 27.19 Preparing to unpack .../133-libavahi-common-data_0.8-16_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.19 Unpacking libavahi-common-data:arm64 (0.8-16) ...
2025-Aug-18 12:13:28.718197
#9 27.24 Selecting previously unselected package libavahi-common3:arm64.
2025-Aug-18 12:13:28.718197
#9 27.24 Preparing to unpack .../134-libavahi-common3_0.8-16_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.24 Unpacking libavahi-common3:arm64 (0.8-16) ...
2025-Aug-18 12:13:28.718197
#9 27.29 Selecting previously unselected package libavahi-client3:arm64.
2025-Aug-18 12:13:28.718197
#9 27.29 Preparing to unpack .../135-libavahi-client3_0.8-16_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.29 Unpacking libavahi-client3:arm64 (0.8-16) ...
2025-Aug-18 12:13:28.718197
#9 27.34 Selecting previously unselected package libpixman-1-0:arm64.
2025-Aug-18 12:13:28.718197
#9 27.34 Preparing to unpack .../136-libpixman-1-0_0.44.0-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.35 Unpacking libpixman-1-0:arm64 (0.44.0-3) ...
2025-Aug-18 12:13:28.718197
#9 27.39 Selecting previously unselected package libxcb-render0:arm64.
2025-Aug-18 12:13:28.718197
#9 27.40 Preparing to unpack .../137-libxcb-render0_1.17.0-2+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.40 Unpacking libxcb-render0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 27.44 Selecting previously unselected package libxcb-shm0:arm64.
2025-Aug-18 12:13:28.718197
#9 27.45 Preparing to unpack .../138-libxcb-shm0_1.17.0-2+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.45 Unpacking libxcb-shm0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 27.49 Selecting previously unselected package libxrender1:arm64.
2025-Aug-18 12:13:28.718197
#9 27.50 Preparing to unpack .../139-libxrender1_1%3a0.9.12-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.50 Unpacking libxrender1:arm64 (1:0.9.12-1) ...
2025-Aug-18 12:13:28.718197
#9 27.54 Selecting previously unselected package libcairo2:arm64.
2025-Aug-18 12:13:28.718197
#9 27.55 Preparing to unpack .../140-libcairo2_1.18.4-1+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.55 Unpacking libcairo2:arm64 (1.18.4-1+b1) ...
2025-Aug-18 12:13:28.718197
#9 27.63 Selecting previously unselected package libcom-err2:arm64.
2025-Aug-18 12:13:28.718197
#9 27.63 Preparing to unpack .../141-libcom-err2_1.47.2-3+b3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.64 Unpacking libcom-err2:arm64 (1.47.2-3+b3) ...
2025-Aug-18 12:13:28.718197
#9 27.68 Selecting previously unselected package libjson-c5:arm64.
2025-Aug-18 12:13:28.718197
#9 27.68 Preparing to unpack .../142-libjson-c5_0.18+ds-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.69 Unpacking libjson-c5:arm64 (0.18+ds-1) ...
2025-Aug-18 12:13:28.718197
#9 27.72 Selecting previously unselected package libcryptsetup12:arm64.
2025-Aug-18 12:13:28.718197
#9 27.73 Preparing to unpack .../143-libcryptsetup12_2%3a2.7.5-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.73 Unpacking libcryptsetup12:arm64 (2:2.7.5-2) ...
2025-Aug-18 12:13:28.718197
#9 27.80 Selecting previously unselected package libkrb5support0:arm64.
2025-Aug-18 12:13:28.718197
#9 27.80 Preparing to unpack .../144-libkrb5support0_1.21.3-5_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.80 Unpacking libkrb5support0:arm64 (1.21.3-5) ...
2025-Aug-18 12:13:28.718197
#9 27.85 Selecting previously unselected package libk5crypto3:arm64.
2025-Aug-18 12:13:28.718197
#9 27.86 Preparing to unpack .../145-libk5crypto3_1.21.3-5_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.86 Unpacking libk5crypto3:arm64 (1.21.3-5) ...
2025-Aug-18 12:13:28.718197
#9 27.91 Selecting previously unselected package libkeyutils1:arm64.
2025-Aug-18 12:13:28.718197
#9 27.91 Preparing to unpack .../146-libkeyutils1_1.6.3-6_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.92 Unpacking libkeyutils1:arm64 (1.6.3-6) ...
2025-Aug-18 12:13:28.718197
#9 27.95 Selecting previously unselected package libkrb5-3:arm64.
2025-Aug-18 12:13:28.718197
#9 27.96 Preparing to unpack .../147-libkrb5-3_1.21.3-5_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 27.96 Unpacking libkrb5-3:arm64 (1.21.3-5) ...
2025-Aug-18 12:13:28.718197
#9 28.02 Selecting previously unselected package libgssapi-krb5-2:arm64.
2025-Aug-18 12:13:28.718197
#9 28.02 Preparing to unpack .../148-libgssapi-krb5-2_1.21.3-5_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 28.03 Unpacking libgssapi-krb5-2:arm64 (1.21.3-5) ...
2025-Aug-18 12:13:28.718197
#9 28.08 Selecting previously unselected package libcups2t64:arm64.
2025-Aug-18 12:13:28.718197
#9 28.09 Preparing to unpack .../149-libcups2t64_2.4.10-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 28.10 Unpacking libcups2t64:arm64 (2.4.10-3) ...
2025-Aug-18 12:13:28.718197
#9 28.15 Selecting previously unselected package libnghttp2-14:arm64.
2025-Aug-18 12:13:28.718197
#9 28.16 Preparing to unpack .../150-libnghttp2-14_1.64.0-1.1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 28.16 Unpacking libnghttp2-14:arm64 (1.64.0-1.1) ...
2025-Aug-18 12:13:28.718197
#9 28.20 Selecting previously unselected package libnghttp3-9:arm64.
2025-Aug-18 12:13:28.718197
#9 28.22 Preparing to unpack .../151-libnghttp3-9_1.8.0-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 28.22 Unpacking libnghttp3-9:arm64 (1.8.0-1) ...
2025-Aug-18 12:13:28.718197
#9 28.26 Selecting previously unselected package libngtcp2-16:arm64.
2025-Aug-18 12:13:28.718197
#9 28.27 Preparing to unpack .../152-libngtcp2-16_1.11.0-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 28.27 Unpacking libngtcp2-16:arm64 (1.11.0-1) ...
2025-Aug-18 12:13:28.718197
#9 28.34 Selecting previously unselected package libngtcp2-crypto-gnutls8:arm64.
2025-Aug-18 12:13:28.718197
#9 28.35 Preparing to unpack .../153-libngtcp2-crypto-gnutls8_1.11.0-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 28.35 Unpacking libngtcp2-crypto-gnutls8:arm64 (1.11.0-1) ...
2025-Aug-18 12:13:28.718197
#9 28.39 Selecting previously unselected package librtmp1:arm64.
2025-Aug-18 12:13:28.718197
#9 28.39 Preparing to unpack .../154-librtmp1_2.4+20151223.gitfa8646d.1-2+b5_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 28.40 Unpacking librtmp1:arm64 (2.4+20151223.gitfa8646d.1-2+b5) ...
2025-Aug-18 12:13:28.718197
#9 28.44 Selecting previously unselected package libssh2-1t64:arm64.
2025-Aug-18 12:13:28.718197
#9 28.44 Preparing to unpack .../155-libssh2-1t64_1.11.1-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 28.44 Unpacking libssh2-1t64:arm64 (1.11.1-1) ...
2025-Aug-18 12:13:28.718197
#9 28.50 Selecting previously unselected package libcurl3t64-gnutls:arm64.
2025-Aug-18 12:13:28.718197
#9 28.50 Preparing to unpack .../156-libcurl3t64-gnutls_8.14.1-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 28.50 Unpacking libcurl3t64-gnutls:arm64 (8.14.1-2) ...
2025-Aug-18 12:13:28.718197
#9 28.57 Selecting previously unselected package libdatrie1:arm64.
2025-Aug-18 12:13:28.718197
#9 28.57 Preparing to unpack .../157-libdatrie1_0.2.13-3+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 28.57 Unpacking libdatrie1:arm64 (0.2.13-3+b1) ...
2025-Aug-18 12:13:28.718197
#9 28.62 Selecting previously unselected package libdeflate0:arm64.
2025-Aug-18 12:13:28.718197
#9 28.62 Preparing to unpack .../158-libdeflate0_1.23-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 28.62 Unpacking libdeflate0:arm64 (1.23-2) ...
2025-Aug-18 12:13:28.718197
#9 28.66 Selecting previously unselected package libdrm-common.
2025-Aug-18 12:13:28.718197
#9 28.66 Preparing to unpack .../159-libdrm-common_2.4.124-2_all.deb ...
2025-Aug-18 12:13:28.718197
#9 28.67 Unpacking libdrm-common (2.4.124-2) ...
2025-Aug-18 12:13:28.718197
#9 28.71 Selecting previously unselected package libdrm2:arm64.
2025-Aug-18 12:13:28.718197
#9 28.72 Preparing to unpack .../160-libdrm2_2.4.124-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 28.72 Unpacking libdrm2:arm64 (2.4.124-2) ...
2025-Aug-18 12:13:28.718197
#9 28.76 Selecting previously unselected package libdrm-amdgpu1:arm64.
2025-Aug-18 12:13:28.718197
#9 28.77 Preparing to unpack .../161-libdrm-amdgpu1_2.4.124-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 28.77 Unpacking libdrm-amdgpu1:arm64 (2.4.124-2) ...
2025-Aug-18 12:13:28.718197
#9 28.81 Selecting previously unselected package libedit2:arm64.
2025-Aug-18 12:13:28.718197
#9 28.82 Preparing to unpack .../162-libedit2_3.1-20250104-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 28.82 Unpacking libedit2:arm64 (3.1-20250104-1) ...
2025-Aug-18 12:13:28.718197
#9 28.87 Selecting previously unselected package libwayland-server0:arm64.
2025-Aug-18 12:13:28.718197
#9 28.88 Preparing to unpack .../163-libwayland-server0_1.23.1-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 28.88 Unpacking libwayland-server0:arm64 (1.23.1-3) ...
2025-Aug-18 12:13:28.718197
#9 28.92 Selecting previously unselected package libelf1t64:arm64.
2025-Aug-18 12:13:28.718197
#9 28.93 Preparing to unpack .../164-libelf1t64_0.192-4_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 28.93 Unpacking libelf1t64:arm64 (0.192-4) ...
2025-Aug-18 12:13:28.718197
#9 28.99 Selecting previously unselected package libxml2:arm64.
2025-Aug-18 12:13:28.718197
#9 29.00 Preparing to unpack .../165-libxml2_2.12.7+dfsg+really2.9.14-2.1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 29.00 Unpacking libxml2:arm64 (2.12.7+dfsg+really2.9.14-2.1) ...
2025-Aug-18 12:13:28.718197
#9 29.08 Selecting previously unselected package libz3-4:arm64.
2025-Aug-18 12:13:28.718197
#9 29.08 Preparing to unpack .../166-libz3-4_4.13.3-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 29.09 Unpacking libz3-4:arm64 (4.13.3-1) ...
2025-Aug-18 12:13:28.718197
#9 29.97 Selecting previously unselected package libllvm19:arm64.
2025-Aug-18 12:13:28.718197
#9 29.97 Preparing to unpack .../167-libllvm19_1%3a19.1.7-3+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 29.97 Unpacking libllvm19:arm64 (1:19.1.7-3+b1) ...
2025-Aug-18 12:13:28.718197
#9 31.51 Selecting previously unselected package libsensors-config.
2025-Aug-18 12:13:28.718197
#9 31.51 Preparing to unpack .../168-libsensors-config_1%3a3.6.2-2_all.deb ...
2025-Aug-18 12:13:28.718197
#9 31.52 Unpacking libsensors-config (1:3.6.2-2) ...
2025-Aug-18 12:13:28.718197
#9 31.55 Selecting previously unselected package libsensors5:arm64.
2025-Aug-18 12:13:28.718197
#9 31.55 Preparing to unpack .../169-libsensors5_1%3a3.6.2-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 31.55 Unpacking libsensors5:arm64 (1:3.6.2-2) ...
2025-Aug-18 12:13:28.718197
#9 31.59 Selecting previously unselected package libx11-xcb1:arm64.
2025-Aug-18 12:13:28.718197
#9 31.60 Preparing to unpack .../170-libx11-xcb1_2%3a1.8.12-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 31.60 Unpacking libx11-xcb1:arm64 (2:1.8.12-1) ...
2025-Aug-18 12:13:28.718197
#9 31.66 Selecting previously unselected package libxcb-dri3-0:arm64.
2025-Aug-18 12:13:28.718197
#9 31.66 Preparing to unpack .../171-libxcb-dri3-0_1.17.0-2+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 31.67 Unpacking libxcb-dri3-0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 31.72 Selecting previously unselected package libxcb-present0:arm64.
2025-Aug-18 12:13:28.718197
#9 31.72 Preparing to unpack .../172-libxcb-present0_1.17.0-2+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 31.73 Unpacking libxcb-present0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 31.77 Selecting previously unselected package libxcb-randr0:arm64.
2025-Aug-18 12:13:28.718197
#9 31.78 Preparing to unpack .../173-libxcb-randr0_1.17.0-2+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 31.78 Unpacking libxcb-randr0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 31.82 Selecting previously unselected package libxcb-sync1:arm64.
2025-Aug-18 12:13:28.718197
#9 31.83 Preparing to unpack .../174-libxcb-sync1_1.17.0-2+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 31.83 Unpacking libxcb-sync1:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 31.88 Selecting previously unselected package libxcb-xfixes0:arm64.
2025-Aug-18 12:13:28.718197
#9 31.88 Preparing to unpack .../175-libxcb-xfixes0_1.17.0-2+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 31.89 Unpacking libxcb-xfixes0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 31.93 Selecting previously unselected package libxshmfence1:arm64.
2025-Aug-18 12:13:28.718197
#9 31.93 Preparing to unpack .../176-libxshmfence1_1.3.3-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 31.93 Unpacking libxshmfence1:arm64 (1.3.3-1) ...
2025-Aug-18 12:13:28.718197
#9 31.97 Selecting previously unselected package mesa-libgallium:arm64.
2025-Aug-18 12:13:28.718197
#9 31.97 Preparing to unpack .../177-mesa-libgallium_25.0.7-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 31.98 Unpacking mesa-libgallium:arm64 (25.0.7-2) ...
2025-Aug-18 12:13:28.718197
#9 32.57 Selecting previously unselected package libgbm1:arm64.
2025-Aug-18 12:13:28.718197
#9 32.57 Preparing to unpack .../178-libgbm1_25.0.7-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 32.58 Unpacking libgbm1:arm64 (25.0.7-2) ...
2025-Aug-18 12:13:28.718197
#9 32.62 Selecting previously unselected package libwayland-client0:arm64.
2025-Aug-18 12:13:28.718197
#9 32.62 Preparing to unpack .../179-libwayland-client0_1.23.1-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 32.62 Unpacking libwayland-client0:arm64 (1.23.1-3) ...
2025-Aug-18 12:13:28.718197
#9 32.66 Selecting previously unselected package libegl-mesa0:arm64.
2025-Aug-18 12:13:28.718197
#9 32.67 Preparing to unpack .../180-libegl-mesa0_25.0.7-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 32.67 Unpacking libegl-mesa0:arm64 (25.0.7-2) ...
2025-Aug-18 12:13:28.718197
#9 32.71 Selecting previously unselected package libfile-fcntllock-perl.
2025-Aug-18 12:13:28.718197
#9 32.71 Preparing to unpack .../181-libfile-fcntllock-perl_0.22-4+b4_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 32.72 Unpacking libfile-fcntllock-perl (0.22-4+b4) ...
2025-Aug-18 12:13:28.718197
#9 32.76 Selecting previously unselected package libfribidi0:arm64.
2025-Aug-18 12:13:28.718197
#9 32.77 Preparing to unpack .../182-libfribidi0_1.0.16-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 32.77 Unpacking libfribidi0:arm64 (1.0.16-1) ...
2025-Aug-18 12:13:28.718197
#9 32.81 Selecting previously unselected package libglib2.0-data.
2025-Aug-18 12:13:28.718197
#9 32.81 Preparing to unpack .../183-libglib2.0-data_2.84.3-1_all.deb ...
2025-Aug-18 12:13:28.718197
#9 32.81 Unpacking libglib2.0-data (2.84.3-1) ...
2025-Aug-18 12:13:28.718197
#9 32.96 Selecting previously unselected package libglvnd0:arm64.
2025-Aug-18 12:13:28.718197
#9 32.97 Preparing to unpack .../184-libglvnd0_1.7.0-1+b2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 32.97 Unpacking libglvnd0:arm64 (1.7.0-1+b2) ...
2025-Aug-18 12:13:28.718197
#9 33.02 Selecting previously unselected package libgpg-error-l10n.
2025-Aug-18 12:13:28.718197
#9 33.02 Preparing to unpack .../185-libgpg-error-l10n_1.51-4_all.deb ...
2025-Aug-18 12:13:28.718197
#9 33.03 Unpacking libgpg-error-l10n (1.51-4) ...
2025-Aug-18 12:13:28.718197
#9 33.07 Selecting previously unselected package libgpgme11t64:arm64.
2025-Aug-18 12:13:28.718197
#9 33.07 Preparing to unpack .../186-libgpgme11t64_1.24.2-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 33.08 Unpacking libgpgme11t64:arm64 (1.24.2-3) ...
2025-Aug-18 12:13:28.718197
#9 33.12 Selecting previously unselected package libgpgmepp6t64:arm64.
2025-Aug-18 12:13:28.718197
#9 33.13 Preparing to unpack .../187-libgpgmepp6t64_1.24.2-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 33.13 Unpacking libgpgmepp6t64:arm64 (1.24.2-3) ...
2025-Aug-18 12:13:28.718197
#9 33.18 Selecting previously unselected package libgraphite2-3:arm64.
2025-Aug-18 12:13:28.718197
#9 33.18 Preparing to unpack .../188-libgraphite2-3_1.3.14-2+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 33.19 Unpacking libgraphite2-3:arm64 (1.3.14-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 33.23 Selecting previously unselected package libharfbuzz0b:arm64.
2025-Aug-18 12:13:28.718197
#9 33.23 Preparing to unpack .../189-libharfbuzz0b_10.2.0-1+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 33.23 Unpacking libharfbuzz0b:arm64 (10.2.0-1+b1) ...
2025-Aug-18 12:13:28.718197
#9 33.30 Selecting previously unselected package libjbig0:arm64.
2025-Aug-18 12:13:28.718197
#9 33.30 Preparing to unpack .../190-libjbig0_2.1-6.1+b2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 33.31 Unpacking libjbig0:arm64 (2.1-6.1+b2) ...
2025-Aug-18 12:13:28.718197
#9 33.35 Selecting previously unselected package libjpeg62-turbo:arm64.
2025-Aug-18 12:13:28.718197
#9 33.35 Preparing to unpack .../191-libjpeg62-turbo_1%3a2.1.5-4_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 33.36 Unpacking libjpeg62-turbo:arm64 (1:2.1.5-4) ...
2025-Aug-18 12:13:28.718197
#9 33.42 Selecting previously unselected package libkmod2:arm64.
2025-Aug-18 12:13:28.718197
#9 33.42 Preparing to unpack .../192-libkmod2_34.2-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 33.43 Unpacking libkmod2:arm64 (34.2-2) ...
2025-Aug-18 12:13:28.718197
#9 33.46 Selecting previously unselected package liblcms2-2:arm64.
2025-Aug-18 12:13:28.718197
#9 33.47 Preparing to unpack .../193-liblcms2-2_2.16-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 33.48 Unpacking liblcms2-2:arm64 (2.16-2) ...
2025-Aug-18 12:13:28.718197
#9 33.55 Selecting previously unselected package libldap-common.
2025-Aug-18 12:13:28.718197
#9 33.56 Preparing to unpack .../194-libldap-common_2.6.10+dfsg-1_all.deb ...
2025-Aug-18 12:13:28.718197
#9 33.56 Unpacking libldap-common (2.6.10+dfsg-1) ...
2025-Aug-18 12:13:28.718197
#9 33.61 Selecting previously unselected package liblerc4:arm64.
2025-Aug-18 12:13:28.718197
#9 33.62 Preparing to unpack .../195-liblerc4_4.0.0+ds-5_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 33.62 Unpacking liblerc4:arm64 (4.0.0+ds-5) ...
2025-Aug-18 12:13:28.718197
#9 33.67 Selecting previously unselected package libnspr4:arm64.
2025-Aug-18 12:13:28.718197
#9 33.67 Preparing to unpack .../196-libnspr4_2%3a4.36-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 33.67 Unpacking libnspr4:arm64 (2:4.36-1) ...
2025-Aug-18 12:13:28.718197
#9 33.72 Selecting previously unselected package libnss3:arm64.
2025-Aug-18 12:13:28.718197
#9 33.72 Preparing to unpack .../197-libnss3_2%3a3.110-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 33.73 Unpacking libnss3:arm64 (2:3.110-1) ...
2025-Aug-18 12:13:28.718197
#9 33.86 Selecting previously unselected package libopenjp2-7:arm64.
2025-Aug-18 12:13:28.718197
#9 33.87 Preparing to unpack .../198-libopenjp2-7_2.5.3-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 33.87 Unpacking libopenjp2-7:arm64 (2.5.3-2) ...
2025-Aug-18 12:13:28.718197
#9 33.92 Selecting previously unselected package libthai-data.
2025-Aug-18 12:13:28.718197
#9 33.92 Preparing to unpack .../199-libthai-data_0.1.29-2_all.deb ...
2025-Aug-18 12:13:28.718197
#9 33.92 Unpacking libthai-data (0.1.29-2) ...
2025-Aug-18 12:13:28.718197
#9 33.99 Selecting previously unselected package libthai0:arm64.
2025-Aug-18 12:13:28.718197
#9 34.00 Preparing to unpack .../200-libthai0_0.1.29-2+b1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 34.00 Unpacking libthai0:arm64 (0.1.29-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 34.05 Selecting previously unselected package libpango-1.0-0:arm64.
2025-Aug-18 12:13:28.718197
#9 34.05 Preparing to unpack .../201-libpango-1.0-0_1.56.3-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 34.05 Unpacking libpango-1.0-0:arm64 (1.56.3-1) ...
2025-Aug-18 12:13:28.718197
#9 34.13 Selecting previously unselected package libpangoft2-1.0-0:arm64.
2025-Aug-18 12:13:28.718197
#9 34.13 Preparing to unpack .../202-libpangoft2-1.0-0_1.56.3-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 34.14 Unpacking libpangoft2-1.0-0:arm64 (1.56.3-1) ...
2025-Aug-18 12:13:28.718197
#9 34.19 Selecting previously unselected package libpangocairo-1.0-0:arm64.
2025-Aug-18 12:13:28.718197
#9 34.20 Preparing to unpack .../203-libpangocairo-1.0-0_1.56.3-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 34.20 Unpacking libpangocairo-1.0-0:arm64 (1.56.3-1) ...
2025-Aug-18 12:13:28.718197
#9 34.25 Selecting previously unselected package libsharpyuv0:arm64.
2025-Aug-18 12:13:28.718197
#9 34.25 Preparing to unpack .../204-libsharpyuv0_1.5.0-0.1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 34.26 Unpacking libsharpyuv0:arm64 (1.5.0-0.1) ...
2025-Aug-18 12:13:28.718197
#9 34.30 Selecting previously unselected package libwebp7:arm64.
2025-Aug-18 12:13:28.718197
#9 34.31 Preparing to unpack .../205-libwebp7_1.5.0-0.1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 34.31 Unpacking libwebp7:arm64 (1.5.0-0.1) ...
2025-Aug-18 12:13:28.718197
#9 34.40 Selecting previously unselected package libtiff6:arm64.
2025-Aug-18 12:13:28.718197
#9 34.42 Preparing to unpack .../206-libtiff6_4.7.0-3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 34.43 Unpacking libtiff6:arm64 (4.7.0-3) ...
2025-Aug-18 12:13:28.718197
#9 34.53 Selecting previously unselected package libpoppler147:arm64.
2025-Aug-18 12:13:28.718197
#9 34.54 Preparing to unpack .../207-libpoppler147_25.03.0-5_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 34.55 Unpacking libpoppler147:arm64 (25.03.0-5) ...
2025-Aug-18 12:13:28.718197
#9 34.70 Selecting previously unselected package libpoppler-cpp2:arm64.
2025-Aug-18 12:13:28.718197
#9 34.71 Preparing to unpack .../208-libpoppler-cpp2_25.03.0-5_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 34.71 Unpacking libpoppler-cpp2:arm64 (25.03.0-5) ...
2025-Aug-18 12:13:28.718197
#9 34.77 Selecting previously unselected package libpoppler-dev:arm64.
2025-Aug-18 12:13:28.718197
#9 34.78 Preparing to unpack .../209-libpoppler-dev_25.03.0-5_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 34.79 Unpacking libpoppler-dev:arm64 (25.03.0-5) ...
2025-Aug-18 12:13:28.718197
#9 34.84 Selecting previously unselected package libpoppler-cpp-dev:arm64.
2025-Aug-18 12:13:28.718197
#9 34.84 Preparing to unpack .../210-libpoppler-cpp-dev_25.03.0-5_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 34.85 Unpacking libpoppler-cpp-dev:arm64 (25.03.0-5) ...
2025-Aug-18 12:13:28.718197
#9 34.90 Selecting previously unselected package libsasl2-modules:arm64.
2025-Aug-18 12:13:28.718197
#9 34.91 Preparing to unpack .../211-libsasl2-modules_2.1.28+dfsg1-9_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 34.92 Unpacking libsasl2-modules:arm64 (2.1.28+dfsg1-9) ...
2025-Aug-18 12:13:28.718197
#9 34.97 Selecting previously unselected package libxcomposite1:arm64.
2025-Aug-18 12:13:28.718197
#9 34.97 Preparing to unpack .../212-libxcomposite1_1%3a0.4.6-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 34.98 Unpacking libxcomposite1:arm64 (1:0.4.6-1) ...
2025-Aug-18 12:13:28.718197
#9 35.03 Selecting previously unselected package libxfixes3:arm64.
2025-Aug-18 12:13:28.718197
#9 35.03 Preparing to unpack .../213-libxfixes3_1%3a6.0.0-2+b4_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 35.04 Unpacking libxfixes3:arm64 (1:6.0.0-2+b4) ...
2025-Aug-18 12:13:28.718197
#9 35.08 Selecting previously unselected package libxcursor1:arm64.
2025-Aug-18 12:13:28.718197
#9 35.09 Preparing to unpack .../214-libxcursor1_1%3a1.2.3-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 35.09 Unpacking libxcursor1:arm64 (1:1.2.3-1) ...
2025-Aug-18 12:13:28.718197
#9 35.14 Selecting previously unselected package libxdamage1:arm64.
2025-Aug-18 12:13:28.718197
#9 35.15 Preparing to unpack .../215-libxdamage1_1%3a1.1.6-1+b2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 35.15 Unpacking libxdamage1:arm64 (1:1.1.6-1+b2) ...
2025-Aug-18 12:13:28.718197
#9 35.19 Selecting previously unselected package libxrandr2:arm64.
2025-Aug-18 12:13:28.718197
#9 35.19 Preparing to unpack .../216-libxrandr2_2%3a1.5.4-1+b3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 35.20 Unpacking libxrandr2:arm64 (2:1.5.4-1+b3) ...
2025-Aug-18 12:13:28.718197
#9 35.25 Selecting previously unselected package manpages-dev.
2025-Aug-18 12:13:28.718197
#9 35.25 Preparing to unpack .../217-manpages-dev_6.9.1-1_all.deb ...
2025-Aug-18 12:13:28.718197
#9 35.26 Unpacking manpages-dev (6.9.1-1) ...
2025-Aug-18 12:13:28.718197
#9 35.55 Selecting previously unselected package poppler-utils.
2025-Aug-18 12:13:28.718197
#9 35.55 Preparing to unpack .../218-poppler-utils_25.03.0-5_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 35.56 Unpacking poppler-utils (25.03.0-5) ...
2025-Aug-18 12:13:28.718197
#9 35.67 Selecting previously unselected package psmisc.
2025-Aug-18 12:13:28.718197
#9 35.67 Preparing to unpack .../219-psmisc_23.7-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 35.68 Unpacking psmisc (23.7-2) ...
2025-Aug-18 12:13:28.718197
#9 35.75 Selecting previously unselected package publicsuffix.
2025-Aug-18 12:13:28.718197
#9 35.76 Preparing to unpack .../220-publicsuffix_20250328.1952-0.1_all.deb ...
2025-Aug-18 12:13:28.718197
#9 35.76 Unpacking publicsuffix (20250328.1952-0.1) ...
2025-Aug-18 12:13:28.718197
#9 35.82 Selecting previously unselected package shared-mime-info.
2025-Aug-18 12:13:28.718197
#9 35.83 Preparing to unpack .../221-shared-mime-info_2.4-5+b2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 35.83 Unpacking shared-mime-info (2.4-5+b2) ...
2025-Aug-18 12:13:28.718197
#9 35.92 Selecting previously unselected package systemd-cryptsetup.
2025-Aug-18 12:13:28.718197
#9 35.92 Preparing to unpack .../222-systemd-cryptsetup_257.7-1_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 35.93 Unpacking systemd-cryptsetup (257.7-1) ...
2025-Aug-18 12:13:28.718197
#9 36.02 Selecting previously unselected package xdg-user-dirs.
2025-Aug-18 12:13:28.718197
#9 36.02 Preparing to unpack .../223-xdg-user-dirs_0.18-2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 36.03 Unpacking xdg-user-dirs (0.18-2) ...
2025-Aug-18 12:13:28.718197
#9 36.08 Selecting previously unselected package gnupg-utils.
2025-Aug-18 12:13:28.718197
#9 36.08 Preparing to unpack .../224-gnupg-utils_2.4.7-21+b3_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 36.09 Unpacking gnupg-utils (2.4.7-21+b3) ...
2025-Aug-18 12:13:28.718197
#9 36.13 Selecting previously unselected package libegl1:arm64.
2025-Aug-18 12:13:28.718197
#9 36.14 Preparing to unpack .../225-libegl1_1.7.0-1+b2_arm64.deb ...
2025-Aug-18 12:13:28.718197
#9 36.14 Unpacking libegl1:arm64 (1.7.0-1+b2) ...
2025-Aug-18 12:13:28.718197
#9 36.19 Setting up libexpat1:arm64 (2.7.1-2) ...
2025-Aug-18 12:13:28.718197
#9 36.21 Setting up libgraphite2-3:arm64 (1.3.14-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 36.21 Setting up liblcms2-2:arm64 (2.16-2) ...
2025-Aug-18 12:13:28.718197
#9 36.22 Setting up libpixman-1-0:arm64 (0.44.0-3) ...
2025-Aug-18 12:13:28.718197
#9 36.22 Setting up libsharpyuv0:arm64 (1.5.0-0.1) ...
2025-Aug-18 12:13:28.718197
#9 36.23 Setting up libwayland-server0:arm64 (1.23.1-3) ...
2025-Aug-18 12:13:28.718197
#9 36.24 Setting up systemd-sysv (257.7-1) ...
2025-Aug-18 12:13:28.718197
#9 36.28 Setting up libxau6:arm64 (1:1.0.11-1) ...
2025-Aug-18 12:13:28.718197
#9 36.29 Setting up libxdmcp6:arm64 (1:1.1.5-1) ...
2025-Aug-18 12:13:28.718197
#9 36.30 Setting up libnpth0t64:arm64 (1.8-3) ...
2025-Aug-18 12:13:28.718197
#9 36.31 Setting up libkeyutils1:arm64 (1.6.3-6) ...
2025-Aug-18 12:13:28.718197
#9 36.32 Setting up libxcb1:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 36.33 Setting up libxcb-xfixes0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 36.34 Setting up liblerc4:arm64 (4.0.0+ds-5) ...
2025-Aug-18 12:13:28.718197
#9 36.34 Setting up libgpg-error0:arm64 (1.51-4) ...
2025-Aug-18 12:13:28.718197
#9 36.36 Setting up libdatrie1:arm64 (0.2.13-3+b1) ...
2025-Aug-18 12:13:28.718197
#9 36.36 Setting up libgdbm-compat4t64:arm64 (1.24-2) ...
2025-Aug-18 12:13:28.718197
#9 36.37 Setting up xdg-user-dirs (0.18-2) ...
2025-Aug-18 12:13:28.718197
#9 36.39 Setting up psmisc (23.7-2) ...
2025-Aug-18 12:13:28.718197
#9 36.40 Setting up libxcb-render0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 36.41 Setting up manpages (6.9.1-1) ...
2025-Aug-18 12:13:28.718197
#9 36.41 Setting up libglvnd0:arm64 (1.7.0-1+b2) ...
2025-Aug-18 12:13:28.718197
#9 36.42 Setting up libbrotli1:arm64 (1.1.0-2+b7) ...
2025-Aug-18 12:13:28.718197
#9 36.43 Setting up libedit2:arm64 (3.1-20250104-1) ...
2025-Aug-18 12:13:28.718197
#9 36.43 Setting up libsasl2-modules:arm64 (2.1.28+dfsg1-9) ...
2025-Aug-18 12:13:28.718197
#9 36.45 Setting up binutils-common:arm64 (2.44-3) ...
2025-Aug-18 12:13:28.718197
#9 36.46 Setting up x11-common (1:7.7+24) ...
2025-Aug-18 12:13:28.718197
#9 36.66 debconf: unable to initialize frontend: Dialog
2025-Aug-18 12:13:28.718197
#9 36.66 debconf: (TERM is not set, so the dialog frontend is not usable.)
2025-Aug-18 12:13:28.718197
#9 36.66 debconf: falling back to frontend: Readline
2025-Aug-18 12:13:28.718197
#9 36.67 debconf: unable to initialize frontend: Readline
2025-Aug-18 12:13:28.718197
#9 36.67 debconf: (This frontend requires a controlling tty.)
2025-Aug-18 12:13:28.718197
#9 36.67 debconf: falling back to frontend: Teletype
2025-Aug-18 12:13:28.718197
#9 36.68 debconf: unable to initialize frontend: Teletype
2025-Aug-18 12:13:28.718197
#9 36.68 debconf: (This frontend requires a controlling tty.)
2025-Aug-18 12:13:28.718197
#9 36.68 debconf: falling back to frontend: Noninteractive
2025-Aug-18 12:13:28.718197
#9 36.71 invoke-rc.d: could not determine current runlevel
2025-Aug-18 12:13:28.718197
#9 36.72 invoke-rc.d: policy-rc.d denied execution of start.
2025-Aug-18 12:13:28.718197
#9 36.73 Setting up libsensors-config (1:3.6.2-2) ...
2025-Aug-18 12:13:28.718197
#9 36.74 Setting up libnghttp2-14:arm64 (1.64.0-1.1) ...
2025-Aug-18 12:13:28.718197
#9 36.75 Setting up libdeflate0:arm64 (1.23-2) ...
2025-Aug-18 12:13:28.718197
#9 36.76 Setting up linux-libc-dev (6.12.41-1) ...
2025-Aug-18 12:13:28.718197
#9 36.77 Setting up libctf-nobfd0:arm64 (2.44-3) ...
2025-Aug-18 12:13:28.718197
#9 36.78 Setting up libgcrypt20:arm64 (1.11.0-7) ...
2025-Aug-18 12:13:28.718197
#9 36.79 Setting up libnss-systemd:arm64 (257.7-1) ...
2025-Aug-18 12:13:28.718197
#9 36.81 Setting up krb5-locales (1.21.3-5) ...
2025-Aug-18 12:13:28.718197
#9 36.82 Setting up libxcb-shm0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 36.82 Setting up libcom-err2:arm64 (1.47.2-3+b3) ...
2025-Aug-18 12:13:28.718197
#9 36.83 Setting up libgomp1:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 36.84 Setting up bzip2 (1.0.8-6) ...
2025-Aug-18 12:13:28.718197
#9 36.84 Setting up libldap-common (2.6.10+dfsg-1) ...
2025-Aug-18 12:13:28.718197
#9 36.85 Setting up libjbig0:arm64 (2.1-6.1+b2) ...
2025-Aug-18 12:13:28.718197
#9 36.86 Setting up libsframe1:arm64 (2.44-3) ...
2025-Aug-18 12:13:28.718197
#9 36.87 Setting up libfakeroot:arm64 (********-1) ...
2025-Aug-18 12:13:28.718197
#9 36.88 Setting up libelf1t64:arm64 (0.192-4) ...
2025-Aug-18 12:13:28.718197
#9 36.89 Setting up libjansson4:arm64 (2.14-2+b3) ...
2025-Aug-18 12:13:28.718197
#9 36.89 Setting up poppler-data (0.4.12-1) ...
2025-Aug-18 12:13:28.718197
#9 36.92 Setting up libkrb5support0:arm64 (1.21.3-5) ...
2025-Aug-18 12:13:28.718197
#9 36.93 Setting up libsasl2-modules-db:arm64 (2.1.28+dfsg1-9) ...
2025-Aug-18 12:13:28.718197
#9 36.94 Setting up fakeroot (********-1) ...
2025-Aug-18 12:13:28.718197
#9 36.95 update-alternatives: using /usr/bin/fakeroot-sysv to provide /usr/bin/fakeroot (fakeroot) in auto mode
2025-Aug-18 12:13:28.718197
#9 36.95 update-alternatives: warning: skip creation of /usr/share/man/man1/fakeroot.1.gz because associated file /usr/share/man/man1/fakeroot-sysv.1.gz (of link group fakeroot) doesn't exist
2025-Aug-18 12:13:28.718197
#9 36.95 update-alternatives: warning: skip creation of /usr/share/man/man1/faked.1.gz because associated file /usr/share/man/man1/faked-sysv.1.gz (of link group fakeroot) doesn't exist
2025-Aug-18 12:13:28.718197
#9 36.95 update-alternatives: warning: skip creation of /usr/share/man/es/man1/fakeroot.1.gz because associated file /usr/share/man/es/man1/fakeroot-sysv.1.gz (of link group fakeroot) doesn't exist
2025-Aug-18 12:13:28.718197
#9 36.96 update-alternatives: warning: skip creation of /usr/share/man/es/man1/faked.1.gz because associated file /usr/share/man/es/man1/faked-sysv.1.gz (of link group fakeroot) doesn't exist
2025-Aug-18 12:13:28.718197
#9 36.96 update-alternatives: warning: skip creation of /usr/share/man/fr/man1/fakeroot.1.gz because associated file /usr/share/man/fr/man1/fakeroot-sysv.1.gz (of link group fakeroot) doesn't exist
2025-Aug-18 12:13:28.718197
#9 36.96 update-alternatives: warning: skip creation of /usr/share/man/fr/man1/faked.1.gz because associated file /usr/share/man/fr/man1/faked-sysv.1.gz (of link group fakeroot) doesn't exist
2025-Aug-18 12:13:28.718197
#9 36.96 update-alternatives: warning: skip creation of /usr/share/man/sv/man1/fakeroot.1.gz because associated file /usr/share/man/sv/man1/fakeroot-sysv.1.gz (of link group fakeroot) doesn't exist
2025-Aug-18 12:13:28.718197
#9 36.96 update-alternatives: warning: skip creation of /usr/share/man/sv/man1/faked.1.gz because associated file /usr/share/man/sv/man1/faked-sysv.1.gz (of link group fakeroot) doesn't exist
2025-Aug-18 12:13:28.718197
#9 36.96 Setting up libxcb-present0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 36.97 Setting up libasound2-data (1.2.14-1) ...
2025-Aug-18 12:13:28.718197
#9 36.98 Setting up libz3-4:arm64 (4.13.3-1) ...
2025-Aug-18 12:13:28.718197
#9 36.99 Setting up libglib2.0-data (2.84.3-1) ...
2025-Aug-18 12:13:28.718197
#9 37.00 Setting up rpcsvc-proto (1.4.3-1+b1) ...
2025-Aug-18 12:13:28.718197
#9 37.00 Setting up linux-sysctl-defaults (4.12) ...
2025-Aug-18 12:13:28.718197
#9 37.01 Setting up libasound2t64:arm64 (1.2.14-1) ...
2025-Aug-18 12:13:28.718197
#9 37.02 Setting up libjpeg62-turbo:arm64 (1:2.1.5-4) ...
2025-Aug-18 12:13:28.718197
#9 37.03 Setting up libx11-data (2:1.8.12-1) ...
2025-Aug-18 12:13:28.718197
#9 37.03 Setting up make (4.4.1-2) ...
2025-Aug-18 12:13:28.718197
#9 37.04 Setting up libmpfr6:arm64 (4.2.2-1) ...
2025-Aug-18 12:13:28.718197
#9 37.05 Setting up libnspr4:arm64 (2:4.36-1) ...
2025-Aug-18 12:13:28.718197
#9 37.06 Setting up gnupg-l10n (2.4.7-21) ...
2025-Aug-18 12:13:28.718197
#9 37.06 Setting up libxcb-sync1:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 37.07 Setting up libavahi-common-data:arm64 (0.8-16) ...
2025-Aug-18 12:13:28.718197
#9 37.08 Setting up libdbus-1-3:arm64 (1.16.2-2) ...
2025-Aug-18 12:13:28.718197
#9 37.09 Setting up xz-utils (5.8.1-1) ...
2025-Aug-18 12:13:28.718197
#9 37.09 update-alternatives: using /usr/bin/xz to provide /usr/bin/lzma (lzma) in auto mode
2025-Aug-18 12:13:28.718197
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzma.1.gz because associated file /usr/share/man/man1/xz.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:28.718197
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/unlzma.1.gz because associated file /usr/share/man/man1/unxz.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:28.718197
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzcat.1.gz because associated file /usr/share/man/man1/xzcat.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:28.718197
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzmore.1.gz because associated file /usr/share/man/man1/xzmore.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:28.718197
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzless.1.gz because associated file /usr/share/man/man1/xzless.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:28.718197
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzdiff.1.gz because associated file /usr/share/man/man1/xzdiff.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:28.718197
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzcmp.1.gz because associated file /usr/share/man/man1/xzcmp.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:28.718197
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzgrep.1.gz because associated file /usr/share/man/man1/xzgrep.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:28.718197
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzegrep.1.gz because associated file /usr/share/man/man1/xzegrep.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:28.718197
#9 37.10 update-alternatives: warning: skip creation of /usr/share/man/man1/lzfgrep.1.gz because associated file /usr/share/man/man1/xzfgrep.1.gz (of link group lzma) doesn't exist
2025-Aug-18 12:13:28.718197
#9 37.10 Setting up libfribidi0:arm64 (1.0.16-1) ...
2025-Aug-18 12:13:28.718197
#9 37.11 Setting up libp11-kit0:arm64 (0.25.5-3) ...
2025-Aug-18 12:13:28.718197
#9 37.12 Setting up libproc2-0:arm64 (2:4.0.4-9) ...
2025-Aug-18 12:13:28.718197
#9 37.13 Setting up libunistring5:arm64 (1.3-2) ...
2025-Aug-18 12:13:28.718197
#9 37.13 Setting up fonts-dejavu-mono (2.37-8) ...
2025-Aug-18 12:13:28.718197
#9 37.15 Setting up libpng16-16t64:arm64 (1.6.48-1) ...
2025-Aug-18 12:13:28.718197
#9 37.16 Setting up libmpc3:arm64 (1.3.1-1+b3) ...
2025-Aug-18 12:13:28.718197
#9 37.17 Setting up systemd-timesyncd (257.7-1) ...
2025-Aug-18 12:13:28.718197
#9 37.20 Creating group 'systemd-timesync' with GID 997.
2025-Aug-18 12:13:28.718197
#9 37.20 Creating user 'systemd-timesync' (systemd Time Synchronization) with UID 997 and GID 997.
2025-Aug-18 12:13:28.718197
#9 37.50 Created symlink '/etc/systemd/system/dbus-org.freedesktop.timesync1.service' → '/usr/lib/systemd/system/systemd-timesyncd.service'.
2025-Aug-18 12:13:28.718197
#9 37.50 Created symlink '/etc/systemd/system/sysinit.target.wants/systemd-timesyncd.service' → '/usr/lib/systemd/system/systemd-timesyncd.service'.
2025-Aug-18 12:13:28.718197
#9 37.52 Setting up libatomic1:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 37.53 Setting up patch (2.8-2) ...
2025-Aug-18 12:13:28.718197
#9 37.54 Setting up fonts-dejavu-core (2.37-8) ...
2025-Aug-18 12:13:28.718197
#9 37.59 Setting up libsensors5:arm64 (1:3.6.2-2) ...
2025-Aug-18 12:13:28.718197
#9 37.59 Setting up libk5crypto3:arm64 (1.21.3-5) ...
2025-Aug-18 12:13:28.718197
#9 37.60 Setting up libsasl2-2:arm64 (2.1.28+dfsg1-9) ...
2025-Aug-18 12:13:28.718197
#9 37.61 Setting up libnghttp3-9:arm64 (1.8.0-1) ...
2025-Aug-18 12:13:28.718197
#9 37.62 Setting up libwebp7:arm64 (1.5.0-0.1) ...
2025-Aug-18 12:13:28.718197
#9 37.62 Setting up libubsan1:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 37.63 Setting up alsa-topology-conf (1.2.5.1-3) ...
2025-Aug-18 12:13:28.718197
#9 37.64 Setting up perl-modules-5.40 (5.40.1-6) ...
2025-Aug-18 12:13:28.718197
#9 37.64 Setting up libxshmfence1:arm64 (1.3.3-1) ...
2025-Aug-18 12:13:28.718197
#9 37.65 Setting up libhwasan0:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 37.66 Setting up at-spi2-common (2.56.2-1) ...
2025-Aug-18 12:13:28.718197
#9 37.67 Setting up gpgv (2.4.7-21+b3) ...
2025-Aug-18 12:13:28.718197
#9 37.67 Setting up libcrypt-dev:arm64 (1:4.4.38-1) ...
2025-Aug-18 12:13:28.718197
#9 37.69 Setting up libtiff6:arm64 (4.7.0-3) ...
2025-Aug-18 12:13:28.718197
#9 37.70 Setting up libxcb-randr0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 37.70 Setting up dbus-session-bus-common (1.16.2-2) ...
2025-Aug-18 12:13:28.718197
#9 37.71 Setting up libasan8:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 37.72 Setting up libassuan9:arm64 (3.0.2-2) ...
2025-Aug-18 12:13:28.718197
#9 37.73 Setting up procps (2:4.0.4-9) ...
2025-Aug-18 12:13:28.718197
#9 37.76 Setting up gpgconf (2.4.7-21+b3) ...
2025-Aug-18 12:13:28.718197
#9 37.77 Setting up libtasn1-6:arm64 (4.20.0-2) ...
2025-Aug-18 12:13:28.718197
#9 37.77 Setting up libopenjp2-7:arm64 (2.5.3-2) ...
2025-Aug-18 12:13:28.718197
#9 37.78 Setting up libx11-6:arm64 (2:1.8.12-1) ...
2025-Aug-18 12:13:28.718197
#9 37.79 Setting up libthai-data (0.1.29-2) ...
2025-Aug-18 12:13:28.718197
#9 37.80 Setting up libngtcp2-16:arm64 (1.11.0-1) ...
2025-Aug-18 12:13:28.718197
#9 37.81 Setting up libkrb5-3:arm64 (1.21.3-5) ...
2025-Aug-18 12:13:28.718197
#9 37.82 Setting up libssh2-1t64:arm64 (1.11.1-1) ...
2025-Aug-18 12:13:28.718197
#9 37.83 Setting up libtsan2:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 37.84 Setting up libbinutils:arm64 (2.44-3) ...
2025-Aug-18 12:13:28.718197
#9 37.85 Setting up dbus-system-bus-common (1.16.2-2) ...
2025-Aug-18 12:13:28.718197
#9 37.86 Creating group 'messagebus' with GID 996.
2025-Aug-18 12:13:28.718197
#9 37.86 Creating user 'messagebus' (System Message Bus) with UID 996 and GID 996.
2025-Aug-18 12:13:28.718197
#9 37.89 Setting up libisl23:arm64 (0.27-1) ...
2025-Aug-18 12:13:28.718197
#9 37.89 Setting up libc-dev-bin (2.41-12) ...
2025-Aug-18 12:13:28.718197
#9 37.90 Setting up libgpg-error-l10n (1.51-4) ...
2025-Aug-18 12:13:28.718197
#9 37.91 Setting up libdrm-common (2.4.124-2) ...
2025-Aug-18 12:13:28.718197
#9 37.92 Setting up libxcomposite1:arm64 (1:0.4.6-1) ...
2025-Aug-18 12:13:28.718197
#9 37.93 Setting up libjson-c5:arm64 (0.18+ds-1) ...
2025-Aug-18 12:13:28.718197
#9 37.94 Setting up publicsuffix (20250328.1952-0.1) ...
2025-Aug-18 12:13:28.718197
#9 37.95 Setting up libxml2:arm64 (2.12.7+dfsg+really2.9.14-2.1) ...
2025-Aug-18 12:13:28.718197
#9 37.96 Setting up libcc1-0:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 37.97 Setting up libldap2:arm64 (2.6.10+dfsg-1) ...
2025-Aug-18 12:13:28.718197
#9 37.98 Setting up dbus-bin (1.16.2-2) ...
2025-Aug-18 12:13:28.718197
#9 37.98 Setting up liblocale-gettext-perl (1.07-7+b1) ...
2025-Aug-18 12:13:28.718197
#9 37.99 Setting up liblsan0:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 38.00 Setting up libitm1:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 38.01 Setting up libkmod2:arm64 (34.2-2) ...
2025-Aug-18 12:13:28.718197
#9 38.01 Setting up libwayland-client0:arm64 (1.23.1-3) ...
2025-Aug-18 12:13:28.718197
#9 38.02 Setting up libctf0:arm64 (2.44-3) ...
2025-Aug-18 12:13:28.718197
#9 38.03 Setting up libksba8:arm64 (1.6.7-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 38.04 Setting up pinentry-curses (1.3.1-2) ...
2025-Aug-18 12:13:28.718197
#9 38.05 Setting up libxcb-dri3-0:arm64 (1.17.0-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 38.06 Setting up libllvm19:arm64 (1:19.1.7-3+b1) ...
2025-Aug-18 12:13:28.718197
#9 38.07 Setting up manpages-dev (6.9.1-1) ...
2025-Aug-18 12:13:28.718197
#9 38.08 Setting up libx11-xcb1:arm64 (2:1.8.12-1) ...
2025-Aug-18 12:13:28.718197
#9 38.08 Setting up libxdamage1:arm64 (1:1.1.6-1+b2) ...
2025-Aug-18 12:13:28.718197
#9 38.09 Setting up gpg-agent (2.4.7-21+b3) ...
2025-Aug-18 12:13:28.718197
#9 38.27 Created symlink '/etc/systemd/user/sockets.target.wants/gpg-agent-browser.socket' → '/usr/lib/systemd/user/gpg-agent-browser.socket'.
2025-Aug-18 12:13:28.718197
#9 38.42 Created symlink '/etc/systemd/user/sockets.target.wants/gpg-agent-extra.socket' → '/usr/lib/systemd/user/gpg-agent-extra.socket'.
2025-Aug-18 12:13:28.718197
#9 38.56 Created symlink '/etc/systemd/user/sockets.target.wants/gpg-agent-ssh.socket' → '/usr/lib/systemd/user/gpg-agent-ssh.socket'.
2025-Aug-18 12:13:28.718197
#9 38.70 Created symlink '/etc/systemd/user/sockets.target.wants/gpg-agent.socket' → '/usr/lib/systemd/user/gpg-agent.socket'.
2025-Aug-18 12:13:28.718197
#9 38.71 Setting up libxrender1:arm64 (1:0.9.12-1) ...
2025-Aug-18 12:13:28.718197
#9 38.72 Setting up alsa-ucm-conf (1.2.14-1) ...
2025-Aug-18 12:13:28.718197
#9 38.72 Setting up fontconfig-config (2.15.0-2.3) ...
2025-Aug-18 12:13:28.718197
#9 38.83 debconf: unable to initialize frontend: Dialog
2025-Aug-18 12:13:28.718197
#9 38.83 debconf: (TERM is not set, so the dialog frontend is not usable.)
2025-Aug-18 12:13:28.718197
#9 38.83 debconf: falling back to frontend: Readline
2025-Aug-18 12:13:28.718197
#9 38.84 debconf: unable to initialize frontend: Readline
2025-Aug-18 12:13:28.718197
#9 38.84 debconf: (This frontend requires a controlling tty.)
2025-Aug-18 12:13:28.718197
#9 38.84 debconf: falling back to frontend: Teletype
2025-Aug-18 12:13:28.718197
#9 38.85 debconf: unable to initialize frontend: Teletype
2025-Aug-18 12:13:28.718197
#9 38.85 debconf: (This frontend requires a controlling tty.)
2025-Aug-18 12:13:28.718197
#9 38.85 debconf: falling back to frontend: Noninteractive
2025-Aug-18 12:13:28.718197
#9 38.92 Setting up gpgsm (2.4.7-21+b3) ...
2025-Aug-18 12:13:28.718197
#9 38.93 Setting up libavahi-common3:arm64 (0.8-16) ...
2025-Aug-18 12:13:28.718197
#9 38.94 Setting up libxext6:arm64 (2:1.3.4-1+b3) ...
2025-Aug-18 12:13:28.718197
#9 38.95 Setting up binutils-aarch64-linux-gnu (2.44-3) ...
2025-Aug-18 12:13:28.718197
#9 38.96 Setting up libidn2-0:arm64 (2.3.8-2) ...
2025-Aug-18 12:13:28.718197
#9 38.96 Setting up libnss3:arm64 (2:3.110-1) ...
2025-Aug-18 12:13:28.718197
#9 38.97 Setting up dbus-daemon (1.16.2-2) ...
2025-Aug-18 12:13:28.718197
#9 38.99 Setting up libperl5.40:arm64 (5.40.1-6) ...
2025-Aug-18 12:13:28.718197
#9 39.00 Setting up libthai0:arm64 (0.1.29-2+b1) ...
2025-Aug-18 12:13:28.718197
#9 39.01 Setting up perl (5.40.1-6) ...
2025-Aug-18 12:13:28.718197
#9 39.03 Setting up libglib2.0-0t64:arm64 (2.84.3-1) ...
2025-Aug-18 12:13:28.718197
#9 39.05 Setting up libgprofng0:arm64 (2.44-3) ...
2025-Aug-18 12:13:28.718197
#9 39.07 Setting up libfreetype6:arm64 (2.13.3+dfsg-1) ...
2025-Aug-18 12:13:28.718197
#9 39.07 Setting up libxfixes3:arm64 (1:6.0.0-2+b4) ...
2025-Aug-18 12:13:28.718197
#9 39.08 Setting up dbus (1.16.2-2) ...
2025-Aug-18 12:13:28.718197
#9 39.12 invoke-rc.d: could not determine current runlevel
2025-Aug-18 12:13:28.718197
#9 39.13 invoke-rc.d: policy-rc.d denied execution of start.
2025-Aug-18 12:13:28.718197
#9 39.13 Setting up shared-mime-info (2.4-5+b2) ...
2025-Aug-18 12:13:28.718197
#9 40.90 Setting up libgssapi-krb5-2:arm64 (1.21.3-5) ...
2025-Aug-18 12:13:28.718197
#9 40.91 Setting up libxrandr2:arm64 (2:1.5.4-1+b3) ...
2025-Aug-18 12:13:28.718197
#9 40.91 Setting up libdpkg-perl (1.22.21) ...
2025-Aug-18 12:13:28.718197
#9 40.92 Setting up libdrm2:arm64 (2.4.124-2) ...
2025-Aug-18 12:13:28.718197
#9 40.93 Setting up cpp-14-aarch64-linux-gnu (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 40.94 Setting up libpam-systemd:arm64 (257.7-1) ...
2025-Aug-18 12:13:28.718197
#9 41.04 debconf: unable to initialize frontend: Dialog
2025-Aug-18 12:13:28.718197
#9 41.04 debconf: (TERM is not set, so the dialog frontend is not usable.)
2025-Aug-18 12:13:28.718197
#9 41.04 debconf: falling back to frontend: Readline
2025-Aug-18 12:13:28.718197
#9 41.07 debconf: unable to initialize frontend: Readline
2025-Aug-18 12:13:28.718197
#9 41.07 debconf: (This frontend requires a controlling tty.)
2025-Aug-18 12:13:28.718197
#9 41.07 debconf: falling back to frontend: Teletype
2025-Aug-18 12:13:28.718197
#9 41.07 debconf: unable to initialize frontend: Teletype
2025-Aug-18 12:13:28.718197
#9 41.08 debconf: (This frontend requires a controlling tty.)
2025-Aug-18 12:13:28.718197
#9 41.08 debconf: falling back to frontend: Noninteractive
2025-Aug-18 12:13:28.718197
#9 41.18 Setting up libc6-dev:arm64 (2.41-12) ...
2025-Aug-18 12:13:28.718197
#9 41.19 Setting up libharfbuzz0b:arm64 (10.2.0-1+b1) ...
2025-Aug-18 12:13:28.718197
#9 41.20 Setting up libfontconfig1:arm64 (2.15.0-2.3) ...
2025-Aug-18 12:13:28.718197
#9 41.21 Setting up libgcc-14-dev:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 41.22 Setting up libstdc++-14-dev:arm64 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 41.22 Setting up libavahi-client3:arm64 (0.8-16) ...
2025-Aug-18 12:13:28.718197
#9 41.23 Setting up gpg (2.4.7-21+b3) ...
2025-Aug-18 12:13:28.718197
#9 41.37 Created symlink '/etc/systemd/user/sockets.target.wants/keyboxd.socket' → '/usr/lib/systemd/user/keyboxd.socket'.
2025-Aug-18 12:13:28.718197
#9 41.38 Setting up gnupg-utils (2.4.7-21+b3) ...
2025-Aug-18 12:13:28.718197
#9 41.38 Setting up libdrm-amdgpu1:arm64 (2.4.124-2) ...
2025-Aug-18 12:13:28.718197
#9 41.39 Setting up libgnutls30t64:arm64 (3.8.9-3) ...
2025-Aug-18 12:13:28.718197
#9 41.39 Setting up mesa-libgallium:arm64 (25.0.7-2) ...
2025-Aug-18 12:13:28.718197
#9 41.40 Setting up fontconfig (2.15.0-2.3) ...
2025-Aug-18 12:13:28.718197
#9 41.41 Regenerating fonts cache... done.
2025-Aug-18 12:13:28.718197
#9 43.48 Setting up libatk1.0-0t64:arm64 (2.56.2-1) ...
2025-Aug-18 12:13:28.718197
#9 43.49 Setting up libxi6:arm64 (2:1.8.2-1) ...
2025-Aug-18 12:13:28.718197
#9 43.49 Setting up libgbm1:arm64 (25.0.7-2) ...
2025-Aug-18 12:13:28.718197
#9 43.50 Setting up libfile-fcntllock-perl (0.22-4+b4) ...
2025-Aug-18 12:13:28.718197
#9 43.51 Setting up libalgorithm-diff-perl (1.201-1) ...
2025-Aug-18 12:13:28.718197
#9 43.51 Setting up libxtst6:arm64 (2:1.2.5-1) ...
2025-Aug-18 12:13:28.718197
#9 43.52 Setting up libxcursor1:arm64 (1:1.2.3-1) ...
2025-Aug-18 12:13:28.718197
#9 43.53 Setting up libpango-1.0-0:arm64 (1.56.3-1) ...
2025-Aug-18 12:13:28.718197
#9 43.54 Setting up libpsl5t64:arm64 (0.21.2-1.1+b1) ...
2025-Aug-18 12:13:28.718197
#9 43.55 Setting up binutils (2.44-3) ...
2025-Aug-18 12:13:28.718197
#9 43.55 Setting up libcairo2:arm64 (1.18.4-1+b1) ...
2025-Aug-18 12:13:28.718197
#9 43.56 Setting up dpkg-dev (1.22.21) ...
2025-Aug-18 12:13:28.718197
#9 43.57 Setting up libdconf1:arm64 (0.40.0-5) ...
2025-Aug-18 12:13:28.718197
#9 43.58 Setting up dirmngr (2.4.7-21+b3) ...
2025-Aug-18 12:13:28.718197
#9 43.73 Created symlink '/etc/systemd/user/sockets.target.wants/dirmngr.socket' → '/usr/lib/systemd/user/dirmngr.socket'.
2025-Aug-18 12:13:28.718197
#9 43.75 Setting up dbus-user-session (1.16.2-2) ...
2025-Aug-18 12:13:28.718197
#9 43.76 Setting up cpp-aarch64-linux-gnu (4:14.2.0-1) ...
2025-Aug-18 12:13:28.718197
#9 43.77 Setting up libegl-mesa0:arm64 (25.0.7-2) ...
2025-Aug-18 12:13:28.718197
#9 43.78 Setting up librtmp1:arm64 (2.4+20151223.gitfa8646d.1-2+b5) ...
2025-Aug-18 12:13:28.718197
#9 43.79 Setting up libatspi2.0-0t64:arm64 (2.56.2-1) ...
2025-Aug-18 12:13:28.718197
#9 43.80 Setting up cpp-14 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 43.81 Setting up cpp (4:14.2.0-1) ...
2025-Aug-18 12:13:28.718197
#9 43.83 Setting up libegl1:arm64 (1.7.0-1+b2) ...
2025-Aug-18 12:13:28.718197
#9 43.85 Setting up gnupg (2.4.7-21) ...
2025-Aug-18 12:13:28.718197
#9 43.85 Setting up libgpgme11t64:arm64 (1.24.2-3) ...
2025-Aug-18 12:13:28.718197
#9 43.86 Setting up gcc-14-aarch64-linux-gnu (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 43.87 Setting up libpangoft2-1.0-0:arm64 (1.56.3-1) ...
2025-Aug-18 12:13:28.718197
#9 43.88 Setting up libalgorithm-diff-xs-perl (0.04-9) ...
2025-Aug-18 12:13:28.718197
#9 43.88 Setting up libcups2t64:arm64 (2.4.10-3) ...
2025-Aug-18 12:13:28.718197
#9 43.89 Setting up libngtcp2-crypto-gnutls8:arm64 (1.11.0-1) ...
2025-Aug-18 12:13:28.718197
#9 43.90 Setting up libpangocairo-1.0-0:arm64 (1.56.3-1) ...
2025-Aug-18 12:13:28.718197
#9 43.91 Setting up libalgorithm-merge-perl (0.08-5) ...
2025-Aug-18 12:13:28.718197
#9 43.92 Setting up libatk-bridge2.0-0t64:arm64 (2.56.2-1) ...
2025-Aug-18 12:13:28.718197
#9 43.93 Setting up gcc-aarch64-linux-gnu (4:14.2.0-1) ...
2025-Aug-18 12:13:28.718197
#9 43.94 Setting up libgpgmepp6t64:arm64 (1.24.2-3) ...
2025-Aug-18 12:13:28.718197
#9 43.95 Setting up g++-14-aarch64-linux-gnu (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 43.96 Setting up wget (1.25.0-2) ...
2025-Aug-18 12:13:28.718197
#9 43.97 Setting up gpg-wks-client (2.4.7-21+b3) ...
2025-Aug-18 12:13:28.718197
#9 43.98 Setting up libcurl3t64-gnutls:arm64 (8.14.1-2) ...
2025-Aug-18 12:13:28.718197
#9 43.98 Setting up dconf-service (0.40.0-5) ...
2025-Aug-18 12:13:28.718197
#9 43.99 Setting up gcc-14 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 44.00 Setting up g++-aarch64-linux-gnu (4:14.2.0-1) ...
2025-Aug-18 12:13:28.718197
#9 44.01 Setting up g++-14 (14.2.0-19) ...
2025-Aug-18 12:13:28.718197
#9 44.02 Setting up libpoppler147:arm64 (25.03.0-5) ...
2025-Aug-18 12:13:28.718197
#9 44.03 Setting up dconf-gsettings-backend:arm64 (0.40.0-5) ...
2025-Aug-18 12:13:28.718197
#9 44.03 Setting up gcc (4:14.2.0-1) ...
2025-Aug-18 12:13:28.718197
#9 44.05 Setting up libpoppler-cpp2:arm64 (25.03.0-5) ...
2025-Aug-18 12:13:28.718197
#9 44.07 Setting up g++ (4:14.2.0-1) ...
2025-Aug-18 12:13:28.718197
#9 44.08 update-alternatives: using /usr/bin/g++ to provide /usr/bin/c++ (c++) in auto mode
2025-Aug-18 12:13:28.718197
#9 44.08 Setting up build-essential (12.12) ...
2025-Aug-18 12:13:28.718197
#9 44.09 Setting up poppler-utils (25.03.0-5) ...
2025-Aug-18 12:13:28.718197
#9 44.09 Setting up libpoppler-dev:arm64 (25.03.0-5) ...
2025-Aug-18 12:13:28.718197
#9 44.11 Setting up gsettings-desktop-schemas (48.0-1) ...
2025-Aug-18 12:13:28.718197
#9 44.12 Setting up libpoppler-cpp-dev:arm64 (25.03.0-5) ...
2025-Aug-18 12:13:28.718197
#9 44.13 Setting up at-spi2-core (2.56.2-1) ...
2025-Aug-18 12:13:28.718197
#9 44.16 Setting up dmsetup (2:1.02.205-2) ...
2025-Aug-18 12:13:28.718197
#9 44.17 Setting up libdevmapper1.02.1:arm64 (2:1.02.205-2) ...
2025-Aug-18 12:13:28.718197
#9 44.18 Setting up libcryptsetup12:arm64 (2:2.7.5-2) ...
2025-Aug-18 12:13:28.718197
#9 44.19 Setting up systemd-cryptsetup (257.7-1) ...
2025-Aug-18 12:13:28.718197
#9 44.20 Processing triggers for libc-bin (2.41-12) ...
2025-Aug-18 12:13:28.718197
#9 44.30 Processing triggers for systemd (257.7-1) ...
2025-Aug-18 12:13:28.718197
#9 DONE 44.5s
2025-Aug-18 12:13:28.718197
2025-Aug-18 12:13:28.718197
#10 [stage-0 4/9] WORKDIR /app
2025-Aug-18 12:13:28.718197
#10 DONE 0.0s
2025-Aug-18 12:13:28.718197
2025-Aug-18 12:13:28.718197
#11 [stage-0 5/9] COPY pyproject.toml uv.lock* ./
2025-Aug-18 12:13:28.718197
#11 DONE 0.1s
2025-Aug-18 12:13:28.718197
2025-Aug-18 12:13:28.718197
#12 [stage-0 6/9] RUN uv sync --frozen --no-cache
2025-Aug-18 12:13:28.718197
#12 0.460 Using CPython 3.12.11 interpreter at: /usr/local/bin/python3
2025-Aug-18 12:13:28.718197
#12 0.461 Creating virtual environment at: .venv
2025-Aug-18 12:13:28.718197
#12 0.513    Building telekomrbudownloader @ file:///app
2025-Aug-18 12:13:28.718197
#12 0.570 Downloading pillow (4.3MiB)
2025-Aug-18 12:13:28.718197
#12 0.586 Downloading cryptography (4.0MiB)
2025-Aug-18 12:13:28.718197
#12 0.589 Downloading pdfminer-six (5.4MiB)
2025-Aug-18 12:13:28.718197
#12 0.593 Downloading playwright (42.5MiB)
2025-Aug-18 12:13:28.718197
#12 0.596 Downloading numpy (13.4MiB)
2025-Aug-18 12:13:28.718197
#12 0.597 Downloading pandas (10.7MiB)
2025-Aug-18 12:13:28.718197
#12 0.598 Downloading pypdfium2 (2.7MiB)
2025-Aug-18 12:13:28.718197
#12 0.599 Downloading uvloop (4.4MiB)
2025-Aug-18 12:13:28.718197
#12 0.600 Downloading pydantic-core (1.8MiB)
2025-Aug-18 12:13:28.718197
#12 1.231  Downloading pydantic-core
2025-Aug-18 12:13:28.718197
#12 1.425  Downloading pypdfium2
2025-Aug-18 12:13:28.718197
#12 1.825  Downloading pdfminer-six
2025-Aug-18 12:13:28.718197
#12 1.859  Downloading pillow
2025-Aug-18 12:13:28.718197
#12 1.876  Downloading uvloop
2025-Aug-18 12:13:28.718197
#12 1.939  Downloading cryptography
2025-Aug-18 12:13:28.718197
#12 4.166  Downloading playwright
2025-Aug-18 12:13:28.718197
#12 4.564  Downloading numpy
2025-Aug-18 12:13:28.718197
#12 4.801  Downloading pandas
2025-Aug-18 12:13:28.718197
#12 5.654       Built telekomrbudownloader @ file:///app
2025-Aug-18 12:13:28.718197
#12 5.661 Prepared 45 packages in 5.16s
2025-Aug-18 12:13:28.718197
#12 5.806 Installed 45 packages in 146ms
2025-Aug-18 12:13:28.718197
#12 5.807  + annotated-types==0.7.0
2025-Aug-18 12:13:28.718197
#12 5.808  + anyio==4.9.0
2025-Aug-18 12:13:28.718197
#12 5.808  + certifi==2025.4.26
2025-Aug-18 12:13:28.718197
#12 5.810  + cffi==1.17.1
2025-Aug-18 12:13:28.718197
#12 5.810  + charset-normalizer==3.4.2
2025-Aug-18 12:13:28.718197
#12 5.810  + click==8.2.1
2025-Aug-18 12:13:28.718197
#12 5.810  + cryptography==45.0.4
2025-Aug-18 12:13:28.718197
#12 5.810  + et-xmlfile==2.0.0
2025-Aug-18 12:13:28.718197
#12 5.810  + fastapi==0.115.12
2025-Aug-18 12:13:28.718197
#12 5.810  + greenlet==3.2.3
2025-Aug-18 12:13:28.718197
#12 5.810  + h11==0.16.0
2025-Aug-18 12:13:28.718197
#12 5.810  + httptools==0.6.4
2025-Aug-18 12:13:28.718197
#12 5.810  + idna==3.10
2025-Aug-18 12:13:28.718197
#12 5.810  + numpy==2.2.6
2025-Aug-18 12:13:28.718197
#12 5.810  + openpyxl==3.1.5
2025-Aug-18 12:13:28.718197
#12 5.810  + pandas==2.3.0
2025-Aug-18 12:13:28.718197
#12 5.810  + pdfminer-six==20250327
2025-Aug-18 12:13:28.718197
#12 5.810  + pdfplumber==0.11.6
2025-Aug-18 12:13:28.718197
#12 5.810  + pillow==11.2.1
2025-Aug-18 12:13:28.718197
#12 5.810  + playwright==1.52.0
2025-Aug-18 12:13:28.718197
#12 5.810  + psutil==7.0.0
2025-Aug-18 12:13:28.718197
#12 5.810  + pycparser==2.22
2025-Aug-18 12:13:28.718197
#12 5.810  + pydantic==2.11.5
2025-Aug-18 12:13:28.718197
#12 5.810  + pydantic-core==2.33.2
2025-Aug-18 12:13:28.718197
#12 5.810  + pydantic-settings==2.9.1
2025-Aug-18 12:13:28.718197
#12 5.810  + pyee==13.0.0
2025-Aug-18 12:13:28.718197
#12 5.810  + pypdfium2==4.30.1
2025-Aug-18 12:13:28.718197
#12 5.810  + python-dateutil==2.9.0.post0
2025-Aug-18 12:13:28.718197
#12 5.810  + python-dotenv==1.1.0
2025-Aug-18 12:13:28.718197
#12 5.810  + python-multipart==0.0.20
2025-Aug-18 12:13:28.718197
#12 5.810  + pytz==2025.2
2025-Aug-18 12:13:28.718197
#12 5.810  + pyyaml==6.0.2
2025-Aug-18 12:13:28.718197
#12 5.810  + requests==2.32.4
2025-Aug-18 12:13:28.718197
#12 5.810  + six==1.17.0
2025-Aug-18 12:13:28.718197
#12 5.810  + sniffio==1.3.1
2025-Aug-18 12:13:28.718197
#12 5.810  + starlette==0.46.2
2025-Aug-18 12:13:28.718197
#12 5.810  + telekomrbudownloader==0.1.0 (from file:///app)
2025-Aug-18 12:13:28.718197
#12 5.810  + typing-extensions==4.14.0
2025-Aug-18 12:13:28.718197
#12 5.810  + typing-inspection==0.4.1
2025-Aug-18 12:13:28.718197
#12 5.810  + tzdata==2025.2
2025-Aug-18 12:13:28.718197
#12 5.810  + urllib3==2.4.0
2025-Aug-18 12:13:28.718197
#12 5.810  + uvicorn==0.34.3
2025-Aug-18 12:13:28.718197
#12 5.810  + uvloop==0.21.0
2025-Aug-18 12:13:28.718197
#12 5.810  + watchfiles==1.0.5
2025-Aug-18 12:13:28.718197
#12 5.810  + websockets==15.0.1
2025-Aug-18 12:13:28.718197
#12 DONE 6.0s
2025-Aug-18 12:13:28.718197
2025-Aug-18 12:13:28.718197
#13 [stage-0 7/9] RUN uv run playwright install --with-deps
2025-Aug-18 12:13:28.718197
#13 0.753 BEWARE: your OS is not officially supported by Playwright; installing dependencies for ubuntu20.04-arm64 as a fallback.
2025-Aug-18 12:13:28.718197
#13 0.754 Installing dependencies...
2025-Aug-18 12:13:28.718197
#13 0.841 Get:1 http://deb.debian.org/debian trixie InRelease [138 kB]
2025-Aug-18 12:13:28.718197
#13 0.865 Get:2 http://deb.debian.org/debian trixie-updates InRelease [47.1 kB]
2025-Aug-18 12:13:28.718197
#13 0.867 Get:3 http://deb.debian.org/debian-security trixie-security InRelease [43.4 kB]
2025-Aug-18 12:13:28.718197
#13 0.905 Get:4 http://deb.debian.org/debian trixie/main arm64 Packages [9604 kB]
2025-Aug-18 12:13:28.718197
#13 0.995 Get:5 http://deb.debian.org/debian trixie-updates/main arm64 Packages [2432 B]
2025-Aug-18 12:13:28.718197
#13 1.000 Get:6 http://deb.debian.org/debian-security trixie-security/main arm64 Packages [9464 B]
2025-Aug-18 12:13:28.718197
#13 2.015 Fetched 9844 kB in 1s (8288 kB/s)
2025-Aug-18 12:13:28.718197
#13 2.015 Reading package lists...
2025-Aug-18 12:13:28.718197
#13 2.765 Reading package lists...
2025-Aug-18 12:13:28.718197
#13 3.517 Building dependency tree...
2025-Aug-18 12:13:28.718197
#13 3.796 Reading state information...
2025-Aug-18 12:13:28.718197
#13 3.843 Package ttf-ubuntu-font-family is not available, but is referred to by another package.
2025-Aug-18 12:13:28.718197
#13 3.843 This may mean that the package is missing, has been obsoleted, or
2025-Aug-18 12:13:28.718197
#13 3.843 is only available from another source
2025-Aug-18 12:13:28.718197
#13 3.843
2025-Aug-18 12:13:28.718197
#13 3.843 Package libgdk-pixbuf2.0-0 is not available, but is referred to by another package.
2025-Aug-18 12:13:28.718197
#13 3.843 This may mean that the package is missing, has been obsoleted, or
2025-Aug-18 12:13:28.718197
#13 3.843 is only available from another source
2025-Aug-18 12:13:28.718197
#13 3.843 However the following packages replace it:
2025-Aug-18 12:13:28.718197
#13 3.843   libgdk-pixbuf-xlib-2.0-0
2025-Aug-18 12:13:28.718197
#13 3.843
2025-Aug-18 12:13:28.718197
#13 3.843 Package libjpeg-turbo8 is not available, but is referred to by another package.
2025-Aug-18 12:13:28.718197
#13 3.843 This may mean that the package is missing, has been obsoleted, or
2025-Aug-18 12:13:28.718197
#13 3.843 is only available from another source
2025-Aug-18 12:13:28.718197
#13 3.843
2025-Aug-18 12:13:28.718197
#13 3.843 Package ttf-unifont is not available, but is referred to by another package.
2025-Aug-18 12:13:28.718197
#13 3.843 This may mean that the package is missing, has been obsoleted, or
2025-Aug-18 12:13:28.718197
#13 3.843 is only available from another source
2025-Aug-18 12:13:28.718197
#13 3.843 However the following packages replace it:
2025-Aug-18 12:13:28.718197
#13 3.843   fonts-unifont
2025-Aug-18 12:13:28.718197
#13 3.843
2025-Aug-18 12:13:28.718197
#13 3.847 E: Package 'libgdk-pixbuf2.0-0' has no installation candidate
2025-Aug-18 12:13:28.718197
#13 3.847 E: Unable to locate package libx264-155
2025-Aug-18 12:13:28.718197
#13 3.847 E: Unable to locate package libenchant1c2a
2025-Aug-18 12:13:28.718197
#13 3.847 E: Unable to locate package libicu66
2025-Aug-18 12:13:28.718197
#13 3.847 E: Package 'libjpeg-turbo8' has no installation candidate
2025-Aug-18 12:13:28.718197
#13 3.847 E: Unable to locate package libvpx6
2025-Aug-18 12:13:28.718197
#13 3.847 E: Unable to locate package libwebp6
2025-Aug-18 12:13:28.718197
#13 3.847 E: Package 'ttf-unifont' has no installation candidate
2025-Aug-18 12:13:28.718197
#13 3.847 E: Package 'ttf-ubuntu-font-family' has no installation candidate
2025-Aug-18 12:13:28.718197
#13 3.851 Failed to install browsers
2025-Aug-18 12:13:28.718197
#13 3.851 Error: Installation process exited with code: 100
2025-Aug-18 12:13:28.718197
#13 ERROR: process "/bin/sh -c uv run playwright install --with-deps" did not complete successfully: exit code: 1
2025-Aug-18 12:13:28.718197
------
2025-Aug-18 12:13:28.718197
> [stage-0 7/9] RUN uv run playwright install --with-deps:
2025-Aug-18 12:13:28.718197
3.847 E: Unable to locate package libx264-155
2025-Aug-18 12:13:28.718197
3.847 E: Unable to locate package libenchant1c2a
2025-Aug-18 12:13:28.718197
3.847 E: Unable to locate package libicu66
2025-Aug-18 12:13:28.718197
3.847 E: Package 'libjpeg-turbo8' has no installation candidate
2025-Aug-18 12:13:28.718197
3.847 E: Unable to locate package libvpx6
2025-Aug-18 12:13:28.718197
3.847 E: Unable to locate package libwebp6
2025-Aug-18 12:13:28.718197
3.847 E: Package 'ttf-unifont' has no installation candidate
2025-Aug-18 12:13:28.718197
3.847 E: Package 'ttf-ubuntu-font-family' has no installation candidate
2025-Aug-18 12:13:28.718197
3.851 Failed to install browsers
2025-Aug-18 12:13:28.718197
3.851 Error: Installation process exited with code: 100
2025-Aug-18 12:13:28.718197
------
2025-Aug-18 12:13:28.718197
Dockerfile:30
2025-Aug-18 12:13:28.718197
--------------------
2025-Aug-18 12:13:28.718197
28 |     # Install dependencies with uv
2025-Aug-18 12:13:28.718197
29 |     RUN uv sync --frozen --no-cache
2025-Aug-18 12:13:28.718197
30 | >>> RUN uv run playwright install --with-deps
2025-Aug-18 12:13:28.718197
31 |
2025-Aug-18 12:13:28.718197
32 |     # Create data directory for PDFs
2025-Aug-18 12:13:28.718197
--------------------
2025-Aug-18 12:13:28.718197
ERROR: failed to solve: process "/bin/sh -c uv run playwright install --with-deps" did not complete successfully: exit code: 1
2025-Aug-18 12:13:28.718197
exit status 1
2025-Aug-18 12:13:28.740635
Deployment failed. Removing the new version of your application.
2025-Aug-18 12:13:29.222051
Gracefully shutting down build container: pcwwc0w8gc0ck4gsgsk0cg4o
2025-Aug-18 12:13:29.355371
[CMD]: docker stop --time=30 pcwwc0w8gc0ck4gsgsk0cg4o
2025-Aug-18 12:13:29.355371
Flag --time has been deprecated, use --timeout instead
2025-Aug-18 12:13:29.493762
pcwwc0w8gc0ck4gsgsk0cg4o
2025-Aug-18 12:13:29.658136
[CMD]: docker rm -f pcwwc0w8gc0ck4gsgsk0cg4o
2025-Aug-18 12:13:29.658136
Error response from daemon: No such container: pcwwc0w8gc0ck4gsgsk0cg4o