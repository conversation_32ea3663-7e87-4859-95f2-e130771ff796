# Telekom RBU Downloader

A FastAPI-Playwright microservice for downloading Telekom RBU sheets as PDFs.

## Features

### PDF Download Service
- Receives a `sheetId` via HTTP POST
- Attempts to click the export link (falls back to login if needed)
- Downloads the PDF
- Rotates old PDFs, keeping only the latest 10
- Returns the PDF directly to the caller (n8n)

### PDF Data Extraction Service
- Accepts uploaded PDF files via HTTP POST
- Extracts structured data from German Telekom invoices
- Returns JSON with extracted fields (order numbers, dates, amounts, etc.)
- Perfect for automated invoice processing workflows

## Prerequisites

- [uv](https://docs.astral.sh/uv/) - Fast Python package manager
- Python 3.10+

## Setup

1. Copy `.env.example` to `.env` and fill in your Telekom credentials:
   ```bash
   cp .env.example .env
   ```

2. Install dependencies:
   ```bash
   uv sync
   uv run playwright install --with-deps
   ```

3. Run the service:
   ```bash
   # Development (with auto-reload)
   uv run uvicorn app.main:app --host 0.0.0.0 --port 8080 --reload

   # Production
   uv run uvicorn app.main:app --host 0.0.0.0 --port 8080
   ```

## Docker

Build and run with Docker:

```bash
# Build the image (includes Playwright + PDF processing dependencies)
docker build -t telekom-rbu-downloader .

# Run with environment file
docker run -p 8080:8080 --env-file .env telekom-rbu-downloader

# Or run with environment variables directly
docker run -p 8080:8080 \
  -e USERNAME=your_telekom_user \
  -e PASSWORD=your_telekom_pass \
  telekom-rbu-downloader

# Run in background (detached mode)
docker run -d -p 8080:8080 --env-file .env --name rbu-downloader telekom-rbu-downloader

# Run with volume for persistent PDF storage (optional)
docker run -d -p 8080:8080 --env-file .env \
  -v $(pwd)/data/pdfs:/data/pdfs \
  --name rbu-downloader telekom-rbu-downloader
```

**Important:**
- Use the `-p 8080:8080` flag to map the container's port 8080 to your host's port 8080
- The Docker image includes all dependencies for both PDF download and extraction
- Optionally mount a volume for persistent PDF storage

## Development

### Available Commands

- `uv run uvicorn app.main:app --host 0.0.0.0 --port 8080 --reload` - Development server
- `uv run uvicorn app.main:app --host 0.0.0.0 --port 8080` - Production server
- `uv run python test_api.py` - Test the API endpoints

### Adding Dependencies

```bash
# Add runtime dependency
uv add package-name

# Add development dependency
uv add --dev package-name
```

## Process Management & Zombie Prevention

This service includes robust process management to prevent zombie browser processes:

### Features
- **Signal Handlers**: Proper SIGTERM, SIGINT, and SIGCHLD handling
- **Browser Cleanup**: Guaranteed browser process cleanup with try/finally blocks
- **Concurrency Limits**: Maximum 3 concurrent browser operations to prevent resource exhaustion
- **Health Monitoring**: `/health` endpoint monitors zombie and browser processes
- **Graceful Shutdown**: Proper application lifecycle management

### Health Monitoring

Check application health and process status:
```bash
curl http://localhost:8080/health
```

Response includes:
- Zombie process count
- Active headless_shell process count
- Available browser concurrency slots

### Testing Process Management

Run the zombie process test:
```bash
uv run python test_zombie_fix.py
```

This test monitors process counts before/after concurrent operations.

## API Usage

POST to `/export` with a `sheet_id` parameter:

### Option 1: Save with custom filename
```bash
curl -X POST "http://localhost:8080/export" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "sheet_id=YOUR_SHEET_ID" \
     --output sheet.pdf
```

### Option 2: Keep original filename from web page
```bash
curl -X POST "http://localhost:8080/export" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "sheet_id=YOUR_SHEET_ID" \
     -O -J
```

The `-O -J` flags tell curl to use the original filename from the server's `Content-Disposition` header.

The service will return the PDF file directly with the original filename from the Telekom portal (e.g., `rbu_19569962_0010624516.pdf`).

### PDF Data Extraction

Upload a PDF file to extract structured data:

```bash
curl -X POST "http://localhost:8080/extract" \
     -F "file=@invoice.pdf"
```

**Response Example:**
```json
{
  "bestell_nr": "4213235521/00010",
  "leistungsort": "37242 Bad Sooden-All",
  "startdatum": "01.01.2025",
  "slutdatum": "31.03.2025",
  "leb_erfassung_datum": "09.06.2025",
  "summa_netto_eur": 1300.0,
  "leb_nr": "0010624394",
  "ag_leb_nr": "1209021553",
  "steuernummer": "1129713991",
  "extraction_timestamp": "2025-06-12T09:28:27.727131",
  "success": true,
  "error": null
}
```

**Extracted Fields:**
- `bestell_nr`: Order number
- `startdatum`: Service period start date
- `slutdatum`: Service period end date
- `leb_erfassung_datum`: LEB recording date
- `summa_netto_eur`: Net amount in EUR
- `leb_nr`: LEB number
- `ag_leb_nr`: AG LEB number
- `steuernummer`: Tax number
- `extraction_timestamp`: When extraction was performed
- `success`: Whether extraction succeeded
- `error`: Error message if extraction failed