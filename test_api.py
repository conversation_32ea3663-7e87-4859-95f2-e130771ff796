#!/usr/bin/env python3
"""
Simple test script to verify the API is working.
Run this after starting the server with: uv run uvicorn app.main:app --host 0.0.0.0 --port 8080 --reload
"""

import requests
import sys

def test_api():
    base_url = "http://localhost:8080"
    
    # Test root endpoint
    try:
        response = requests.get(f"{base_url}/")
        print(f"Root endpoint status: {response.status_code}")
        print(f"Root response: {response.json()}")
    except Exception as e:
        print(f"Error testing root endpoint: {e}")
        return False
    
    # Test export endpoint (this will likely fail without proper credentials/setup)
    try:
        response = requests.post(f"{base_url}/export", data={"sheet_id": "0010624516"})
        print(f"Export endpoint status: {response.status_code}")
        if response.status_code == 200:
            print("Export successful - PDF downloaded")
        else:
            print(f"Export response: {response.text}")
    except Exception as e:
        print(f"Error testing export endpoint: {e}")
    
    return True

if __name__ == "__main__":
    if test_api():
        print("API tests completed")
    else:
        print("API tests failed")
        sys.exit(1)
