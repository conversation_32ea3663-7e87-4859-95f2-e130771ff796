#!/usr/bin/env python3
"""
Test script to verify zombie process fixes are working.
This script tests the health endpoint and monitors process counts.
"""

import requests
import time
import subprocess
import sys

def get_process_count(process_name):
    """Get count of processes matching the given name."""
    try:
        result = subprocess.run(['pgrep', '-c', process_name], 
                              capture_output=True, text=True)
        return int(result.stdout.strip()) if result.returncode == 0 else 0
    except:
        return 0

def get_zombie_count():
    """Get count of zombie processes."""
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        zombie_count = result.stdout.count('<defunct>')
        return zombie_count
    except:
        return 0

def test_health_endpoint():
    """Test the health endpoint."""
    try:
        response = requests.get("http://localhost:8080/health")
        if response.status_code == 200:
            data = response.json()
            print(f"Health check: {data}")
            return data
        else:
            print(f"Health endpoint failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"Error calling health endpoint: {e}")
        return None

def test_concurrent_exports():
    """Test multiple concurrent export requests."""
    import threading
    import time
    
    def make_export_request(sheet_id):
        try:
            response = requests.post("http://localhost:8080/export", 
                                   data={"sheet_id": sheet_id}, 
                                   timeout=30)
            print(f"Export {sheet_id}: {response.status_code}")
            return response.status_code
        except Exception as e:
            print(f"Export {sheet_id} failed: {e}")
            return 500
    
    # Test with multiple concurrent requests
    sheet_ids = ["**********", "**********", "**********"]
    threads = []
    
    print("Starting concurrent export tests...")
    start_time = time.time()
    
    for sheet_id in sheet_ids:
        thread = threading.Thread(target=make_export_request, args=(sheet_id,))
        threads.append(thread)
        thread.start()
        time.sleep(0.5)  # Stagger requests slightly
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    print(f"Concurrent tests completed in {end_time - start_time:.2f} seconds")

def main():
    """Main test function."""
    print("=== Zombie Process Fix Test ===")
    
    # Initial health check
    print("\n1. Initial health check:")
    initial_health = test_health_endpoint()
    
    if not initial_health:
        print("Health endpoint not available. Is the server running?")
        return False
    
    # Get initial process counts
    initial_headless = get_process_count("headless_shell")
    initial_zombies = get_zombie_count()
    
    print(f"\nInitial process counts:")
    print(f"  Headless shell processes: {initial_headless}")
    print(f"  Zombie processes: {initial_zombies}")
    
    # Test concurrent exports (this will likely fail without credentials, but should test process management)
    print("\n2. Testing concurrent exports:")
    test_concurrent_exports()
    
    # Wait a bit for processes to settle
    print("\n3. Waiting for processes to settle...")
    time.sleep(5)
    
    # Final health check
    print("\n4. Final health check:")
    final_health = test_health_endpoint()
    
    # Get final process counts
    final_headless = get_process_count("headless_shell")
    final_zombies = get_zombie_count()
    
    print(f"\nFinal process counts:")
    print(f"  Headless shell processes: {final_headless}")
    print(f"  Zombie processes: {final_zombies}")
    
    # Analysis
    print(f"\n=== Analysis ===")
    print(f"Headless shell change: {final_headless - initial_headless}")
    print(f"Zombie process change: {final_zombies - initial_zombies}")
    
    if final_zombies <= initial_zombies:
        print("✅ No increase in zombie processes - fix appears to be working!")
    else:
        print("❌ Zombie processes increased - may need additional fixes")
    
    return True

if __name__ == "__main__":
    if main():
        print("\nTest completed successfully")
    else:
        print("\nTest failed")
        sys.exit(1)
