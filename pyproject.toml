[project]
name = "telekomrbudownloader"
version = "0.1.0"
description = "FastAPI-Playwright microservice for downloading Telekom RBU sheets as PDFs"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "playwright>=1.40.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.0.0",
    "python-multipart>=0.0.6",
    "pandas>=2.3.0",
    "pdfplumber>=0.11.6",
    "openpyxl>=3.0.0",
    "psutil>=5.9.0",
]

# Note: For uv scripts, we'll use uv run commands directly instead of entry points

[tool.setuptools.packages.find]
include = ["app*"]

[tool.uv]
dev-dependencies = [
    "requests>=2.31.0",
]
package = true
