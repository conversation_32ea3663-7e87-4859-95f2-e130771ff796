FROM python:3.12-slim

# Telekom RBU Downloader - FastAPI service with Playwright automation and PDF extraction
# Features: PDF download from Telekom portal + PDF data extraction

# Install uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /usr/local/bin/

# System deps for Playwright and PDF processing
RUN apt-get update && apt-get install -y \
    # Playwright dependencies
    wget gnupg libnss3 libatk1.0-0 libatk-bridge2.0-0 libx11-xcb1 \
    libxcomposite1 libxcursor1 libxdamage1 libxrandr2 libgbm1 libasound2 \
    libpangocairo-1.0-0 libpango-1.0-0 libcups2 libdrm2 libwayland-client0 \
    libwayland-server0 libxshmfence1 libegl1 \
    # PDF processing dependencies
    libpoppler-cpp-dev libpoppler-dev poppler-utils \
    # General utilities
    build-essential \
 && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy project files
COPY pyproject.toml uv.lock* ./

# Install dependencies with uv
RUN uv sync --frozen --no-cache
RUN uv run playwright install --with-deps

# Create data directory for PDFs
RUN mkdir -p /data/pdfs

# Copy application code
COPY . .

# Expose port 8080
EXPOSE 8080

# Use uv to run the application
CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080"]
